const { convertPathQuery } = require("@/services/routerManager")
const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const supportDomainList = require("@/config/webviewSupportDomain")
const { parseUrl, previewFile } = require("@/utils/file")

Page({
  data: {
    url: "",
    id: "",
    filePath: "",
    fileName: "",
    isNotSupportUrl: "", // 不支持链接的url
    isUnViewText: false, // 是否为不可预览文件类型
    shareData: {},
  },

  onLoad(options) {
    this.options = options
    if (options.url) {
      const url = decodeURIComponent(options.url)
      this.setData({ id: options.id })
      this.getOptList(url)
      console.log(url)
      // 如果是小程序资讯，获取分享信息
      if (this.data.id) {
        this.getShareInfo()
      }
    }
  },

  /**
   * 获取操作列表并处理 URL
   * @param {string} url - 解码后的 URL
   */
  getOptList(url) {
    const { baseUrl, queryParams } = parseUrl(url)
    const key = this.findUnitKeyByBaseUrl(supportDomainList, baseUrl)
    if (["courseDetail", "advclassDetail", "questionsetDetail"].includes(key)) {
      this.handleDetailRedirect(key, queryParams)
    } else if (key === "download" && url.indexOf(".html") <= 0) {
      this.handlePreviewFile(url)
    } else if (key === "webview") {
      console.log("包含")
      this.handleConfigOrWebView(key, url)
    } else {
      this.setData({
        isNotSupportUrl: url,
      })
    }
  },

  // 处理预览文件
  handlePreviewFile(url) {
    previewFile(url)
      .then((res) => {
        setTimeout(() => {
          wx.navigateBack({ delta: 1 })
        }, 1000)
      })
      .catch((res) => {
        // 不支持的文件类型
        this.setData({
          filePath: res?.filePath,
          fileName: res?.fileName,
          isUnViewText: true,
        })
      })
  },
  /**
   * 根据基础 URL 查找对应的单元键
   * @param {Array} optList - 配置列表
   * @param {string} baseUrl - 基础 URL
   * @returns {string} 对应的单元键
   */
  findUnitKeyByBaseUrl(optList, baseUrl) {
    for (const item of optList) {
      if (item.list && item.list.length) {
        for (const i of item.list) {
          if (baseUrl.includes(i)) {
            return item.unitKey
          }
        }
      }
    }
    return ""
  },
  /**
   * 处理详情页跳转
   * @param {string} key - 单元键
   * @param {Object} queryParams - 查询参数
   */
  handleDetailRedirect(key, queryParams) {
    const pathMap = {
      courseDetail: "/package-goods/course/detail/index",
      questionsetDetail: "/package-goods/questionset/detail/index",
      advclassDetail: "/package-goods/advclass/advclass-detail/index",
    }
    const path = pathMap[key]
    const param = {
      no: queryParams.no,
      app_brand: queryParams.brand,
      app_province: queryParams.province,
    }
    ROUTER.redirectTo({ path, query: param })
  },

  /**
   * 处理配置信息或 WebView 页面
   * @param {string} key - 单元键
   * @param {string} url - 解码后的 URL
   */
  handleConfigOrWebView(key, url) {
    const flattenedArray = supportDomainList.reduce(
      (acc, curr) => acc.concat(curr.list),
      []
    )
    const paramList = encodeURIComponent(JSON.stringify(flattenedArray))
    this.setData({
      url: `${url}${url.includes("?") ? "&" : "?"}optList=${paramList}`,
    })
  },

  /**
   * 获取配置信息
   */
  async getShareInfo() {
    const res = await UTIL.request(API.getShareInfo, { id: this.data.id })
    if (res) {
      this.setData({ shareData: res.data })
    }
  },
  //转发
  forward() {
    wx.shareFileMessage({
      filePath: this.data.filePath,
      fileName: this.data.fileName,
      success: () => {
        console.log("转发文档成功")
      },
      fail: (err) => {
        console.log(`转发文档失败`, err)
      },
    })
  },

  getPageShareParams() {
    let query = this.options
    return APP.createShareParams({
      title: this.data?.shareData.title || this.options?.title || "",
      imageUrl: this.data?.shareData.image || "",
      path: "/pages/webview/web/index",
      query: query,
    })
  },
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  // 复制链接
  copyUrl() {
    const link = this.data.isNotSupportUrl

    // 调用微信API复制链接到剪贴板
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: "复制成功",
          duration: 2000,
          icon: "none",
        })
      },
    })
  },
})
