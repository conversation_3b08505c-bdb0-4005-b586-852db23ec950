.van-tabbar--fixed {
  bottom: 0;
  left: 0;
  position: fixed;
  width: 100%;
  background-color: #fff;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  z-index: 100;
}

.van-hairline--top-bottom {
  border-top: 0.5px solid #ebecf0;
}

.boottom-video {
  position: fixed;
  left: 24rpx;
  background: #fff;
  padding: 16rpx;
  display: flex;
  align-items: center;
  right: 24rpx;
  // border-top: 0.5px solid rgba(235, 236, 240, 1);
  box-shadow: 0rpx 6rpx 16rpx 2rpx rgba(34, 36, 46, 0.1);
  border-radius: 56rpx;
  .music-bg {
    width: 64rpx;
    height: 64rpx;
  }
  text {
    font-size: 26rpx;
    color: #3c3d42;
    padding-left: 16rpx;
    flex: 1;
    min-width: 0;
    padding-right: 16rpx;
  }
  .bo-f {
    width: 48rpx;
    height: 48rpx;
  }
  .close {
    width: 32rpx;
    height: 32rpx;
    margin-left: 24rpx;
  }

  .prev {
    width: 48rpx;
    height: 48rpx;
  }

  .circle-box {
    display: flex;
    align-items: center;
    height: 64rpx;
    width: 64rpx;
    // margin: 0 12rpx;
    margin-top: 8rpx;
    .imgs {
      width: 24rpx;
      height: 24rpx;
      z-index: 2;
    }
  }
  .menu {
    width: 32rpx;
    height: 32rpx;
    margin-left: 32rpx;
  }
}

.play-box {
  padding: 32rpx 40rpx 0 40rpx;
  .play-box-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .left-menu {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #3c3d42;
      position: relative;
      .jian {
        width: 24rpx;
        height: 24rpx;
        margin-left: 8rpx;
        transform: rotate(-180deg);
        &.xuan {
          transform: rotate(0deg);
        }
      }
    }
    .menu-box {
      width: 192rpx;
      position: absolute;
      left: 0;
      top: 60rpx;
      background: #ffffff;
      box-shadow: 0rpx 6rpx 16rpx 2rpx rgba(0, 0, 0, 0.1);
      border-radius: 16rpx;
      padding: 0 16rpx;
      z-index: 22;

      .menu-box-item {
        font-size: 26rpx;
        color: #3c3d42;
        padding: 24rpx 0;
        text-align: center;
        border-bottom: 1rpx solid #ebecf0;
        &:last-child {
          border-bottom: 0;
        }
      }
    }
    .right-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(242, 244, 247, 0.6);
      font-size: 26rpx;
      color: #666666;
      padding: 12rpx 16rpx;
      border-radius: 28rpx;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
    }
  }
  .play-list {
    padding-top: 8rpx;
    .play-list-item {
      padding: 32rpx 0;
      border-bottom: 1rpx solid #ebecf0;
      &:last-child {
        border-bottom: 0;
      }
      .title {
        font-size: 28rpx;
        color: #3c3d42;
        // font-weight: bold;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-right: 10rpx;
        }
      }
      &.active {
        .title {
          color: #e60000;
        }
      }
      .time {
        font-size: 24rpx;
        color: #919499;
      }
      .fonts {
        display: flex;
        align-items: center;
        margin-top: 20rpx;
      }
      .lines {
        font-size: 24rpx;
        color: #c2c5cc;
        margin: 0 8rpx;
      }
      .progress {
        font-size: 24rpx;
        color: #919499;
      }
    }
  }
}

.scroll-box-h {
  height: 70vh;
}

.scroll-box-h ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.van-overlay {
  background-color: rgba(0, 0, 0, 0.1); /* 调整这里的透明度值 */
}

.bo-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
