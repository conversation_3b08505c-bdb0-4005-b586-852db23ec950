const CONFIG = require("./config/config")
const API = require("./config/api")
const UTIL = require("./utils/util")
const Player = require("./utils/AudioPlayer")
const ROUTER = require("./services/routerManager")
const AudioService = require("./utils/AudioPlayerBg")
const BASE_CACHE = require("@/utils/cache/baseCache")
const USER_CACHE = require("@/utils/cache/userCache")
const { cmdManage } = require("@/services/cmdManager")
const { checkSetupLocaltionBase } = require("./services/checkSetupLocaltion")
// 创建一个缓存对象用于存储 promise，模拟单例模式
const promiseCache = new Map()
// app.js
App({
  cmdManage,
  onLaunch: function (options) {
    console.log("小程序启动", options)
    this.globalData.popuShow = true
    this.globalData.launchOptions = options
    // 初始化音频服务并挂载到globalData
    console.log(this.globalData.audioService, "yinpin")
    this.getShowScreenHeight()
    // 延迟下拉数据，避免config请求获取不到token
    setTimeout(() => {
      this.checkLoadRequest()

      //obeyMuteSwitch （仅在 iOS 生效）是否遵循静音开关，设置为 false 之后，即使是在静音模式下，也能播放声音
      wx.setInnerAudioOption({ obeyMuteSwitch: false })
    })
    if (!this.globalData.audioService) {
      this.globalData.audioService = new AudioService()
    }
    this.globalData.audioService.setTimer()
    this.globalData.audioService.getCcumulationListTag()
    const AudioState = wx.getStorageSync("AudioState")
    if (AudioState.tagId) {
      this.globalData.audioService.setState(AudioState)
    }
  },

  // 监听小程序全局显示
  async onShow(options) {
    let examList = this.globalData.serverConfig?.exam_province_list
    const queryExamKey = Number(options.query.exam)
    const queryProvinceKey = options.query.province
    const queryCampusId = options.query.campus_id // 页面的校区id
    const launchCampusId = this.globalData.launchOptions?.query?.campus_id // 本地存储校区id

    // 没有省份和考试就不执行
    if (!queryExamKey && !queryProvinceKey) {
      return false
    }

    console.log("onShow 修改选择设置", options)

    // 没有数据时去请求
    if (!examList || examList?.length <= 0) {
      await this.checkLoadRequest()
    }
    examList = this.globalData.serverConfig?.exam_province_list

    const { exam, province } = BASE_CACHE.getBaseCache() // 校区 考试方向 省份

    console.log(exam, province)

    // 当前校区id与入口参数id不一致时执行
    if (
      JSON.stringify(queryExamKey) !== JSON.stringify(exam.key) ||
      queryProvinceKey !== province?.key ||
      (queryCampusId && launchCampusId !== queryCampusId)
    ) {
      // 有校区id时，更新
      try {
        if (queryCampusId) {
          this.globalData.launchOptions.query.campus_id = queryCampusId
        }
      } catch (error) {}

      this.initCommonConfiguration(examList, options.query)
      return
    }
  },

  // 状态监听系统
  watchPlayer(handler) {
    const listener = (state) => handler(state)
    this.globalData.player.on("stateChange", listener)

    // 返回取消监听函数
    return () => {
      this.globalData.player.off("stateChange", listener)
    }
  },

  // 获取公共配置数据
  getConfiguration: async function () {
    const respose = await UTIL.request(
      API.getCommonConfiguration,
      { launchOptions: this.globalData.launchOptions },
      "get"
    )
    if (respose.data) {
      this.globalData.serverConfig = respose.data
    }
    return respose
  },

  // 设置用户信息
  setUserInfo(data) {
    this.globalData.userInfo = data
    USER_CACHE.setUserInfoCache(data)
  },

  // 获取用户信息
  getUserInfo: async function () {
    let codeRes
    try {
      codeRes = await this.getWxCode()
      // codeRes = {}
    } catch (error) {
      codeRes = {}
    }

    const respose = await UTIL.request(API.getUserInfo, {
      code: codeRes.code,
    })
    // 有用户信息时，更新用户信息
    if (respose.error.code === 0) {
      this.setUserInfo(respose.data)
    } else {
      this.setUserInfo({})
    }
    return this.globalData.userInfo
  },

  // 获取code
  getWxCode() {
    return new Promise(function (resolve, reject) {
      wx.login({
        success: (res) => {
          resolve(res)
          // resolve({})
        },
        fail: (e) => {
          reject(e)
        },
      })
    })
  },

  // 用户登录
  userLogin: async function (data) {
    const codeData = await this.getWxCode()

    const params = {
      encryptedData: data.encryptedData,
      iv: data.iv,
      code: codeData.code,
    }
    const launchOptionsQuery = this.globalData.launchOptions.query || {}
    if (launchOptionsQuery.province) {
      params.default_province = launchOptionsQuery.province
    }
    if (launchOptionsQuery.campus_id) {
      params.default_campus_id = launchOptionsQuery.campus_id
    }
    if (launchOptionsQuery.rcode) {
      params.rcode = launchOptionsQuery.rcode
    }

    const respose = await UTIL.request(API.userLogin, params)

    // 有用户信息时，更新用户信息
    if (respose.error.code === 0) {
      this.setUserInfo(respose.data)
      return respose.data
    }
    return null
  },

  // 初始化配置信息
  async initCommonConfiguration(exam_province_list, optionsQuery) {
    const newCache = checkSetupLocaltionBase(exam_province_list, optionsQuery)

    if (newCache.exam || newCache.province) {
      if (newCache.exam) {
        BASE_CACHE.setBaseExamCache({
          name: newCache.exam.exam_name,
          key: newCache.exam.exam_key,
        })
      }

      if (newCache.province) {
        BASE_CACHE.setBaseProvinceCache({
          name: newCache.province.province_name,
          key: newCache.province.province_key,
        })
      }

      return true
    }

    return true
  },

  // 检查全局请求是否完成
  checkLoadRequest: async function () {
    // 辅助函数：获取数据并使用缓存（保持不变）
    const fetchWithCache = async (fetchFunction, cacheKey, setData) => {
      if (!promiseCache.has(cacheKey)) {
        const promise = fetchFunction()
          .then(setData)
          .finally(() => promiseCache.delete(cacheKey))
        promiseCache.set(cacheKey, promise)
      }
      return promiseCache.get(cacheKey)
    } // 收集需要发起的请求数组

    const requests = [] // 用户信息请求

    if (!this.globalData.userInfo) {
      // 优化初次加载请求
      this.globalData.userInfo = USER_CACHE.getUserInfoCache() || null
      requests.push(
        fetchWithCache(
          () => this.getUserInfo(),
          "userInfo",
          (data) => this.setUserInfo(data)
        )
      )
    } // 配置信息请求

    if (!this.globalData.serverConfig) {
      requests.push(
        fetchWithCache(
          () => this.getConfiguration(),
          "serverConfig",
          (data) => {
            this.globalData.serverConfig = data.data

            this.initCommonConfiguration(
              this.globalData.serverConfig.exam_province_list,
              this.globalData.launchOptions.query
            )
          }
        )
      )
    } // 并发执行所有请求

    await Promise.all(requests)

    return true
  },

  getShowScreenHeight() {
    //系统信息
    const systemInfo = wx.getWindowInfo()
    //胶囊位置
    const capsuleInfo = wx.getMenuButtonBoundingClientRect()
    let navHeight = systemInfo.statusBarHeight
    this.globalData.navHeight = 10
    // console.log(navHeight, "12312321")
  },

  // 判断是否登录
  getIsUserLogin() {
    return !!this.globalData.userInfo?.token
  },

  // 设置复制当前页面url
  setCopyCurrentPageUrl(query) {
    const result = this.createShareParams({ query })
    wx.onCopyUrl(() => {
      return { query: ROUTER.convertPathQuery(result.query) }
    })
  },
  /**
   * 创建分享参数对象。
   * @param {Object} [param={}] - 一个可选的对象，用于自定义分享内容。它可能包含以下属性：
   * @param {string} [param.title] - 分享标题，默认使用全局配置中的分享文本。
   * @param {string} [param.imageUrl] - 分享图片链接，默认使用全局配置中的分享图片。
   * @param {string} [param.path] - 分享链接的基础路径（仅在分享给好友时需要提供）。
   * @param {Object} [param.query] - 路径参数对象。
   * @returns {Object} 返回一个包含了分享所需的参数对象。
   */
  createShareParams(param = {}) {
    const baseCache = BASE_CACHE.getBaseCache()
    const serverShareConfig = this.globalData.serverConfig.share_config?.default
    const launchOptionsQuery = this.globalData.launchOptions?.query || {}
    const result = {
      title: param.title || serverShareConfig.title || "",
      imageUrl: param.imageUrl || serverShareConfig.image || "",
      query: {},
    }

    // 当不传图片时，默认使用系统图片
    if (param.imageUrl === "screenshot") {
      result.imageUrl = ""
      param.imageUrl = ""
    }

    if (launchOptionsQuery.campus_id) {
      param.query.campus_id = launchOptionsQuery.campus_id
    }

    if (baseCache.province?.key) {
      param.query.province = baseCache.province.key
    }
    if (baseCache.exam?.key) {
      param.query.exam = baseCache.exam?.key
    }

    // 分享携带参数
    if (param.query) {
      result.query = Object.assign(result.query, param.query)
    }

    // 分享路径（仅分享好友时传入）
    if (param.path) {
      const path = param.path
      const query = param.query
      delete param.query
      result.path = ROUTER.createPathWithParams(path, query)
    }
    console.log(result)
    return result
  },

  globalData: {
    player: new Player(),
    popuShow: false, // 首页弹窗配置
    serverConfig: null, // 服务器配置
    userInfo: null,
    launchOptions: {}, // 小程序进入时的参数
    navHeight: 0,
    headerHeight: 0,
    audioService: null, // 全局背景音乐播放器
  },
})
