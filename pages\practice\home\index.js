const ROUTER = require("@/services/routerManager")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
const { findExamProvinceData } = require("@/services/checkSetupLocaltion")
let audioService = null
Page({
  data: {
    bannerSwiperIndex: 0,
    advertisement_list: [1, 2],
    isLogin: true,
    exerciseStructureList: [],
    exerciseStructureAcitves: "",
    continueKey: "",
    show_white: false,
    isComplete: false,
    homeData: null,
    musicTitle: "素材随身听",
    musicLabel: "素材聚沙成塔，考场从容应答",
    musicFilm:
      "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/film_1.png",
    isPlaying: false,
    currentIndex: 0,
    tagId: "",
    showPopup: false,
    cmdPopup: false,
    imagePopu: "",
    navigationBarHeight: "",
    paddingTop: 0,
    is_show_accumulation_banner: false,
    hideenShow: false,
    weichatShow: false, // 微信客服弹窗
    weichatCustomerService: false, // 微信客服信息
    isShowDeafult: false,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    let menuInfo = wx.getMenuButtonBoundingClientRect()
    this.setData({
      navigationBarHeight: menuInfo.top,
      cacheStting: BASE_CACHE.getBaseCache(),
      paddingTop: menuInfo.top + 131,
      is_show_accumulation_banner:
        APP.globalData.serverConfig.is_show_accumulation_banner === 1 &&
        this.getAccumulationConfig(),
    })
    audioService = APP.globalData.audioService
    console.log(APP.globalData)
    this.getHome()

    if (
      !this.data.cacheStting?.province?.key ||
      !this.data.cacheStting?.exam?.key
    ) {
      ROUTER.navigateTo({
        path: "/pages/entry/list/index",
      })
    }
  },
  onShow() {
    if (!this.data.isComplete) {
      return
    }
    this.getHome()
    let menuInfo = wx.getMenuButtonBoundingClientRect()
    this.setData({
      cacheStting: BASE_CACHE.getBaseCache(),
      paddingTop: menuInfo.top + 131,
    })
    if (
      !this.data.cacheStting?.province?.key ||
      !this.data.cacheStting?.exam?.key
    ) {
      ROUTER.navigateTo({
        path: "/pages/entry/list/index",
      })
    }
  },
  refresh() {
    this.getHome()
  },
  // 获取首页
  getHome() {
    this.getAccumulationConfig()
    UTIL.request(API.getHome, {}, "get").then((res) => {
      if (!res.data) {
        this.setData({
          isComplete: true,
        })
        return
      }
      if (audioService && !this.data.isComplete) {
        console.log("播放器", audioService.getState())
        const tagId = audioService.getState().tagId
        if (tagId) {
          audioService.getMusicList(tagId)
        }
        // 注册状态变化监听器
        this.unwatchPlayer = audioService.watchPlayer((state) => {
          // console.log("首页播放器状态更新:", state)
          // 直接从state中获取当前播放项的状态信息
          const currentItemState = state

          const accumulation_default_config = this.getAccumulationConfig()
          // 更新UI数据
          this.setData({
            currentTime: currentItemState.currentTime,
            duration: currentItemState.duration,
            progress: currentItemState.progress,
            musicTitle:
              currentItemState.title || accumulation_default_config?.title,
            isPlaying: state.isPlaying,
            musicLabel:
              currentItemState.summary || accumulation_default_config?.desc,
            tagId: currentItemState.tagId,
            musicFilm:
              currentItemState.tag_film_img || accumulation_default_config?.img,
          })
        })
        audioService.updateAndEmitState(
          audioService.getState().id,
          audioService.getState().isPlaying
        )
      }
      let resData = res.data
      // 广告列表
      if (resData?.home_ad?.length > 0) {
        resData.home_ad = resData.home_ad.map(this.parseCmd)
      }
      // 首页弹窗广告
      if (resData?.popup_ad?.length > 0 && resData?.popup_ad[0].img) {
        // once  只弹一次 , 1d 每天  ,2d 每两天,3d 每三天, reboot 小程序每次启动
        const popupAd = resData.popup_ad[0]
        const frequency = popupAd.frequency // 弹出频率
        const popupId = popupAd.id
        const lastShowTimeKey = `popupLastShowTime-${popupId}`
        const now = Date.now()

        switch (frequency) {
          case "once":
            const onceShowStorage = wx.getStorageSync("onceShow") || {}
            if (!onceShowStorage[popupId]) {
              this.setData({ showPopup: true })
              onceShowStorage[popupId] = true
              wx.setStorageSync("onceShow", onceShowStorage)
            }
            break
          case "1d":
            if (!this.hasShownToday(lastShowTimeKey)) {
              this.setData({ showPopup: true })
              wx.setStorageSync(lastShowTimeKey, now)
            }
            break
          case "2d":
            if (!this.hasShownInDays(lastShowTimeKey, 2)) {
              this.setData({ showPopup: true })
              wx.setStorageSync(lastShowTimeKey, now)
            }
            break
          case "3d":
            if (!this.hasShownInDays(lastShowTimeKey, 3)) {
              this.setData({ showPopup: true })
              wx.setStorageSync(lastShowTimeKey, now)
            }
            break
          case "reboot":
            if (APP.globalData.popuShow) {
              this.setData({ showPopup: true })
            }
            break
          default:
            console.warn("Unknown frequency:", frequency)
        }
      }

      const isShowDeafult =
        resData?.menu_list?.length == 0 && resData?.home_ad?.length == 0
          ? true
          : false
      this.setData({
        homeData: resData,
        isComplete: true,
        isShowDeafult,
      })
      this.getRreeList()
    })
  },

  getAccumulationConfig() {
    const cacheStting = BASE_CACHE.getBaseCache()
    const examKey = cacheStting?.exam?.key
    const provinceKey = cacheStting?.province?.key
    const data = findExamProvinceData(
      APP.globalData.serverConfig.exam_province_list,
      provinceKey,
      examKey
    )
    return data?.accumulation_default_config
  },
  // 处理关闭校区微信客服
  closeWeichatCustomerService() {
    this.setData({
      weichatShow: false,
    })
  },
  // 处理打开校区微信客服
  openWeichatCustomerService() {
    let configList = APP?.globalData?.serverConfig?.customer_list
    const obj = BASE_CACHE.getBaseCache() || {}
    let province = obj.province?.key
    let customerService = configList.find(
      (item) => item.province_key == province
    ).customer_config
    if (customerService.type == "popup") {
      this.setData({
        weichatCustomerService: customerService,
        weichatShow: true,
      })
      return
    }
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: {
        url: customerService.url,
      },
    })
  },
  closeHidden() {
    console.log("12321321312")
    this.setData({
      hideenShow: false,
    })
  },
  openHideen() {
    this.setData({
      hideenShow: true,
    })
  },
  toChangeExam() {
    ROUTER.navigateTo({
      path: "/pages/entry/list/index",
      query: {
        back: false,
      },
    })
  },
  hasShownToday(lastShowTimeKey) {
    const lastShowTime = wx.getStorageSync(lastShowTimeKey)
    if (!lastShowTime) return false
    const todayStart = new Date().setHours(0, 0, 0, 0)
    return lastShowTime >= todayStart
  },
  hasShownInDays(lastShowTimeKey, days) {
    const lastShowTime = wx.getStorageSync(lastShowTimeKey)
    if (!lastShowTime) return false
    const threshold = Date.now() - days * 24 * 60 * 60 * 1000
    return lastShowTime >= threshold
  },
  // onUnload() {
  //   // 页面卸载时取消监听
  //   if (this.unwatchPlayer) {
  //     this.unwatchPlayer()
  //     this.unwatchPlayer = null // 清除引用，有助于垃圾回收
  //   }
  // },

  // onHide() {
  //   // 页面隐藏时取消监听（可选）
  //   if (this.unwatchPlayer) {
  //     this.unwatchPlayer()
  //     this.unwatchPlayer = null // 清除引用，有助于垃圾回收
  //   }
  // },
  // 套卷练习
  goPractice(e) {
    const data = e.currentTarget.dataset.item
    ROUTER.navigateTo({
      path: "/pages/situation/sheetDetail/index",
      query: {
        id: data.id,
      },
    })
  },
  // 去套卷列表
  goMore() {
    ROUTER.navigateTo({
      path: "/pages/situation/sheetList/index",
      query: {
        b_id: this.data.homeData?.home_book_list?.b_id,
      },
    })
  },
  onClickHide() {
    APP.globalData.popuShow = false
    this.setData({
      showPopup: false,
    })
  },
  parseCmd(adItem) {
    try {
      adItem.cmd_json = JSON.parse(adItem.cmd_json)
    } catch (e) {
      console.error("解析cmd_json失败:", e)
      adItem.cmd_json = {} // 或者可以设置为null或其他默认值
    }
    return adItem
  },
  // 处理banner swiper 切换
  handleBannerSwiperChange(e) {
    this.setData({
      bannerSwiperIndex: e.detail.current,
    })
  },
  // 监听知识点展开
  onExerciseStructureChange(e) {
    const key = e.detail.key
    const exerciseStructureAcitves = this.data.exerciseStructureAcitves
    this.setData({
      exerciseStructureAcitves: this.toggleValueInArray(
        [...exerciseStructureAcitves],
        key
      ),
    })
  },
  // 监听知识点的点击
  onRightButton(e) {
    const info = e.detail
    console.log("", info)
    ROUTER.navigateTo({
      path: "/pages/situation/questionList/index",
      // query: {
      //   key: info.userInfo.key,
      //   occasion_key: info.userInfo.occasion_key,
      //   name: info.userInfo.name,
      // },
      query: {
        key: info.key,
        occasion_key: info.occasion_key,
        name: info.name,
      },
    })
  },
  // 获取知识结构
  getRreeList() {
    UTIL.request(API.getRreeList).then((res) => {
      // console.log(res, "12312312321")
      this.getHomeExerciseProgressRequest(res.data)
    })
  },
  // 获取首页知识点进度
  async getHomeExerciseProgressRequest(exerciseInfo) {
    const treeList = exerciseInfo.list
    const done_list = []
    this.traverseTreeList(treeList, (node) => {
      done_list.push(node.key)
    })
    const res = await UTIL.request(API.getHomeExerciseProgress, {
      data: {
        done_list,
      },
    })
    const resData = res.data || {}
    try {
      this.traverseTreeList(treeList, (node) => {
        const key = node.key
        const progress = resData.done_list || {}
        node.right_question_num = progress[key].done_num || 0
      })
      this.setData({
        exerciseStructureList: treeList,
      })
    } catch (error) {
      console.log(error)
      this.setData({
        exerciseStructureList: treeList,
      })
    }
  },
  // 深度遍历
  traverseTreeList(list, callback) {
    list.forEach((node) => {
      // 调用回调函数处理当前节点
      callback(node)

      // 如果当前节点有子节点，则递归遍历子节点
      if (node.child && node.child.length > 0) {
        this.traverseTreeList(node.child, callback)
      }
    })
  },
  toggleValueInArray(arr, value) {
    // 检查数组中是否已经包含指定的值
    const index = arr.indexOf(value)

    if (index > -1) {
      // 如果数组中包含该值，则删除它
      arr.splice(index, 1)
    } else {
      // 如果数组中不包含该值，则添加它
      arr.push(value)
    }
    // 返回更新后的数组
    return arr
  },
  // 浮窗点击
  goFloat() {
    const data = this.data.homeData.suspension_ad[0]
    const cmd = this.parseCmdJson(data.cmd_json)
    // 图片弹窗
    if (cmd.type == "image") {
      this.setData({
        imagePopu: cmd.param.url,
        cmdPopup: true,
      })
      return
    }
    APP.cmdManage(cmd, data)
  },
  goNotice() {
    const data = this.data.homeData.notice[0]
    const cmd = this.parseCmdJson(data.cmd_json)
    // 图片弹窗
    if (cmd.type == "image") {
      this.setData({
        imagePopu: cmd.param.url,
        cmdPopup: true,
      })
      return
    }
    APP.cmdManage(cmd, data)
  },
  goMenu(e) {
    console.log("点击了")
    const data = e.currentTarget.dataset.item
    const cmd = data.cmd_json
    // 图片弹窗
    if (cmd.type == "image") {
      this.setData({
        imagePopu: cmd.param.url,
        cmdPopup: true,
      })
      return
    }
    APP.cmdManage(cmd, data)
  },
  parseCmdJson(cmdJsonStr) {
    try {
      // 尝试将字符串转换为对象
      const cmdJson = JSON.parse(cmdJsonStr)
      return cmdJson
    } catch (error) {
      console.error("JSON 解析错误:", error)
      // 如果解析失败，返回 null 或者其他默认值
      return null
    }
  },
  // 菜单跳转
  goDetail(e) {
    const data = e.currentTarget.dataset.item
    const cmd = this.parseCmdJson(data.cmd_json)
    // 图片弹窗
    if (cmd.type == "image") {
      this.setData({
        imagePopu: cmd.param.url,
        cmdPopup: true,
      })
      return
    }
    if (cmd.type == "Customer") {
      this.openWeichatCustomerService()
      return
    }
    // 执行指令
    APP.cmdManage(cmd, data)
  },
  closePopu() {
    this.setData({
      cmdPopup: false,
    })
  },

  // 打开播放清单
  openMusicPopu() {
    const childComponent = this.selectComponent("#home")
    if (childComponent) {
      console.log("调用了没得")
      childComponent.openList()
    }
    this.setData({
      hideenShow: true,
    })
  },
  // 点击banner播放
  changePlay() {
    this.setData({
      isPlaying: !this.data.isPlaying,
    })
    const index = audioService.getState().currentIndex
    const isPlaying = audioService.getState().isPlaying
    if (isPlaying) {
      audioService.pause()
    } else {
      audioService.playPlaylist(index == -1 ? 0 : index)
    }
  },
  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight - 10) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  onReachBottom() {},
  getPageShareParams() {
    let query = {}
    return APP.createShareParams({
      title: this.data?.homeData?.share_info?.title || "",
      imageUrl: this.data?.homeData?.share_info?.image || "",
      path: "/pages/practice/home/<USER>",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
