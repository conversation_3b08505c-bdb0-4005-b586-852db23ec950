page {
  background: rgba(242, 244, 247, 1);
  box-sizing: border-box;
}

.main-content {
  padding: 32rpx;
  box-sizing: border-box;
  padding-bottom: 130rpx;
}

.banner {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
}

.practice-list {
  &-item {
    position: relative;
    background: #fff;
    padding: 32rpx;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    .title-box {
      padding-bottom: 32rpx;
      border-bottom: 0.5px solid rgba(235, 236, 240, 1);
    }
    .tag-label {
      background: rgba(76, 96, 133, 0.1);
      color: rgba(76, 96, 133, 1);
      font-size: 22rpx;
      padding: 0 16rpx;
      height: 36rpx;
      line-height: 36rpx;
      display: inline-block;
      border-radius: 8rpx;
      box-sizing: border-box;
      margin-bottom: 15rpx;
    }

    .title {
      font-size: 30rpx;
      color: #3c3d42;
      box-sizing: border-box;
      line-height: 60rpx;

      text {
        background: rgba(57, 160, 237, 0.1);
        color: #e60000;
        font-size: 22rpx;
        padding: 0 12rpx;
        height: 36rpx;
        line-height: 36rpx;
        display: inline-block;
        border-radius: 8rpx;
        margin-right: 10rpx;
        box-sizing: border-box;
        transform: translateY(-2rpx);
      }
    }
    .new {
      top: -14rpx;
      right: -8rpx;
      position: absolute;
      width: 52rpx;
      height: 28rpx;
    }
    .information-box {
      display: flex;
      margin-top: 32rpx;
      // padding-bottom: 20rpx;
      // border-bottom: 0.5px solid rgba(235, 236, 240, 1);
      image {
        width: 28rpx;
        height: 28rpx;
        margin-right: 8rpx;
        margin-top: 4rpx;
      }
      text {
        font-size: 24rpx;
        color: rgba(76, 96, 133, 1);
        line-height: 36rpx;
        flex: 1;
        min-width: 0;
      }
    }
    .practice-status {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      margin-top: 24rpx;
    }
  }
}

.answering-process {
  margin-top: 64rpx;
  padding: 0 32rpx;
  .answering-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #3c3d42;
  }
  .answering-process-box {
    margin-top: 32rpx;
    .process-item {
      position: relative;
      padding-bottom: 32rpx;
      .waiting {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #e60000;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-right: 4rpx;
        }
      }
      &:last-child::after {
        display: none;
      }
      .yuan {
        width: 14rpx;
        height: 14rpx;
        border: 4rpx solid #e60000;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 7rpx;
        background: #fff;
        z-index: 3;
      }
      padding-left: 38rpx;
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 1px;
        height: 100%;
        border-left: 1px dashed rgba(57, 160, 237, 0.2);
        left: 10rpx;
        top: 7rpx;
        z-index: 2;
      }

      .time {
        font-size: 24rpx;
        color: #919499;
        margin-bottom: 24rpx;
      }
      .right-box {
        flex: 1;
        background: rgba(242, 244, 247, 0.5);
        padding: 24rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        .left-font {
          flex: 1;
          min-width: 0;
          padding-right: 30rpx;
        }
        .right-arrow {
          width: 24rpx;
          height: 24rpx;
        }
        .titles {
          font-size: 28rpx;
          color: #3c3d42;
        }

        .time-all {
          font-size: 24rpx;
          color: #919499;
          text {
            color: #666666;
          }
          margin-right: 26rpx;
        }

        .line {
          width: 2rpx;
          height: 24rpx;
          background: #ebecf0;
        }

        .score-all {
          font-size: 24rpx;
          color: #919499;
          margin-left: 26rpx;
          text {
            color: #e60000;
          }
        }

        .font-bottom {
          display: flex;
          align-items: center;
          margin-top: 20rpx;
        }

        .label {
          font-size: 24rpx;
          color: #919499;
          margin-top: 14rpx;
        }
      }
    }
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar .button {
  flex: 1;
  height: 100%;
  background-color: var(--main-color);
  font-size: 30rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &.hui {
    opacity: 0.5;
  }
}

.left-collect {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  &.blue {
    text {
      color: #e60000;
    }
  }
  text {
    font-size: 20rpx;
    color: rgba(145, 148, 153, 1);
  }
  image {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
  }
}

.remain-time {
  position: fixed;
  left: 0;
  bottom: 142rpx;
  right: 0;
  background: #fff;
  box-sizing: border-box;

  .remain-time-box {
    background-color: rgba(57, 160, 237, 0.1);
    padding: 20rpx 32rpx;
    display: flex;
    align-items: center;
    &.error {
      background: rgba(255, 247, 245, 1);
      .text {
        color: rgba(245, 96, 58, 1);
      }
    }
    .text {
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      text {
        color: #e60000;
      }
      flex: 1;
      min-width: 0;
    }
    .right-btn {
      font-size: 24rpx;
      color: #e60000;
      display: flex;
      align-items: center;
      image {
        width: 24rpx;
        height: 24rpx;
        margin-left: 4rpx;
      }
    }
  }
}

.content-box {
  background: #ffffff;
  border-radius: 24rpx;
  box-sizing: border-box;
  .top-menu {
    display: flex;
    align-items: center;
    // background: #eaeaea;
    height: 100rpx;
    border-radius: 25rpx 25rpx 0 0;
    border: 3rpx solid #fff;
    box-sizing: border-box;
    position: relative;
    .bg {
      width: 100%;
      position: absolute;
      top: 0rpx;
    }
    .menu-item {
      font-size: 32rpx;
      color: #666666;
      position: relative;
      height: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 380rpx;
        height: 100rpx;
        display: block;
        // position: absolute;
        // // left: 0;
        // // z-index: -1;
      }
      &.active-right {
        image {
          width: 380rpx;
          height: 100rpx;
          display: block;
          position: absolute;
          right: 0rpx;
          z-index: 1;
          top: 0rpx;
        }
        .text {
          position: relative;
          z-index: 2;
          color: var(--main-color);
        }
      }
      &.active {
        image {
          width: 380rpx;
          height: 100rpx;
          display: block;
          position: absolute;
          top: 0rpx;
          left: 0rpx;
          z-index: 1;
        }
        .text {
          position: relative;
          z-index: 2;
          color: var(--main-color);
        }
      }
    }
  }

  .mind-mapping-img {
    width: 100%;
    // height: 462rpx;
  }
}

.tab-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
}

.black-color {
  color: #22242e !important;
}
.report-card-text {
  font-weight: 400;
  font-size: 26rpx;
  color: #3c3d42;
  line-height: 52rpx;
}
