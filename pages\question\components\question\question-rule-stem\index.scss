.single-pop {
  background: #fff;
  backdrop-filter: blur(30px);
  padding: 40rpx;
  border-radius: 24rpx;
  .right {
  }
  .practice-list-item {
    padding-top: 0;
    .information-box {
      border-top: 0;
      padding-top: 0;
    }
  }

  &-top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #3c3d42;
      font-weight: bold;
      .text {
        display: flex;
        align-items: center;
      }
      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 12rpx;
      }
      text {
        font-size: 24rpx;
        color: #919499;
        margin-left: 14rpx;
        font-weight: normal;
      }
    }
    .right {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #666666;
      image {
        width: 20rpx;
        height: 20rpx;
        margin-left: 8rpx;
      }
    }
  }

  &.active {
    .practice-list {
      &-item {
        position: relative;
        border-radius: 16rpx;
        padding-top: 28rpx;
        .title {
          color: #3c3d42;
          line-height: 1.5;
          font-size: 28rpx;
          color: #3c3d42;
          text {
            background: rgba(57, 160, 237, 0.1);
            color: #e60000;
            font-size: 22rpx;
            padding: 6rpx 12rpx;
            border-radius: 8rpx;
            margin-right: 10rpx;
          }
        }
        .new {
          top: -14rpx;
          right: -8rpx;
          position: absolute;
          width: 52rpx;
          height: 28rpx;
        }
        .information-box {
          display: flex;
          padding-top: 20rpx;
          // padding-bottom: 20rpx;
          margin-top: 20rpx;
          border-top: 0.5px solid rgba(235, 236, 240, 1);
          image {
            width: 32rpx;
            height: 32rpx;
            margin-right: 16rpx;
            margin-top: 4rpx;
          }
          text {
            font-size: 24rpx;
            color: rgba(76, 96, 133, 1);
            line-height: 36rpx;
          }
        }
        .practice-status {
          font-size: 24rpx;
          color: rgba(145, 148, 153, 1);
          margin-top: 24rpx;
        }
      }
    }
    .right {
    }
  }
  &.black {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(30.7rpx);
    .left {
      color: #fff;
    }
    .information-box {
      text {
        color: rgba(145, 148, 153, 1) !important;
      }
      border-color: rgba(255, 255, 255, 0.1) !important;
    }
    .practice-list-item {
      background: transparent;
      .title {
        color: rgba(255, 255, 255, 0.8);
        text {
          background: rgba(102, 102, 102, 1);
          color: #fff;
        }
      }
    }
    .single-pop-top {
      border-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.practice-list {
  &-item {
    position: relative;
    // background: #fff;
    border-radius: 16rpx;
    padding-top: 28rpx;
    .title {
      font-size: 30rpx;
      color: #3c3d42;
      line-height: 46rpx;

      text {
        background: rgba(57, 160, 237, 0.1);
        color: #e60000;
        font-size: 22rpx;
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
        margin-right: 10rpx;
      }
    }
    .new {
      top: -14rpx;
      right: -8rpx;
      position: absolute;
      width: 52rpx;
      height: 28rpx;
    }
    .information-box {
      display: flex;
      padding-top: 20rpx;
      // padding-bottom: 20rpx;
      margin-top: 20rpx;
      border-top: 0.5px solid rgba(235, 236, 240, 1);
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
        margin-top: 4rpx;
      }
      text {
        font-size: 24rpx;
        color: rgba(76, 96, 133, 1);
        line-height: 36rpx;
      }
    }
    .practice-status {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      margin-top: 24rpx;
    }
  }
}

.scroll-box {
  max-height: 450rpx;
}

.single-pop-content {
  margin-top: 24rpx;
  border-top: 0.5px solid rgba(34, 36, 46, 0.1);
}

.stem-drak {
  background-color: rgba(#000000, 0.5) !important;
  .single-pop-top .left {
    color: #fff;
  }
  .single-pop-content {
    border-top: 1rpx solid rgba(#fff, 0.1) !important;
  }
  .single-pop-top .right {
    color: rgba(#fff, 0.8) !important;
  }
  .practice-list-item {
    .title {
      color: rgba(#fff, 0.8) !important;
    }
  }
}

.rich-text {
  font-size: 28rpx;
  line-height: 50rpx;
  white-space: break-spaces;
}
