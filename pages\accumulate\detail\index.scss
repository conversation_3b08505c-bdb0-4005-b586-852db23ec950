page {
  box-sizing: border-box;
}

.top-box {
  box-sizing: border-box;
  background: #fff;
  padding: 16rpx 40rpx 32rpx 40rpx;
  text-align: center;
  font-size: 40rpx;
  color: #22242e;
  line-height: 64rpx;
  font-weight: bold;
}

.main-content {
  padding: 0 40rpx;
  box-sizing: border-box;
}

.politics-list {
  &-item {
    padding: 48rpx 0;
    border-bottom: 2rpx solid #ebecf0;
    .title {
      display: flex;
      align-items: center;
      font-size: 36rpx;
      color: #22242e;
      font-weight: bold;
      margin-bottom: 40rpx;
      image {
        width: 7rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
      text {
        flex: 1;
        min-width: 0;
      }
    }
    .content {
      font-size: 30rpx;
      color: #3c3d42;
      line-height: 54rpx;
    }
    &:last-of-type {
      border-bottom: none;
    }
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar .button-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .button {
    width: calc(50% - 7rpx);
    background-color: var(--main-color);
    font-size: 30rpx;
    color: #fff;
    height: 84rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .w100 {
    width: 100% !important;
  }
  .next-btn {
    background: #ffffff;
    border: 1rpx solid rgba(230, 0, 0, 0.6);
    color: #e60000;
  }
}

.left-collect {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  min-width: 60rpx;
  &.blue {
    text {
      color: #e60000;
    }
  }
  text {
    font-size: 20rpx;
    color: rgba(145, 148, 153, 1);
  }
  image {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
  }
}

.person-content {
  background: #ffffff;
}

.rich-text {
  font-size: 30rpx;
  line-height: 60rpx;
  color: #3c3d42;
}
.opc-05 {
  opacity: 0.5;
}
.ql-image {
  max-width: 100%;
}
.ql-align-center {
  text-align: center;
}
