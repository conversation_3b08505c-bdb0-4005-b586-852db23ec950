<view class="weui-navigation-bar {{extClass}} {{ isSticky?'sticky-style':''}} {{isResult && !showWhite? 'bg-result':''}}">
  <view class="weui-navigation-bar__inner " style="color: {{color}}; background: {{background}}; {{displayStyle}}; {{safeAreaTop}}">

    <view style="position: relative; width: 100%;">
      <!-- 左侧按钮 -->
      <view class='weui-navigation-bar__left'>
        <slot name="left"></slot>
      </view>

      <!-- 标题 -->
      <view class='weui-navigation-bar__center'>
        <view wx:if="{{loading}}" class="weui-navigation-bar__loading" aria-role="alert">
          <view class="weui-loading" aria-role="img" aria-label="加载中"></view>
        </view>
        <block wx:if="{{title}}">
          <text>{{title}}</text>
        </block>
        <block wx:else>
          <slot name="center"></slot>
        </block>
      </view>

      <!-- 右侧留空 -->
      <view class='weui-navigation-bar__right'>
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</view>