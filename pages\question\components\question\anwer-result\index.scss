.header-tips {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba($color: #f5603a, $alpha: 0.05);
  font-size: 22rpx;
  color: #f5603a;
  line-height: 46rpx;
}
.page-content {
  padding-left: 40rpx;
  padding-right: 40rpx;
  padding-top: 45rpx;

  .page-title {
    font-weight: bold;
    font-size: 36rpx;
    color: #3c3d42;
    line-height: 46rpx;
    margin-bottom: 64rpx;
  }
}
.media-content {
  margin-bottom: 80rpx;
  .video-content {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 2rpx rgba(34, 36, 46, 0.1);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 16rpx;
    margin: 0 auto;
    width: max-content;
    .video {
      width: 400rpx;
      height: 712rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
    }
  }
}
.time-content {
  display: flex;
  .time-item {
    flex: 1;
    height: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    & + .time-item {
      border-left: 1prx solid #ebecf0;
    }
    .name {
      font-size: 26rpx;
      color: #919499;
      line-height: 46rpx;
    }
    .text {
      font-weight: 500;
      font-size: 40rpx;
      color: #666666;
      margin-top: 25rpx;
    }
  }
}

.bottom-actionbar {
  position: fixed;
  bottom: 50rpx;
  width: 100%;
  .submit-button {
    width: 574rpx;
    height: 104rpx;
    background: linear-gradient(135deg, #ff5252 0%, #e60000 100%);
    border-radius: 60rpx 60rpx 60rpx 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 46rpx;
    margin: 0 auto 50rpx;
  }
  .replay-button {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #e60000;
    line-height: 46rpx;
    text-align: center;
    .icon {
      width: 32rpx;
      margin-right: 8rpx;
    }
  }
}
