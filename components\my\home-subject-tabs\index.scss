.scroll-x {
  white-space: nowrap;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
  }
  .scroll-item {
    display: inline-block;
  }
}

.cmpt-tabs {
  .tab-nav {
    box-sizing: border-box;

    &.flex_center {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .nav-item {
      position: relative;
      flex: 1;
      line-height: 54rpx;
      background: #fff;
      font-size: 26rpx;
      color: #666666;
      border-radius: 88rpx;
      padding: 0 32rpx;
      & + .nav-item {
        margin-left: 16rpx;
      }

      &.active {
        background: rgba(230, 0, 0, 0.05);
        color: #e60000;
      }
    }
  }
}
