<view class="single-pop {{open?'active':''}} {{isDark?'stem-drak':''}}">
  <!-- 单题 -->
  <view class="single-pop-top" wx:if="{{questionType ==='singleQuestion'}}">
    <view class="left">
      <block wx:if="{{isRecording}}">
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/stem_answering.png"></image>
        答题中
      </block>
      <block wx:else>
        <!-- 音频加载失败（已重试失败） -->
        <view class="text text-button" wx:if="{{playState[question.question_id].isFailed}}">请查看文字题目作答</view>
        <!-- 音频等待中 -->
        <view class="text text-button" wx:elif="{{playState[question.question_id].isWait}}">题目音频加载中... </view>
        <view class="text text-button" wx:elif="{{!playState[question.question_id].isPlaying}}" catch:tap="tapPlayQuestion">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_play.png"></image>
          <block wx:if="{{playState[question.question_id].isPlayCompleted}}">
            再听一遍
          </block>
          <block wx:elif="{{playState[question.question_id].isPause}}">
            继续听题
          </block>
          <block wx:else>
            开始听题
          </block>
        </view>
        <view class="text" wx:else catch:tap="tapPauseQuestion">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_pause.png"></image>
          听题中
        </view>
        <text wx:if="{{playState[question.question_id].isPlayCompleted&&playState[question.question_id].count}}">(已听 {{playState[question.question_id].count}} 遍)</text>
      </block>
    </view>

    <view class="right" catch:tap="changePop">
      <block>
        {{open?'隐藏文字题目':'查看文字题目'}}
      </block>
      <block wx:if="{{isDark}}">
        <image wx:if="{{open}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_up_white.png"></image>
        <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_down_white.png"></image>
      </block>
      <block wx:else>
        <image src="{{open?'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_up.png':'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_down.png'}}" mode="widthFix"></image>
      </block>
    </view>
  </view>
  <!-- 多题 -->
  <view class="single-pop-top" wx:if="{{questionType ==='manyQuestion'}}">
    <view class="left">
      <!-- 主状态 答题中 -->
      <block wx:if="{{mainState === 'answering'}}">
        <!-- 答题中 -->
        <block wx:if="{{question.state==='answer'}}">
          <view class="text">
            <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/stem_answering.png"></image>
            作答第 {{question.index+1}} 题
          </view>
        </block>

        <!-- 准备作答 -->
        <view wx:if="{{question.state==='prepare'}}">
          <!-- 听题模式 -->
          <block wx:if="{{manyMode==='listen'}}">
            <view class="text">
              <!-- 音频加载失败（已重试失败） -->
              <block wx:if="{{playState[question.question_id].isFailed}}">
                请查看文字题目作答
              </block>
              <!-- 音频等待中 -->
              <block wx:elif="{{playState[question.question_id].isWait}}">
                题目音频加载中...
              </block>
              <!-- 播放中 -->
              <block wx:elif="{{playState[question.question_id].isPlaying}}">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif" />
                请听第 {{question.index+1}} 题
              </block>
              <!-- 未播放 -->
              <block wx:else>
                <block wx:if="{{playState[question.question_id].isPlayCompleted}}">
                  <view class="text text-button" catch:tap="tapPlayQuestion">
                    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_play.png"></image>
                    再听一遍
                  </view>
                  <text wx:if="{{playState[question.question_id].isPlayCompleted&&playState[question.question_id].count}}">(已听 {{playState[question.question_id].count}} 遍)</text>
                </block>
                <block wx:else>
                  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.png" />
                  请准备作答第{{question.index+1}}题
                </block>
              </block>
            </view>
          </block>
          <!-- 看题模式 -->
          <block wx:else>
            <view class="text">准备作答第 {{question.index+1}} 题</view>
          </block>
        </view>
      </block>
      <block wx:elif="{{manyMode==='look'}}">
        <scroll-view scroll-x="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}" enable-flex style="white-space: nowrap;width:{{questionList.length<=3?'450':'550'}}rpx;height: 100rpx;margin-top: -30rpx;margin-bottom: -30rpx;">
          <view class="tab-list">
            <view class="tab-list-item {{activeIndex == index?'active':''}}" data-index="{{index}}" catch:tap="changeTab" wx:for="{{questionList}}" wx:key="index">第{{index+1}}题</view>
          </view>
        </scroll-view>
        <!-- tab-list -->
      </block>
    </view>
    <view class="right" catch:tap="changePop">
      <!-- 答题中 -->
      <block wx:if="{{((question.state==='answer'||question.state==='prepare') &&(questionList.length<=3)) || mainState==='answering'}}">
        {{open?'隐藏文字题目':'查看文字题目'}}
      </block>


      <block wx:if="{{isDark}}">
        <view style="margin: -10rpx 0; padding: 10rpx 0;">
          <image wx:if="{{open}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_up_white.png"></image>
          <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_down_white.png"></image>
        </view>
      </block>
      <block wx:else>
        <view style="margin: -10rpx 0; padding: 10rpx 0;">
          <image src="{{open?'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_up.png':'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_down.png'}}" mode="widthFix"></image>
        </view>
      </block>
    </view>
  </view>

  <view class="single-pop-content" wx:if="{{open}}">
    <view class="practice-list">
      <view class="practice-list-item">
        <scroll-view scroll-y class="scroll-box" wx:if="{{open}}">
          <block wx:if="{{mainState==='answering'}}">
            <view class="title">

              <view wx:for="{{questionList}}">
                <question-rich-text wx:if="{{item.question_id===question.question_id}}" customClass="questin-rich-text questin-rich-text-{{isDark?'drak':''}}" strText="{{item.content_delta}}" bind:onImagePreview="previewImage"></question-rich-text>
              </view>
            </view>
          </block>
          <block wx:else>
            <view class="title">
              <view wx:for="{{questionList}}">
                <!-- <text wx:if="{{questionList[activeIndex].tag_name}}">{{questionList[activeIndex].tag_name}}</text> -->
                <question-rich-text wx:if="{{activeIndex===index}}" customClass="questin-rich-text questin-rich-text-{{isDark?'drak':''}}" strText="{{item.content_delta}}" bind:onImagePreview="previewImage">
                </question-rich-text>
              </view>
            </view>
          </block>
        </scroll-view>

        <!-- 作答时 -->
        <block wx:if="{{mainState==='answering'}}">
          <view class="material-button" wx:if="{{question.document_content.length>0}}" data-item="{{question.document_content}}" catch:tap="onMaterialOpen">
            <image wx:if="{{!isDark}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/stem_material_black.png" mode="widthFix" class="icon" />
            <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/stem_material_white.png" mode="widthFix" class="icon" />
            查看材料
          </view>
        </block>
        <!-- 非作答时 -->
        <block wx:else>
          <view class="material-button" wx:if="{{questionList[activeIndex].document_content.length>0}}" data-item="{{questionList[activeIndex].document_content}}" catch:tap="onMaterialOpen">
            <image wx:if="{{!isDark}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/stem_material_black.png" mode="widthFix" class="icon" />
            <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/stem_material_white.png" mode="widthFix" class="icon" />
            查看材料
          </view>
        </block>

        <!-- <view class="information-box">
          <image wx:if="{{open}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_down_gray.png"></image>
          <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_down.png"></image>
          <text class="text-ellipsis-1">{{dateInfo}}</text>
        </view> -->
      </view>
    </view>
  </view>
</view>