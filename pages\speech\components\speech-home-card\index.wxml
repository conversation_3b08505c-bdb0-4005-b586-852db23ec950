<wxs src="/utils/wxs/utils.wxs" module="utils" />

<view class="speech-home-card" catchtap="goDetail" style="background-image: url({{card.img_data.home_img}});">
  <view class="speech-home-card-header">
    <view class="title-image">
      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_name.png" mode="widthFix" class="img" />
    </view>
    <view class="history-button" catch:tap="toHistory">
      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_history.png" mode="widthFix" class="img" />
      <text class="text">赛程</text>
    </view>
  </view>
  <view class="speech-home-card-content">
    <view class="content-title text-ellipsis-2">{{card.title}}</view>



    <block wx:if="{{card.top3_users.length}}">
      <view class="exam-content">
        <view class="exam-content-item">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_document.png" mode="widthFix" class="img" />
          <text class="text1">考题内容：</text>
          <text class="text2">{{card.qv_data.question_number}}题 · {{card.qv_data.answer_time}}分钟</text>
        </view>
        <view class="exam-content-item">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_history.png" mode="widthFix" class="img" />
          <text class="text1">参与时间：</text>
          <text class="text2">{{card.formattedStartTime}}开始</text>
        </view>

      </view>
      <view class="rank">
        <view class="rank-header">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_rank_icon.png" mode="widthFix" class="img-icon" />
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_rank_title.png" mode="widthFix" class="img-title" />
        </view>
        <view class="rank-content">
          <view class="rank-content-item" wx:for="{{card.top3_users}}" wx:key="index">
            <view class="number">
              <image wx:if="{{index===0}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_rank_1.png" mode="widthFix" class="img" />
              <image wx:if="{{index===1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_rank_2.png" mode="widthFix" class="img" />
              <image wx:if="{{index===2}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_rank_3.png" mode="widthFix" class="img" />
            </view>
            <view class="user-info">
              <view class="user-portrait">
                <image src="{{item.portrait}}" mode="widthFix" class="img" />
              </view>
              <text class="user-name text-ellipsis-1">{{item.nickname}}</text>
              <view class="user-score">
                <text class="number">{{utils.clearPriceZero(item.score)}}</text>
                <text class="text">分</text>
              </view>
            </view>
          </view>
        </view>

      </view>
    </block>
    <block wx:else>
      <view class="content-subtitle text-ellipsis-2">{{card.summary}}</view>
      <view class="exam-content">
        <view class="exam-content-item">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_document.png" mode="widthFix" class="img" />
          <text class="text1">考题内容：</text>
          <text class="text2">{{card.qv_data.question_number}}题·{{card.qv_data.answer_time}}分钟</text>
        </view>
        <view class="exam-content-item">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_heom_card_history.png" mode="widthFix" class="img" />
          <text class="text1">参与时间：</text>
          <text class="text2">{{card.formattedStartTime}}开始</text>
        </view>
      </view>

    </block>
  </view>
  <view class="speech-home-card-footer">
    <view class="time-button" wx:if="{{!isTimeEnd}}">
      <view class="number">{{timeStr}}</view>
      <view class="text">即将开始</view>
    </view>
    <block wx:else>
      <block wx:if="{{card.has_qualification}}">
        <block wx:if="{{card.join_record}}">
          <view class="join-button" wx:if="{{card.join_record.correct_status == 1}}" style="background: {{card.btn_color}};" catch:tap="toJoin">查看报告</view>
          <view class="join-button" wx:if="{{card.join_record.correct_status== 0}}" style="background: {{card.btn_color}};" catch:tap="toJoin">报告生成中</view>
          <view class="join-button" wx:if="{{card.join_record.correct_status == -1}}" style="background: {{card.btn_color}};" catch:tap="joinSpeech">报告生成失败，重新作答</view>
        </block>
        <view wx:else catchtap="btn">
          <authorize-phone isBindPhone="{{isLogin}}" catch:onAuthorize="joinSpeech">
            <view class="join-button" style="background: {{card.btn_color}};">参与PK</view>
          </authorize-phone>
        </view>
      </block>
      <view class="no-permission" wx:else catch:tap="openWeichatCustomerService">
        <image class="tip" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/no_permission.png"></image>
        本场演练为线下学员专享，去了解
        <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/yellow_right.png"></image>
      </view>
    </block>

  </view>
</view>