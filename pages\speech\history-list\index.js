const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
let PAGE_OPTIONS = {}
Page({
  data: {
    drillList: [],
    drillData: null,
    isRequest: true,
    isComplete: false,
  },
  async onLoad(options) {
    PAGE_OPTIONS = options
    await APP.checkLoadRequest()
    this.getDrillList()
  },
  onShow() {
    if (this.data.drillData) {
      this.getDrillList()
    }
  },
  async getDrillList() {
    const res = await UTIL.request(API.getDrillList, {
      tag_id: PAGE_OPTIONS.tag_id,
      job_id: PAGE_OPTIONS.job_id,
    })
    const resData = res.data
    if (resData.list.length > 0) {
      const now = new Date()
      resData.list = resData.list.map((item) => {
        const startTime = new Date(item.start_time.replace(" ", "T"))
        const status = now > startTime ? 1 : 2
        const formattedStartTime = this.formatStartTime(startTime)
        return {
          ...item,
          status: status,
          formattedStartTime: formattedStartTime,
        }
      })
      console.log("2112312321321312", resData.list)
    }
    this.setData({
      isComplete: true,
      drillData: resData,
      drillList: resData.list,
    })
  },
  // 查看完整报告
  goResult(e) {
    const data = e.currentTarget.dataset.item
    ROUTER.navigateTo({
      path: "/pages/speech/detail/index",
      query: {
        id: data.id,
      },
    })
  },
  getPageShareParams() {
    let query = {}
    return APP.createShareParams({
      title: this.data?.drillData?.share_info?.title || "",
      imageUrl: this.data?.drillData?.share_info?.image || "",
      path: "/pages/speech/history-list/index",
      query: query,
    })
  },
  formatStartTime(date) {
    const monthNames = [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月",
    ]

    const month = monthNames[date.getMonth()]
    const day = date.getDate()
    const hours = String(date.getHours()).padStart(2, "0")
    const minutes = String(date.getMinutes()).padStart(2, "0")

    return `${month}${day}日 ${hours}:${minutes}`
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
