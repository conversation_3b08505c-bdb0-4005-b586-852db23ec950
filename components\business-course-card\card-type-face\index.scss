/* pages/course/components/deafultCard/index.wxss */
.course-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 32rpx;
  box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(0, 0, 0, 0.04);

  .title-h {
    height: 96rpx;
  }
  .label-text {
    font-size: 24rpx;
    color: #919499;
    margin-top: 8rpx;
  }
  .label-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16rpx;
    .try-listen {
      display: flex;
      align-items: center;
      font-size: 20rpx;
      color: #ff6a4d;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      .img {
        width: 14rpx;
        height: 16rpx;
        display: block;
        margin-right: 8rpx;
      }
      background: rgba(255, 106, 77, 0.05);
    }
  }
  .font-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30rpx;
    width: 100%;
    .left-tex {
      font-size: 22rpx;
      color: #c2c5cc;
    }
  }
}

.face-list-card {
  height: 424rpx;
  background-size: 100%;
  background-position: top center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
  .bg-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    width: 100%;
    display: block;
    border-radius: 16rpx;
    height: 100%;
  }
  .bottom-box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0 0 16rpx 16rpx;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0) 100%
    );
    height: 200rpx;
    padding: 80rpx 24rpx 0 24rpx;
    display: flex;
    align-items: center;
    .left-box {
      flex: 1;
      min-width: 0;
      .title {
        font-size: 32rpx;
        color: #ffffff;
        line-height: 48rpx;
        font-weight: bold;
      }
      .time {
        font-size: 24rpx;
        color: #fff;
        margin-top: 16rpx;
        font-weight: 400;
      }
    }
    .see-btn {
      font-size: 24rpx;
      color: #e60000;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 64rpx;
      padding: 12rpx 32rpx;
    }
  }
}

.face-defaul-card {
  padding: 24rpx;
  .banner-img {
    width: 240rpx;
    height: 148rpx;
    border-radius: 8rpx;
    object-fit: cover;
    margin-right: 24rpx;
  }
}
.study-num {
  font-size: 24rpx;
  color: #c2c5cc;
  margin-right: 16rpx;
}
