/* 新增样式 */

.video-section {
  position: relative;
  overflow: hidden;
}

.video-container {
  position: relative;
  width: 100%;
  margin: 20rpx 0;
}

.video-container.landscape {
  height: 500rpx;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  .video {
    position: relative;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .video-text {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    opacity: 0.8;
  }
}

.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  &-header {
    position: relative;
    height: 400rpx;
    .top-actionbar {
      padding-top: 24rpx;
      padding-left: 24rpx;
      padding-right: 24rpx;
      display: flex;
      justify-content: space-between;
      .button-group {
        display: flex;
        .button {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 64rpx;
          height: 64rpx;
          background: #000000;
          opacity: 0.3;
          border-radius: 50rpx;
          margin: 0 8rpx;
          .icon {
            width: 32rpx;
          }
        }
      }
    }
    .video-bottom-mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
    }
  }
  &-content {
    position: relative;
    z-index: 1;
    flex: 1;
    background-color: #f2f4f7;
    .content-top-mask {
      position: absolute;
      height: 290rpx;
      top: 0;
      width: 100%;
      background: linear-gradient(
        #48665c 0%,
        rgba(72, 102, 92, 0.1) 43%,
        rgba(72, 102, 92, 0) 100%
      );
    }
  }
}

.single-ready-actionbar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding-bottom: 50rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-evenly;
  z-index: 200;
  .actionbar-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;

    &.small-item {
      .image-box {
        width: 100rpx;
        height: 100rpx;
        background-color: rgba(#ffffff, 0.4);
        border-radius: 50rpx 50rpx 50rpx 50rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 24rpx;
        .icon {
          width: 48rpx;
        }
      }
    }
    &.big-item {
      .image-box {
        width: 160rpx;
        height: 160rpx;
        background: linear-gradient(180deg, #ff5252 0%, #e60000 100%);

        box-shadow: 0rpx 16rpx 32rpx 2rpx rgba(230, 0, 0, 0.2),
          inset 0rpx -6rpx 32rpx 2rpx #ff774e;
        border-radius: 100rpx;
        margin-bottom: 16rpx;
        .icon {
          width: 72rpx;
        }
      }
    }
    .image-box {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .text {
      font-size: 24rpx;
      color: #919499;
      line-height: 36rpx;
    }
  }
}

.single-answer-actionbar {
  position: absolute;
  bottom: 50rpx;
  left: 0;
  width: 100%;
  z-index: 1;
  .answer-voice {
    height: 120rpx;
    background: #ffffff;
    border-radius: 100rpx;
    padding: 40rpx;
    padding-left: 60rpx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    .button {
      width: 176rpx;
      height: 80rpx;
      background: linear-gradient(180deg, #f55d39 0%, #ff9157 100%);
      box-shadow: 0rpx 4rpx 8rpx 2rpx rgba(245, 93, 57, 0.2);
      border-radius: 100rpx 100rpx 100rpx 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      color: #ffffff;
      margin-left: 36rpx;
      margin-left: auto;
      &.disabled {
        opacity: 0.5;
      }
    }

    .time {
      width: 110rpx;
      font-size: 28rpx;
      color: #4c6085;
      padding-left: 16rpx;
      box-sizing: border-box;
      font-weight: bold;
    }
    .text-question {
      font-size: 28rpx;
      color: #f5603a;
      padding-left: 18rpx;
    }
    .line {
      .icon {
        height: 32rpx;
        width: auto;
      }
    }
    .voice-icon {
      width: 40rpx;
      font-size: 0;
    }
  }
}
.many-answer-actionbar {
  bottom: 30rpx;
  .answer-voice {
    padding-left: 32rpx;
  }
}

.many-ready-actionbar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  display: flex;
  align-items: flex-end;
  padding-bottom: 30rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
  box-sizing: border-box;
  z-index: 100;
  .actionbar-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;

    .image-box {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 4rpx;
      .icon {
        width: 40rpx;
      }
    }
    .text {
      font-size: 20rpx;
      color: #919499;
      line-height: 36rpx;
    }
  }
  .actionbar-button {
    height: 88rpx;
    background: #e60000;
    border-radius: 44rpx 44rpx 44rpx 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 36rpx;
    width: 100%;
    &.disabled {
      opacity: 0.5;
    }
  }
}

.content-container {
  padding-left: 32rpx;
  padding-right: 32rpx;
  box-sizing: border-box;
}

.red-time-text {
  color: #f94f4f;
}
.question-answer-toast {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

  &-content {
    font-weight: bold;
    font-size: 40rpx;
    color: #3c3d42;
    background-color: rgba($color: #fff, $alpha: 0.3);
    padding: 30rpx 60rpx;
    border-radius: 100rpx;
  }

  &.think-toast {
    bottom: 292rpx;
  }
  .number {
    font-weight: bold;
    font-size: 48rpx;
    color: #f5603a;
    display: inline-block;
    transform: translateY(5rpx);
  }
  &.qeustion-time-toast {
    background-color: transparent;
    bottom: 200rpx;
  }
}
.single-question {
  .qeustion-time-toast {
    bottom: 280rpx;
  }
}

.camera-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 300rpx;
  z-index: 0;
  background-image: linear-gradient(to bottom, rgba(#000, 0), rgba(#000, 0.7));
}

.skin-dark {
  .many-ready-actionbar {
    .image-box {
      background-color: rgba(#000, 0) !important;
    }
  }
  .actionbar-item {
    .image-box {
      background-color: rgba(#000, 0.4);
    }
    .text {
      color: #919499;
    }
  }
  .question-answer-toast-content {
    background-color: rgba(#000000, 0.3);
    color: #fff;
  }
}
.tips-popup {
  &-content {
    display: flex;
    flex-direction: column;
    height: calc(70vh);
    position: relative;
    box-sizing: border-box;
    padding-bottom: 200rpx;
    padding-top: 80rpx;
    padding-left: 40rpx;
    padding-right: 60rpx;
    &-scoll {
      overflow-y: auto;
      flex: 1;
    }
  }
  &-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300rpx;
    z-index: 100;
    background-image: linear-gradient(
      to bottom,
      rgba(242, 244, 247, 0),
      #f2f4f7 10%,
      #f2f4f7 100%
    );
  }
}

.tips-material {
  width: 100vw;
  &-content {
    width: 100%;
    height: 90vh;
    overflow-y: auto;
    padding: 36rpx 40rpx;
    padding-top: 60rpx;
    box-sizing: border-box;

    img {
      max-width: 100%;
    }
  }
}

.material-rich-text {
  color: #3c3d42 !important;
  font-size: 32rpx !important;
  line-height: 2 !important;
}

.ql-image {
  max-width: 100%;
}
.upload-loading-icon {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  animation: rotateAnimation 2s linear infinite;
}
@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.no-data-btn {
  margin: 48rpx auto;
  width: 212rpx;
  height: 72rpx;
  background: #e60000;
  border-radius: 44rpx;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
