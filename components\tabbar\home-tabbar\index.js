// components/tabbar/home-tabbar/index.js
// import { getBaseCampusCache } from "@/utils/cache/baseCache"
const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
const ROUTER = require("@/services/routerManager")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    active: {
      type: String,
      value: "home",
    },
    showAudio: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLoad: false,
    list: [
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_practice.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_practice_select.png",
        pagePath: "pages/practice/home/<USER>",
        pathKey: "practice",
        text: "练习",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_accumulate.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_accumulate_select.png",
        pagePath: "pages/accumulate/list/index",
        pathKey: "accumulate",
        text: "积累",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_drill.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_drill_select.png",
        pagePath: "pages/speech/list/index",
        pathKey: "speech",
        text: "演练",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_improve.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_improve_select.png",
        pagePath: "pages/improve/list/index",
        pathKey: "improve",
        text: "提升",
      },

      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_my.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/tabbar/tab_my_select.png",
        pagePath: "pages/my/home/<USER>",
        pathKey: "my",
        text: "我的",
      },
    ],
    menus: [],
  },
  pageLifetimes: {
    show() {
      this.loadMenus()
    },
    ready() {
      this.loadMenus()
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    loadMenus: async function () {
      try {
        if (!this.data.isLoad) {
          await APP.checkLoadRequest()
        }
      } catch (error) {}

      const baseCache = BASE_CACHE.getBaseCache()
      const provinceMap =
        APP.globalData.serverConfig?.wx_menu_province_button_map
      let menus = provinceMap.default || this.data.list || []
      if (baseCache.province?.key && provinceMap[baseCache.province?.key]) {
        menus = provinceMap[baseCache.province?.key]
      }
      // const menus =
      //   APP.globalData.serverConfig?.wx_menu_button_map || this.data.list || []
      // const menus = this.data.list || []

      if (menus && JSON.stringify(menus) !== JSON.stringify(this.data.menus)) {
        this.setData({ menus, isLoad: true })
      }
    },
    onChange(event) {
      const path = event.currentTarget.dataset.item.pagePath
      ROUTER.switchTab({
        path: "/" + path,
        success: function (res) {},
        fail: function (res) {
          console.log(res)
        },
      })
    },
    openList() {
      const childComponent = this.selectComponent("#tabbar")
      if (childComponent) {
        console.log("调用了没得")
        childComponent.openList()
      }
    },
    closeHidden() {
      this.triggerEvent("closeHidden")
      // this.setData({
      //   hideenShow: false,
      // })
    },
    openHideen() {
      this.triggerEvent("openHideen")
      // this.setData({
      //   hideenShow: true,
      // })
    },
  },
})
