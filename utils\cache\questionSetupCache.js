import { setCache, getCache } from "./core/core"

const CACHE_NAME = "question_setup_record"

// 默认配置
const defaultConfig = {
  isRulePopup: false,
}

/* 核心方法 */
// 设置完整配置
export function setQuestionSetupCache(questionId, data) {
  return setCache(`${CACHE_NAME}.${questionId}`, {
    ...defaultConfig,
    ...data,
  })
}

// 获取完整配置
export function getQuestionSetupCache(questionId) {
  return {
    ...defaultConfig,
    ...getCache(`${CACHE_NAME}.${questionId}`),
  }
}

/* 字段级操作 */
// 弹窗状态
export function setQuestionSetupPopupCache(questionId, isOpened) {
  setCache(`${CACHE_NAME}.${questionId}.isRulePopup`, isOpened)
}

export function getQuestionSetupPopupCache(questionId) {
  return (
    getCache(`${CACHE_NAME}.${questionId}.isRulePopup`) ||
    defaultConfig.isRulePopup
  )
}
