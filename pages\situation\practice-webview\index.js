const API = require("@/config/api")
const APP = getApp()
const UTIL = require("@/utils/util")
const supportDomainList = require("@/config/webviewSupportDomain")
const { parseUrl, previewFile } = require("@/utils/file")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    url: null,
    id: null,
    share_info: {},
    filePath: "",
    fileName: "",
    isNotSupportUrl: "", // 不支持链接的url
    isUnViewText: false, // 是否为不可预览文件类型
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    const { id } = options
    this.pageOptions = options
    let url = `${API.getNewsDetail}?id=${id}`
    if (id) {
      this.setData({
        id,
      })
      this.getOptList(url)
      this.getShareInfo()
    }
  },
  async getShareInfo() {
    const res = await UTIL.request(API.getShareInfo, { id: this.data.id })
    this.setData({
      share_info: res.data,
    })
  },

  /**
   * 获取操作列表并处理 URL
   * @param {string} url - 解码后的 URL
   */
  getOptList(url) {
    const { baseUrl } = parseUrl(url)
    const key = this.findUnitKeyByBaseUrl(supportDomainList, baseUrl)
    if (key === "download" && url.indexOf(".html") <= 0) {
      this.handlePreviewFile(url)
    } else if (key === "webview") {
      console.log("包含")
      this.handleConfigOrWebView(key, url)
    } else {
      this.setData({
        isNotSupportUrl: url,
      })
    }
  },

  /**
   * 处理配置信息或 WebView 页面
   * @param {string} key - 单元键
   * @param {string} url - 解码后的 URL
   */
  handleConfigOrWebView(key, url) {
    const flattenedArray = supportDomainList.reduce(
      (acc, curr) => acc.concat(curr.list),
      []
    )
    const paramList = encodeURIComponent(JSON.stringify(flattenedArray))
    this.setData({
      url: `${url}${url.includes("?") ? "&" : "?"}optList=${paramList}`,
    })
  },

  // 处理预览文件
  handlePreviewFile(url) {
    previewFile(url)
      .then((res) => {
        setTimeout(() => {
          wx.navigateBack({ delta: 1 })
        }, 1000)
      })
      .catch((res) => {
        // 不支持的文件类型
        this.setData({
          filePath: res?.filePath,
          fileName: res?.fileName,
          isUnViewText: true,
        })
      })
  },
  /**
   * 根据基础 URL 查找对应的单元键
   * @param {Array} optList - 配置列表
   * @param {string} baseUrl - 基础 URL
   * @returns {string} 对应的单元键
   */
  findUnitKeyByBaseUrl(optList, baseUrl) {
    for (const item of optList) {
      if (item.list && item.list.length) {
        for (const i of item.list) {
          if (baseUrl.includes(i)) {
            return item.unitKey
          }
        }
      }
    }
    return ""
  },
  //转发
  forward() {
    wx.shareFileMessage({
      filePath: this.data.filePath,
      fileName: this.data.fileName,
      success: () => {
        console.log("转发文档成功")
      },
      fail: (err) => {
        console.log(`转发文档失败`, err)
      },
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  getPageShareParams() {
    let query = this.pageOptions
    return APP.createShareParams({
      title: this.data.share_info.title || "",
      imageUrl: this.data.share_info.image || "",
      path: "/pages/situation/practice-webview/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  // 复制链接
  copyUrl() {
    const link = this.data.isNotSupportUrl

    // 调用微信API复制链接到剪贴板
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: "复制成功",
          duration: 2000,
          icon: "none",
        })
      },
    })
  },
})
