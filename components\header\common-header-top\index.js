// components/home-header/index.js
// const APP = getApp()
// const API = require("@/config/api")
// const UTIL = require("@/utils/util")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
  },
  /**
   * 组件的属性列表
   */
  properties: {
    examTabs: {
      type: Object,
      value: null,
    },
    subjectTabs: {
      type: Object,
      value: null,
    },
    show_white: {
      type: Boolean,
      value: false,
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    examTabs: {
      list: [],
      active: "",
    },
    subjectTabs: {
      list: [],
      active: "",
    },
  },
  attached() {
    // console.log("组件加载了")
    // this.getExamListRequest()
  },
  ready() {
    // console.log("组件布局完成了")
  },
  methods: {
    async getExamListRequest() {
      const res = await UTIL.request(API.getHomeExamList, {})
      const resData = res.data
      this.setData({
        ["examTabs.list"]: resData,
        ["examTabs.active"]: resData[0].exam_key,
      })
      console.log("进来了嘛1")
      await this.getListTag()
      this.changeSubject()
    },
    async getListTag() {
      const res = await UTIL.request(API.getListTag, {
        exam_key: this.data.examTabs.active,
      })
      const resData = res.data
      console.log("进来了嘛", resData)
      this.setData({
        ["subjectTabs.list"]: resData?.list || [],
        ["subjectTabs.active"]: resData?.list[0]?.id || "",
      })
      console.log()
    },
    // 处理考试tab切换
    async handleExamTabsItemEvent(e) {
      const data = e.detail.data
      // this.setData({
      //   ["examTabs.active"]: data.exam_key,
      // })
      // await this.getListTag()
      // this.changeSubject()
      this.triggerEvent("examChage", data)
    },
    // 处理科目tab切换
    handleSubjectTabsItemEvent(e) {
      console.log(e, "12312312")
      const data = e.detail.data
      // this.setData({
      //   ["subjectTabs.active"]: data.id,
      // })
      // this.changeSubject()
      this.triggerEvent("subjectChage", data)
    },
    // 选择切换
    changeSubject() {
      const data = {
        examKey: this.data.examTabs.active,
        tag: this.data.subjectTabs.active,
      }
      console.log(data, "数据")
      this.triggerEvent("customEvent", data)
    },
  },
})
