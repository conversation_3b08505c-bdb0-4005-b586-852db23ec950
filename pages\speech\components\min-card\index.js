const ROUTER = require("@/services/routerManager")
Component({
  properties: {
    list: {
      type: Array,
      value: [],
    },
  },
  data: {},
  methods: {
    // 演练详情
    goResult(e) {
      const data = e.currentTarget.dataset.item
      // 获取当前时间
      const now = new Date()

      // 假设 start_time 是 "2025-03-19 18:43:48"
      const startTimeStr = data.start_time

      // 解析开始时间字符串为 Date 对象
      const startTime = new Date(startTimeStr.replace(" ", "T")) // 将空格替换为 'T' 以符合 ISO 8601 格式
      if (now > startTime) {
        ROUTER.navigateTo({
          path: "/pages/speech/detail/index",
          query: {
            id: data.id,
          },
        })
      } else {
        wx.showToast({
          title: "演练尚未开始",
          icon: "none",
          duration: 2000,
        })
      }
    },
  },
})
