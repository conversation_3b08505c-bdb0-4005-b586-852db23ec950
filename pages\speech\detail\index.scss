page {
  // background: #ebecf0;
}
.drill-detail {
  .top-bg {
    width: 100%;
    height: 540rpx;
    background-size: cover;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding: 90rpx 100rpx 48rpx 40rpx;
    .date-title {
      font-size: 48rpx;
      font-weight: bold;
      color: #ffffff;
    }
    .name-title {
      font-weight: bold;
      font-size: 56rpx;
      color: #ffffff;
      margin-bottom: 35rpx;
    }
    .label-title {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
    .icon-text-area {
      margin-top: 60rpx;
      display: flex;
      align-items: center;
      .icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
      .text {
        font-weight: 400;
        color: rgba(255, 255, 255, 0.9);
        font-size: 28rpx;
      }
      .yellow-text {
        color: #fff039;
      }
    }
  }
  .main-content {
    width: 100%;
    margin-top: -22rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    .top-box {
      width: 100%;
      height: 160rpx;
      background-repeat: no-repeat;
      background-size: cover;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &::after {
        position: absolute;
        content: " ";
        display: block;
        height: 0.5px;
        width: calc(100% - 40px);
        background-color: #ebecf0;
        bottom: 0;
        left: 40rpx;
      }
      .subheading-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        &::after {
          display: block;
          position: absolute;
          content: " ";
          width: 2rpx;
          height: 80rpx;
          background: #ebecf0;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          z-index: 99;
        }
        .subheading-title {
          font-size: 24rpx;
          color: #919499;
        }
        .subheading-c {
          font-size: 32rpx;
          color: #3c3d42;
          margin-top: 24rpx;
          font-weight: bold;
          &.red {
            color: #e60000;
          }
          text {
            font-size: #919499;
            font-weight: normal;
            font-size: 24rpx;
          }
        }
      }
      .title {
        width: 100%;
        padding: 62rpx 40rpx 0 40rpx;
        box-sizing: border-box;
        font-weight: bold;
        font-size: 40rpx;
        color: #22242e;
        text-align: center;
      }
    }
    .desc-area {
      // margin-top: 64rpx;
      padding: 48rpx 40rpx 64rpx 40rpx;
      // border-top: 0.5px solid #ebecf0;
      box-sizing: border-box;
      border-bottom: 12rpx solid #ebecf0;
      .desc-title {
        font-size: 28rpx;
        color: #3c3d42;
        margin-bottom: 24rpx;
      }
      .demand-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        line-height: 43rpx;
        // margin-bottom: 32rpx;
      }
      .people-area {
        display: flex;
        align-items: center;
        justify-content: center;
        // margin-bottom: 64rpx;
        margin-top: 32rpx;
        &-photo {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          // background: red;
          margin-right: -12rpx;
          border: 3rpx solid #fff;
          box-shadow: 0rpx 2rpx 4rpx 2rpx rgba(60, 61, 66, 0.1);
        }
        &-text {
          margin-left: 28rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
        }
      }
      .tips-area {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 34rpx;
        .tips {
          width: 32rpx;
          height: 32rpx;
        }
        .tips-text {
          font-size: 28rpx;
          color: #f7a04a;
          font-weight: 400;
        }
      }
      .btn {
        margin: 0 auto;
        width: 622rpx;
        height: 88rpx;
        background: #4b6ce5;
        box-shadow: 0rpx 8rpx 24rpx 2rpx rgba(75, 108, 229, 0.2);
        border-radius: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 32rpx;
        color: #ffffff;
        margin-top: 64rpx;
      }
    }
  }
  .classmate-box {
    margin-top: 12rpx;
    width: 100%;
    background: #ffffff;
    box-sizing: border-box;
    padding: 64rpx 40rpx 158rpx 40rpx;
    .title-area {
      display: flex;
      align-items: center;
      .classmates-icon {
        width: 8rpx;
        height: 34rpx;
        margin-right: 16rpx;
      }
      .text {
        font-size: 36rpx;
        font-weight: bold;
        color: #22242e;
      }
    }
    .classmates-list {
      margin-top: 48rpx;
      .list-item {
        padding-bottom: 32rpx;
        margin-bottom: 32rpx;
        border-bottom: 2rpx solid #ebecf0;
        &:last-of-type {
          padding-bottom: 0;
          margin-bottom: 0;
          border-bottom: none;
        }
        .main-area {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .left {
            display: flex;
            align-items: center;
            .user-photo {
              width: 56rpx;
              height: 56rpx;
              border-radius: 50%;
              background-color: red;
              margin-right: 16rpx;
            }
            .user-name {
              font-weight: 500;
              font-size: 30rpx;
              color: #3c3d42;
            }
          }
          .right {
            display: flex;
            align-items: center;
            .icon {
              width: 24rpx;
              height: 24rpx;
              margin-left: 8rpx;
            }
            .score {
              font-weight: 500;
              font-size: 26rpx;
              color: #919499;
            }
          }
        }
        .teachers-area {
          margin-top: 24rpx;
          box-sizing: border-box;
          padding: 20rpx 24rpx;
          background: rgba(242, 244, 247, 0.5);
          border-radius: 12rpx;
          display: flex;
          align-items: flex-start;
          .teachers-photo {
            width: 32rpx;
            height: 32rpx;
            border-radius: 50%;
            background-color: yellow;
            margin-right: 8rpx;
          }
          .teachers-desc {
            flex: 1;
            font-size: 24rpx;
            color: #666666;
            font-weight: 400;
            .name {
              color: #e94442;
            }
          }
        }
      }
    }
  }
}

.see-more {
  font-size: 24rpx;
  color: #919499;
  display: flex;
  align-items: center;
  image {
    width: 24rpx;
    height: 24rpx;
    margin-left: 8rpx;
  }
}

.classmate-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rank-list {
  margin-top: 40rpx;
  .rank-list-top {
    display: flex;
    align-items: center;
    padding: 8rpx 0;
    .name {
      font-size: 26rpx;
      color: #c2c5cc;
    }
    .flex1 {
      flex: 1;
      min-width: 0;
      padding-left: 32rpx;
    }
    .w52 {
      width: 52rpx;
    }
  }
  .rank-list-item {
    display: flex;
    align-items: center;
    padding: 32rpx 0;
    border-bottom: 0.5px solid #ebecf0;
    &:last-child {
      border-bottom: 0;
    }
    &.active {
      background: rgba(230, 0, 0, 0.05);
      border-radius: 12rpx;
      border-bottom: 0;
      .rank {
        color: rgba(230, 0, 0, 1);
      }
    }
    .rank {
      width: 52rpx;
      image {
        width: 52rpx;
      }
      font-size: 32rpx;
      font-weight: bold;
      color: #c2c5cc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "DINBold";
      padding-left: 16rpx;
    }
    .rank-student {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      padding-left: 32rpx;
      image {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        margin-right: 16rpx;
        border: 1rpx solid #eaeaea;
      }
      text {
        font-size: 28rpx;
        color: #3c3d42;
      }
      .name-text {
        max-width: 214rpx;
      }
      .me {
        width: 32rpx !important;
        height: 32rpx !important;
        border-radius: 0 !important;
        margin-left: 16rpx;
      }
    }
    .rank-score {
      display: flex;
      align-items: center;
      padding-right: 16rpx;
      .number {
        font-size: 28rpx;
        color: #3c3d42;
        text {
          color: #919499;
        }
      }
      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}

.commont-box {
  padding: 48rpx 40rpx 64rpx 40rpx;
  border-bottom: 12rpx solid #ebecf0;
  .commont-top {
    .title-area {
      display: flex;
      align-items: center;
      margin-bottom: 48rpx;
      .classmates-icon {
        width: 8rpx;
        height: 34rpx;
        margin-right: 16rpx;
      }
      .text {
        font-size: 36rpx;
        font-weight: bold;
        color: #22242e;
      }
    }
  }
}

.point-area {
  &-item {
    margin-bottom: 40rpx;
    .title-area {
      display: flex;
      font-weight: 500;
      font-size: 32rpx;
      color: #3c3d42;
      margin-bottom: 32rpx;
      .report-star {
        width: 24rpx;
        height: 26rpx;
        margin-left: 8rpx;
      }
    }
    .content-area {
      font-weight: 400;
      font-size: 30rpx;
      color: #3c3d42;
      line-height: 54rpx;
    }
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.suggestion-area-item {
  font-weight: 400;
  font-size: 30rpx;
  color: #3c3d42;
  line-height: 54rpx;
  margin-bottom: 40rpx;
  .title {
    font-weight: 500;
    color: #e60000;
    line-height: 48rpx;
    margin-right: 8rpx;
  }
  &:last-of-type {
    margin-bottom: 0;
  }
}

.commont-content {
  height: 340rpx;
  overflow: hidden;
  position: relative;
  .hideen-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    .hui {
      width: 100%;
      height: 370rpx;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0) 0%,
        #ffffff 100%
      );
    }
    .btn-box {
      background: #fff;
      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0rpx 8rpx 24rpx 2rpx rgba(69, 80, 196, 0.2);
        background: #4550c4;
        border-radius: 50rpx;
        width: 622rpx;
        height: 100rpx;
        color: #fff;
      }
    }
  }
}

.bor_0 {
  border-bottom: 0 !important;
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 190rpx;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24rpx 40rpx 34rpx 40rpx;
  .btn {
    margin: 0 auto;
    width: 622rpx;
    height: 88rpx;
    background: #4550c4;
    box-shadow: 0rpx 8rpx 24rpx 2rpx rgba(69, 80, 196, 0.2);
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 32rpx;
    color: #ffffff;
  }
}

.action-bar-box {
  display: flex;
  height: 190rpx;
  z-index: 998;
  position: relative;
  box-sizing: border-box;
}

.action-bar-box-ones {
  display: flex;
  z-index: 998;
}

.action-bar .button {
  width: 100%;
  height: 100%;
  background-color: var(--main-color);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &.hui {
    background: rgba(230, 0, 0, 0.5);
  }
}

.action-bar .action-text {
  width: 290rpx;
  font-size: 26rpx;
  color: #6c6c6c;
}

.report-card-text {
  font-weight: 400;
  font-size: 30rpx;
  color: #3c3d42;
  line-height: 54rpx;
}

.no-permission {
  height: 88rpx;
  border: 2rpx solid #f2f4f7;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f7a04a;
  font-size: 28rpx;
  width: 622rpx;
  margin: 0 auto;
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
  .right {
    width: 24rpx;
    height: 24rpx;
    margin-left: 8rpx;
  }
}

.btn-box {
  background: #fff;
  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0rpx 8rpx 24rpx 2rpx rgba(69, 80, 196, 0.2);
    background: #4550c4;
    border-radius: 50rpx;
    width: 622rpx;
    height: 100rpx;
    color: #fff;
  }
}

.desc-box {
  .desc-title {
    font-size: 28rpx;
    color: #3c3d42;
  }
  .demand-text {
    font-size: 24rpx;
    color: #666666;
    line-height: 43rpx;
  }
}

.mt64 {
  margin-top: 64rpx;
}
