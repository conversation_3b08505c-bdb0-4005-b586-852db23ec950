<view class="my-bg" style="background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/my_bg.png)">
  <view class="user-info">
    <view class="left" wx:if="{{isLogin}}">
      <view class="name">{{userInfo.nickname}}</view>
      <view class="num">{{userInfo.mobile}}</view>
    </view>
    <button-authorize wx:else isBindPhone="{{isLogin}}" bind:onAuthorize="getInfo">
      <view class="name">点击登录</view>
    </button-authorize>
    <view class="user-photo">
      <image wx:if="{{isLogin}}" class="avatar" src="{{userInfo.portrait}}" mode="" />
      <button-authorize wx:else isBindPhone="{{isLogin}}" bind:onAuthorize="getInfo">
        <image class="avatar" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/user_avatar.png" mode="" />
      </button-authorize>
    </view>
  </view>
  <view class="card-list">
    <button-authorize wx:for="{{ optionList }}" wx:key="index" isBindPhone="{{ isLogin }}" bind:onAuthorize="goPage" class="card-list-item" data-data="{{item}}">
      <view class="box-area">
        <view class="left">
          <image class="icon-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/my_icon_{{item.num}}.png" mode="" />
          <view class="name-text">{{item.name}}</view>
        </view>
        <image class="right-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
      </view>
    </button-authorize>
  </view>
  <!-- <view class="card-list mt24">
    <button-authorize wx:for="{{ optionList2 }}" wx:key="index" isBindPhone="{{ index == 1? true:isLogin }}" bind:onAuthorize="goPage" class="card-list-item" data-data="{{item}}">
      <view class="box-area">
        <view class="left">
          <image class="icon-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/my_icon_{{item.num}}.png" mode="" />
          <view class="name-text">{{item.name}}</view>
        </view>
        <image class="right-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
      </view>
    </button-authorize>
  </view> -->
</view>
<home-tabbar active="my" />
<!-- 弹窗客服 -->
<wechat-popup weichatShow="{{weichatShow}}" info="{{weichatCustomerService}}" bindonClickHide="closeWeichatCustomerService" />