.scroll-x {
  white-space: nowrap;

  .scroll-item {
    display: inline-block;
  }
}

.cmpt-tabs {
  .tab-nav {
    box-sizing: border-box;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
      color: transparent;
    }
    &.flex_center {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .nav-item {
      position: relative;
      flex: 1;
      line-height: 44rpx;
      padding-bottom: 14rpx;
      text-align: center;
      font-size: 32rpx;
      color: #666666;
      margin-right: 38rpx;
      padding-left: 10rpx;

      &.active {
        font-weight: bold;
        font-size: 36rpx;
        color: #22242e;
      }
    }
  }
}

.tab-nav1 {
  .nav-item {
    &.active {
      &::after {
        content: "";
        position: absolute;
        bottom: 14rpx;
        left: 4rpx;
        width: 86rpx;
        height: 12rpx;
        background: linear-gradient(
          131deg,
          rgba(230, 0, 0, 0.3) 0%,
          rgba(230, 0, 0, 0) 100%
        );
        border-radius: 8rpx;
      }
    }
  }
}

.search-mr .nav-item {
  margin-right: 90rpx !important;
}
