<navigation-status-bar></navigation-status-bar>
<view class="container top-header-nav">
  <view class="header-item-left">
    <view catch:tap="tapButton">
      <!-- <view class="exam-name" bind:tap="toChangeExam"><text>{{cacheStting.exam.name}} ({{cacheStting.province.name}})</text>
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/common/header_exam_down_arrow.png" mode="widthFix" class="icon" />
      </view> -->
      <view class="exam-name" bind:tap="toChangeExam"><text>{{cacheStting.exam.name}} ({{cacheStting.province.name}})</text>
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/header_exam_down_arrow.png" mode="widthFix" class="icon" />
      </view>
    </view>
  </view>
  <view class="title" wx:if="{{title}}">{{title}}</view>
  <view class="header-item-right">
    <slot></slot>
  </view>
</view>