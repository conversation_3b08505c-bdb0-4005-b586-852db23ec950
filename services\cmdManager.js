const ROUTER = require("@/services/routerManager")
export function cmdManage(cmd, data) {
  console.log(cmd, data)
  const pathList = {
    BookletList: "/pages/situation/sheetList/index",
    BookletListTags: "/pages/situation/sheetList/index",
    Booklet: "/pages/situation/sheetDetail/index",
    NewsTags: "/pages/situation/practiceList/index",
    News: "/pages/situation/practice-webview/index",
    QuestionList: "/pages/situation/subjectList/index",
    AccumulationTags: "/pages/accumulate/list/index",
    Accumulation: "/pages/accumulate/detail/index",
    DrillTags: "/pages/speech/list/index",
    Drill: "/pages/speech/detail/index",
    Recommend: "/pages/improve/list/index",
  }
  const path = pathList[cmd.type]
  if (
    cmd.type === "AccumulationTags" ||
    cmd.type === "DrillTags" ||
    cmd.type === "Recommend"
  ) {
    ROUTER.switchTab({ path, query: cmd.param })
    return
  }

  if (cmd.type === "webpage") {
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: cmd.param,
    })
    return
  }

  if (cmd.type === "customize") {
    // 解析路径和查询参数
    const pathWithParams = cmd.param.path
    const queryStartIndex = pathWithParams.indexOf("?")

    let path = pathWithParams
    let query = {}

    if (queryStartIndex !== -1) {
      // 分离路径和查询参数
      path = pathWithParams.substring(0, queryStartIndex)
      const queryString = pathWithParams.substring(queryStartIndex + 1)

      // 将查询字符串转换为对象
      const pairs = queryString.split("&")
      for (const pair of pairs) {
        const [key, value] = pair.split("=")
        if (key) {
          query[decodeURIComponent(key)] = decodeURIComponent(value || "")
        }
      }
    }

    // 使用 navigateTo 并传入路径和查询参数
    ROUTER.navigateTo({
      path: path,
      query: query,
    })
    return
  }

  if (cmd.type === "miniprogram") {
    const path = cmd.param.path
    if (path.includes("activityCustomer")) {
      ROUTER.navigateTo({ path: "/" + cmd.param.path })
      return
    }
    const params = this.createShareParams({ query: {} })
    const newPath = ROUTER.mergeUrlParams(path, params.query)

    wx.navigateToMiniProgram({
      path: newPath,
      appId: cmd.param.appid,
      envVersion: "release",
      success(res) {
        console.log("跳转小程序成功！", res)
      },
      fail(err) {},
    })
    return
  }
  if (cmd.type === "NewsTags") {
    ROUTER.navigateTo({ path, query: { ...cmd.param, title: data.title } })
    return
  }
  if (path) {
    ROUTER.navigateTo({ path, query: cmd.param })
    return
  }
}
