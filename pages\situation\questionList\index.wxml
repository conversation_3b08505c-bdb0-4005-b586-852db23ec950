<wxs module="utils">
  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };
  module.exports = {
    getNum: getNum
  };
</wxs>
<view class="fixed-box">
  <navigation-bar back="{{true}}" isSticky="{{ true }}">
    <view slot="center" class="tab-title">
      <view class="text-ellipsis-1">{{pageTitle}}</view>
    </view>
  </navigation-bar>
  <view class="search-box" wx:if="{{false}}">
    <view class="search-box-content">
      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_search.png"></image>
      <input type="text" placeholder-style="color:rgba(194, 197, 204, 1)" placeholder="搜索题目" />
    </view>
  </view>
  <block wx:if="{{ isComplete }}">
    <view class="action-bar-box" wx:if="{{questionInfo.verify_record && isLogin}}">
      <remain-time custom-class="page-remain-time" elementTop="{{elementTop}}" verify-record="{{questionInfo.verify_record}}"></remain-time>
    </view>
    <scroll-view style="flex: 1;min-height: 0;" bindscrolltolower="bottomNew" scroll-y wx:if="{{questionInfo.questions && questionInfo.questions.length }}">
      <view class="main-content">
        <view class="practice-list">
          <authorize-phone wx:for="{{questionInfo.questions}}" isBindPhone="{{isLogin}}" item="{{item}}" bind:onAuthorize="toSingeQuestion">
            <view class="practice-list-item">
              <view class="title text-ellipsis-3">
                <text class="title-text" wx:if="{{item.tag_name}}">{{item.tag_name}}</text>
                <text class="title-span">{{item.content}}</text>
              </view>
              <view class="line"></view>
              <view class="card-bottom">
                <view class="information-box" data-item="{{item}}" catch:tap="goLinkUrl">
                  <block wx:if="{{item.link_data.id}}">
                    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/link_icon_by_question.png"></image>
                    <view class="text-information text-ellipsis-1">{{ item.link_data.title }}</view>
                  </block>
                  <block wx:else>
                    <view class="text-information text-ellipsis-1">{{ item.feature_desc||item.q_created_time }}</view>
                  </block>
                </view>
                <block wx:if="{{item.record_data.is_involved == 0}}">
                  <view class="gray-text text-num">未练习</view>
                </block>
                <block wx:else>
                  <block wx:if="{{item.record_data.correct_status == 1}}">
                    <view class="blue-text text-num">{{utils.getNum(item.record_data.score)}}分</view>
                  </block>
                  <block wx:else>
                    <view class="{{item.record_data.correct_status == -1?'gray-text':'blue-text'}} text-num">{{item.record_data.correct_status == -1?'报告生成失败':'报告生成中'}}</view>
                  </block>
                </block>
              </view>

              <!-- <image class="new" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_new.png"></image> -->
            </view>
          </authorize-phone>

        </view>
      </view>
    </scroll-view>
    <tips-default width="280" wx:else text="内容筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
  </block>
</view>