.top-header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 66rpx;
  position: relative;
  padding-left: 32rpx;
  z-index: 999;
  .exam-name {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: rgba(60, 61, 66, 1);
    font-weight: bold;
    // line-height: 44rpx;

    .icon {
      width: 32rpx;
      transform: translateY(2rpx);
      margin-left: 2rpx;
      height: 32rpx;
    }
  }
  .title {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 36rpx;
    color: rgba(34, 36, 46, 1);
    font-weight: bold;
  }
}
