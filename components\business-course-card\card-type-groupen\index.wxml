<view class="course-card" bindtap="goDetail" data-item="{{item}}">
  <!-- 卡片标题 -->
  <course-card-title title="{{item.group_title}}" titlePrefix="{{item.name_prefix}}" subTitle="{{item.group_subtitle}}" />
  <!-- 首页不显示标签 -->
  <view class="label-list" wx:if="{{!isInHome}}">
    <view class="try-listen" wx:if="{{item.free_audition === 1}}">
      <image class="image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/course/coures_listen.png"></image>
      免费试听
    </view>
    <block wx:if="{{item.new_tags.length>0}}">
      <view class="try-listen" wx:for="{{item.new_tags}}" wx:key="index" style="background-color:{{item.color}};color:{{item.font_color}}">
        {{item.tag}}
      </view>
    </block>
  </view>

  <!-- 课程列表时的底部样式 -->
  <view class="fonts-bootom" wx:if="{{!isInHome}}">
    <view class="group-buy" wx:if="{{item.pintuan&&item.pintuan.card_tag}}">
      <course-card-groupon-tags tagInfo="{{item.pintuan.card_tag}}" />
    </view>
    <view class="right-box">
      <!-- 价格文本 -->
      <course-card-price-text usability="pintuan" isQualification="{{!!item.is_bought}}" price="{{item.group_price}}" oldPrice="{{item.single_price}}" isHideDescText="{{true}}" isHasGroupon="{{true}}" isJoinGroupon="{{item.record_info.status===1}}" />
    </view>
  </view>

  <!--首页时的底部样式  -->
  <view class="fonts-bootom" wx:else>
    <!-- 价格文本 -->
    <course-card-price-text usability="pintuan" isQualification="{{!!item.is_bought}}" price="{{item.group_price}}" oldPrice="{{item.single_price}}" isInHome="{{true}}" isHasGroupon="{{true}}" isHideDescText="{{true}}" isJoinGroupon="{{item.record_info.status===1}}" />

    <course-card-button usability="pintuan" isHasGroupon="{{true}}" isQualification="{{!!item.is_bought}}" isJoinGroupon="{{item.record_info.status===1}}"></course-card-button>
  </view>
</view>