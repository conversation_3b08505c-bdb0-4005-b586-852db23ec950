page {
  background: #f2f4f7;
}
.my-bg {
  background-size: 100%;
  width: 100%;
  background-repeat: no-repeat;
  padding-top: 216rpx;
  .user-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    margin-bottom: 64rpx;
    .name {
      font-weight: bold;
      font-size: 48rpx;
      color: #22242e;
      margin-bottom: 16rpx;
      line-height: 48rpx;
    }
    .num {
      font-weight: 400;
      font-size: 26rpx;
      color: #22242e;
      opacity: 0.5;
    }
    .user-photo {
      width: 142rpx;
      height: 142rpx;
      border-radius: 50%;
      background-color: #ffffff;
      box-sizing: border-box;
      padding: 6rpx;
      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }
  }
  .card-list {
    width: calc(100% - 64rpx);
    margin: 0 auto;
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 2rpx rgba(76, 96, 133, 0.05);
    border-radius: 16rpx;
    &-item {
      .box-area {
        width: 100%;
        padding: 52rpx 40rpx;
        box-sizing: border-box;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #ebecf0;
      }
      .left {
        display: flex;
        align-items: center;
        .icon-image {
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }
        .name-text {
          font-weight: 400;
          font-size: 32rpx;
          color: #3c3d42;
        }
      }
      .right-image {
        width: 24rpx;
        height: 24rpx;
      }
      &:last-of-type {
        .box-area {
          border-bottom: none;
        }
      }
    }
  }
  .mt24 {
    margin-top: 24rpx;
  }
}
