const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
Page({
  /**
   * 专项练习！！！！！！！！！！！！！！！！
   */
  data: {
    id: null,
    questionInfo: {},
    isLogin: false, // 用户是否登录
    pageTitle: "",
    page: 1,
    limit: 20,
    isRequest: false,
    isComplete: false,
    isNeedFreshData: {}, // 是否需要刷新数据的页码和题目
    elementTop: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    this.pageOptions = options
    this.setData({
      isLogin: APP.getIsUserLogin(),
      pageTitle: this.pageOptions.name,
    })
    this.getList(1)
  },
  getElementTop() {
    if (!this.data.elementTop) {
      const query = wx.createSelectorQuery() // 创建选择器查询实例
      query
        .select(".action-bar-box") // 选择目标元素（通过 id 或 class）
        .boundingClientRect((rect) => {
          if (rect) {
            this.setData({
              elementTop: rect.top, // 获取元素到顶部的距离
            })
            console.log("元素到顶部的距离:", rect.top)
          }
        })
        .exec() // 执行查询
    }
  },
  onShow() {
    if(this.data.isComplete) {
      this.setData({
        isLogin: APP.getIsUserLogin(),
      })
      if(this.data.isNeedFreshData.page) {
        this.getNewList()
      }else {
        this.getList(1)
      }
    }
  },
  async getNewList() {
    let arr = []
    const { occasion_key, key } = this.pageOptions
    let param = {
      occasion_key,
      key,
      limit: this.data.limit,
      page: this.data.isNeedFreshData.page,
    }
    const res = await UTIL.request(API.getExcerciseList, param)
    arr = res.data.question_list || []
    let data = {}
    if (arr.length) {
      data = arr.find(
        (item) => item.question_id == this.data.isNeedFreshData.id
      )
      data.page = this.data.isNeedFreshData.page
    }
    let oldArr = this.data.questionInfo.questions
    for (let i = 0; i < oldArr.length; i++) {
      if (oldArr[i].question_id == this.data.isNeedFreshData.id) {
        oldArr[i] = data
      }
    }
    this.setData({
      ["questionInfo.questions"]: oldArr,
      ["questionInfo.verify_record"]: res.data?.verify_record,
    })
  },
  async getList(type) {
    const { occasion_key, key } = this.pageOptions
    const param = {
      occasion_key,
      key,
      page: this.data.page,
      limit: this.data.limit,
    }
    const res = await UTIL.request(API.getExcerciseList, param)
    if (res?.error?.code === 0) {
      let obj = {}
      let isRequest = this.data.isRequest
      isRequest = res.data.question_list.length < this.data.limit ? false : true
      let arr = []
      let newArr = res.data.question_list || []
      if (newArr.length) {
        newArr.forEach((item) => {
          item.page = this.data.page
        })
      }
      arr = type == 1 ? newArr : this.data.questionInfo.questions.concat(newArr)
      obj.questions = arr
      obj.verify_record = res.data?.verify_record
      this.setData({
        questionInfo: obj,
        isRequest,
        isComplete: true,
      })
      this.getElementTop()
    }
  },
  goLinkUrl(e) {
    const { item } = e.currentTarget.dataset
    let obj = {
      page: item.page,
      id: item.question_id,
    }
    this.setData({
      isNeedFreshData: obj,
    })
    let linkId = item.link_data?.id
    if (!linkId) {
      return
    }
    let url = ""
    if (item.link_data.type == "question_list") {
      url = "/pages/situation/subjectList/index"
    } else {
      url = "/pages/situation/sheetDetail/index"
    }
    if (getCurrentPages().length > 6) {
      ROUTER.reLaunch({
        path: url,
        query: {
          id: item.link_data.id,
        },
      })
    } else {
      ROUTER.navigateTo({
        path: url,
        query: {
          id: item.link_data.id,
        },
      })
    }
  },

  toSingeQuestion(e) {
    const item = e.detail.item
    let param = this.pageOptions
    param.question_id = item.question_id
    param.type = "exercise"
    let obj = {
      page: item.page,
      id: item.question_id,
    }
    this.setData({
      isNeedFreshData: obj,
    })
    if (item.record_data.is_involved == 0) {
      ROUTER.navigateTo({
        path: "/pages/question/answer-vertical/index",
        query: param,
      })
    } else {
      ROUTER.navigateTo({
        path: "/pages/question/single-detail/index",
        query: param,
      })
    }
  },
  getPageShareParams() {
    let query = { ...this.pageOptions }
    return APP.createShareParams({
      title: this.data.questionInfo.title,
      imageUrl: "",
      path: "/pages/situation/questionList/index",
      query: query,
    })
  },
  // goQuestion() {
  //   let param = this.pageOptions
  //   param.item_no = this.data.questionInfo.id
  //   param.type = "exercise"
  //   ROUTER.navigateTo({
  //     path: "/pages/question/many-preparation/index",
  //     query: param,
  //   })
  // },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    // const params = this.getPageShareParams()
    // delete params.imageUrl
    // const result = APP.createShareParams(params)
    // result.query = ROUTER.convertPathQuery(result.query)
    // return result
  },
  onReachBottom() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getList(2)
  },
  bottomNew() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getList(2)
  },
})
