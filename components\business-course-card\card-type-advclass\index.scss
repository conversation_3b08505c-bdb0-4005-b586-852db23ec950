/* pages/course/components/advclass-card/index.wxss */ /* pages/course/components/deafultCard/index.wxss */
.course-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 32rpx;
  box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(0, 0, 0, 0.04);
  background-size: 100% 100%;
  .titles {
    font-size: 30rpx;
    color: #22242e;
    line-height: 48rpx;
    font-weight: bold;
    .label-tip {
      display: inline;
      background: rgba(255, 243, 240, 1);
      font-size: 22rpx;
      color: #f03916;
      position: relative;
      padding: 5rpx 18rpx 5rpx 48rpx;
      border-radius: 18rpx;
      margin-right: 10rpx;
      transform: translateY(-5rpx);
      .image {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .label-text {
    font-size: 24rpx;
    color: #919499;
    margin-top: 10rpx;
  }
  .fonts-bootom {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .teacher-list {
      display: flex;
      align-items: center;
      margin-top: 30rpx;
      .img {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 16rpx;
      }
    }

    .right-box {
      .money {
        font-size: 22rpx;
        color: #e60000;
        .coupons {
          background: rgba(255, 106, 77, 0.05);
          color: rgba(255, 106, 77, 1);
          border: 0.5px solid rgba(255, 106, 77, 0.3);
          padding: 2rpx 6rpx;
          border-radius: 6rpx;
        }
        .symbol {
          font-size: 24rpx;
          font-weight: bold;
          margin-left: 4rpx;
        }
        .num {
          font-family: "DINBold";
          font-size: 30rpx;
          color: #e60000;
        }
      }
      .free-text {
        font-size: 28rpx;
        color: #e60000;
        font-weight: bold;
        text-align: right;
      }
      .study-num {
        font-size: 16rpx;
        color: #c2c5cc;
        text-align: right;
        margin-top: 4rpx;
      }
    }
  }
  .label-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16rpx;
    .try-listen {
      display: flex;
      align-items: center;
      font-size: 20rpx;
      color: #ff6a4d;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      .img {
        width: 14rpx;
        height: 16rpx;
        display: block;
        margin-right: 8rpx;
      }
      background: rgba(255, 106, 77, 0.05);
    }
  }
}

.start-study {
  font-size: 28rpx;
  color: #e60000;
  font-weight: 500;
  text-align: right;
}
