page {
  background: #f2f4f7;
}
.result {
  position: relative;
  .banner {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
  }
  .tab-title {
    color: #ffffff;
    font-size: 36rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    .uavatar {
      width: 48rpx;
      height: 48rpx;
      padding: 4rpx;
      border-radius: 50%;
      background: #ffffff;
      margin-right: 8rpx;
      .img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }
  }
  .report-area {
    flex: 1;
    .top-area {
      padding-top: 16rpx;
      padding-left: 48rpx;
      padding-bottom: 42rpx;
      .report-multiple-bg {
        width: 360rpx;
        height: 56rpx;
        margin-bottom: 20rpx;
      }
      .top-title {
        width: 520rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #ffffff;
        line-height: 36rpx;
        opacity: 0.7;
      }
    }
    .tab-area-all {
      width: calc(100% - 56rpx);
      padding: 0 28rpx 0 28rpx;
      position: sticky;
      left: 0;
      .tab-area {
        width: 100%;
        display: flex;
        align-items: center;
        white-space: nowrap; /* 防止换行 */
        &-list {
          display: inline-block;
          color: #ffffff;
          font-size: 28rpx;
          font-weight: 500;
          margin-right: 12rpx;
          position: relative;
          box-sizing: border-box;
          padding: 28rpx 26rpx 30rpx 26rpx;
          &:last-of-type {
            margin-right: 0;
          }
          &.active {
            font-size: 36rpx;
            font-weight: bold;
            &::after {
              content: "";
              position: absolute;
              bottom: 14rpx;
              left: 50%;
              width: 40rpx;
              height: 8rpx;
              background: #ffe200;
              border-radius: 4rpx;
              margin-left: -20rpx;
            }
          }
        }
      }
    }
    .multiple-report-area {
      margin-top: 16rpx;
      box-sizing: border-box;
      padding: 0 32rpx 64rpx 32rpx;
      .content-box {
        background: linear-gradient(199deg, #ffe1d1 0%, #ffffff 40%);
        border-radius: 24rpx;
        padding: 32rpx 32rpx 40rpx 32rpx;
        position: relative;
        box-sizing: border-box;

        .good-bg {
          position: absolute;
          top: 0;
          right: 0;
          width: 128rpx;
          height: 128rpx;
        }

        .middle-area {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 32rpx;
          margin-bottom: 64rpx;
          position: relative;

          .num-area {
            margin: 0 40rpx;
          }

          .score-area {
            margin: 0 14rpx 16rpx 14rpx;
            text-align: center;
            flex-direction: column;

            .score-num {
              font-family: "DINbold";
              font-size: 96rpx;
              color: #e60000;
            }
          }

          .score_icon {
            width: 56rpx;
            height: 120rpx;
          }
        }

        .number-box {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          .number-box-text {
            color: #ff8c38;
            font-weight: 500;
            font-size: 30rpx;
            margin-left: 8rpx;
            padding-bottom: 8rpx;
          }
        }
        .number-all {
          font-family: "DINBold";
          font-size: 120rpx;
          color: #ff8c38;
          line-height: 120rpx;
        }
        .small-number {
          font-family: "DINBold";
          font-size: 60rpx;
          color: #ff8c38;
          line-height: 60rpx;
          padding-bottom: 8rpx;
        }

        .full-score {
          font-size: 24rpx;
          color: rgba(102, 102, 102, 0.5);
          text-align: center;
          display: block;
          margin-top: 4rpx;
        }

        .all-score {
          font-size: 22rpx;
          font-weight: 400;
          color: #e60000;
          text-align: center;
          display: block;
        }
        .mb24 {
          margin-bottom: 24rpx;
        }

        .exam-name {
          display: flex;
          .label {
            font-size: 24rpx;
            color: #919499;
            line-height: 36rpx;
            font-weight: 400;
          }

          text {
            font-size: 22rpx;
            line-height: 36rpx;
            font-weight: 400;
            flex: 1;
            min-width: 0;
            color: rgba(102, 102, 102, 1);
          }
        }

        .text-box {
          margin-top: 64rpx;
        }
      }

      .question-card {
        background: linear-gradient(193deg, #ffe2e2 0%, #ffffff 40%);
        border-radius: 24rpx;
        box-sizing: border-box;
        padding: 40rpx 32rpx;
        .top-area-question {
          display: flex;
          justify-content: space-between;
          padding: 0rpx 16rpx 0 8rpx;
          box-sizing: border-box;
          margin-bottom: 40rpx;
          .left {
            margin-top: 36rpx;
            &-item {
              margin-top: 24rpx;
              color: #919499;
              font-size: 24rpx;
              font-weight: 400;
              opacity: 0.8;
            }
          }
          .right {
            .title {
              font-weight: 500;
              font-size: 30rpx;
              color: #e60000;
              display: flex;
              align-items: flex-end;
              margin-bottom: 18rpx;
              .num {
                font-weight: bold;
                font-size: 96rpx;
                line-height: 96rpx;
              }
              .small-num {
                font-weight: bold;
                font-size: 48rpx;
              }
            }
            .content {
              color: #919499;
              font-size: 24rpx;
              font-weight: 400;
              opacity: 0.8;
              text-align: center;
            }
          }
        }
        .echarts-area {
          margin-top: 58rpx;
          .tuli {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 40rpx;
            .red-cube {
              width: 12rpx;
              height: 12rpx;
              background: #e60000;
              border-radius: 2rpx;
            }
            .gray-cube {
              width: 12rpx;
              height: 12rpx;
              background: #919499;
              border-radius: 2rpx;
              margin-left: 48rpx;
            }
            .cube-text {
              font-size: 22rpx;
              color: #919499;
              margin-left: 8rpx;
            }
          }
        }
        #mychart-graph {
          position: relative;
          z-index: -1;
        }
      }
    }
  }
  .multiple-line {
    width: 100%;
    height: 2rpx;
    background: #ebecf0;
  }
}
.score-box {
  padding: 32rpx 0;

  .score-top {
    display: flex;
    align-items: center;
    justify-content: center;

    .dian {
      width: 112rpx;
    }

    .score-top-center {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 24rpx;

      image {
        width: 28rpx;
        margin-right: 4rpx;
      }

      .all-text {
        font-size: 24rpx;
        margin-right: 24rpx;
      }

      .me-rank {
        font-size: 28rpx;
        font-family: "DINBold";
        color: #ec3e33;
        font-weight: bold;
      }

      .line {
        font-size: 20rpx;
        color: rgba(102, 102, 102, 0.3);
        margin: 0 4rpx;
      }

      .all-rank {
        font-size: 28rpx;
        color: rgba(102, 102, 102, 0.5);
        font-family: "DINBold";
      }
    }
  }
}
.score-center-box {
  display: flex;
  align-items: center;

  .score-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    position: relative;
    .class-box-item {
      display: flex;
      align-items: center;
      justify-content: center;
      .image-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .class-text {
        color: #919499;
        font-size: 24rpx;
      }
      .black-text {
        font-size: 28rpx;
        font-weight: 500;
        color: #3c3d42;
        margin-left: 16rpx;
      }
    }

    &:nth-child(2) {
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 2rpx;
        height: 64rpx;
        background: #eee;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      &::before {
        position: absolute;
        display: block;
        content: " ";
        width: 2rpx;
        height: 64rpx;
        background: #eee;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    &:nth-child(4) {
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 2rpx;
        height: 64rpx;
        background: #eee;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .score-item-b {
      font-size: 36rpx;
      font-weight: 500;
      color: #3c3d42;
      font-family: "DINbold";
    }

    .score-item-t {
      font-size: 24rpx;
      color: #919499;
      font-weight: 400;
      margin-bottom: 8rpx;
    }

    .score-item-h {
      color: #e60000;
    }
  }
}
.no-line {
  margin-bottom: 40rpx;
  .score-item {
    &:nth-child(2) {
      &::before {
        position: absolute;
        display: none;
        content: " ";
        width: 2rpx;
        height: 64rpx;
        background: #eee;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 2rpx;
        height: 40rpx;
        background: #eee;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
.timu {
  font-weight: 400;
  font-size: 30rpx;
  color: #3c3d42;
  line-height: 54rpx;
  .tips-text {
    padding: 8rpx 12rpx;
    box-sizing: border-box;
    background: rgba(230, 0, 0, 0.1);
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #e60000;
    margin-right: 24rpx;
  }
}

.my-answer {
  &-text {
    font-weight: 400;
    font-size: 30rpx;
    color: #3c3d42;
    line-height: 54rpx;
    margin-top: 40rpx;
  }
}
.point-area {
  &-item {
    margin-bottom: 40rpx;
    .title-area {
      display: flex;
      font-weight: 500;
      font-size: 32rpx;
      color: #3c3d42;
      margin-bottom: 32rpx;
      .report-star {
        width: 24rpx;
        height: 26rpx;
        margin-left: 8rpx;
      }
    }
    .content-area {
      font-weight: 400;
      font-size: 30rpx;
      color: #3c3d42;
      line-height: 54rpx;
    }
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.suggestion-area-item {
  font-weight: 400;
  font-size: 30rpx;
  color: #3c3d42;
  line-height: 54rpx;
  margin-bottom: 40rpx;
  .title {
    font-weight: 500;
    color: #e60000;
    line-height: 48rpx;
    margin-right: 8rpx;
  }
  &:last-of-type {
    margin-bottom: 0;
  }
}
.advertisement {
  width: 100%;
  border-radius: 16rpx;
  margin-top: 24rpx;
  .advertisement-img {
    width: 100%;
    border-radius: 16rpx;
    vertical-align: top;
  }
}
.report-card-text {
  font-weight: 400;
  font-size: 30rpx;
  color: #3c3d42;
  line-height: 54rpx;
}
.bg-white {
  background: #ffffff !important;
  border-bottom: 1rpx solid #ebecf0;
  z-index: 100;
  .tab-area-list {
    color: #666666 !important;
  }
  .active {
    color: #3c3d42 !important;
  }
}
.black-color {
  color: #22242e !important;
}

.my-answer-text {
  font-weight: 400;
  font-size: 30rpx;
  color: #3c3d42;
  line-height: 54rpx;
  margin-top: 40rpx;
  display: block;
}
.mind-mapping-container {
  width: 100%;
  .mind-mapping-img {
    width: 100%; /* 宽度固定为100% */
    display: block; /* 避免图片下方出现多余的空白 */
  }
}

.no-data-box {
  width: calc(100% - 64rpx);
  margin: 40rpx auto;
  height: 1354rpx;
  background: #ffffff;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  .no-data-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 0;
  }
  .gif-bg-img {
    width: 280rpx;
    height: 280rpx;
    margin-top: 400rpx;
    margin-bottom: 16rpx;
  }
  .blue-text {
    font-weight: 500;
    font-size: 32rpx;
    color: #e60000;
    margin-bottom: 16rpx;
  }
  .gray-text {
    font-weight: 400;
    font-size: 32rpx;
    color: #919499;
  }
  .orange-text {
    font-size: 32rpx;
    color: #f5603a;
    font-weight: 500;
    width: 260rpx;
    line-height: 46rpx;
    text-align: center;
  }
  .error-btn {
    width: 184rpx;
    height: 72rpx;
    background: #e60000;
    border-radius: 44rpx;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 48rpx;
  }
}
.ql-image {
  max-width: 100%;
}
.ai-audio-player {
  display: flex;
  align-items: center;
  height: 112rpx;
  box-shadow: 0rpx 4rpx 20rpx 2rpx rgba(34, 36, 46, 0.08);
  border-radius: 60rpx;
  padding: 8rpx;
  position: relative;
  overflow: hidden;
  z-index: 1;
  flex: 1;
  margin-bottom: 40rpx;
}
.progress-mask {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(140deg, #ffb053 0%, #ff6060 100%);
  opacity: 0.85;
  z-index: -1;
}
.content {
  display: flex;
  align-items: center;
  width: 100%;
  .teacher {
    width: 96rpx;
    height: 96rpx;
    border-radius: 56rpx;
    overflow: hidden;
  }
  .progress-content {
    margin: 0 40rpx;
    flex: 1;
    height: 36rpx;
  }
  .play-button {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
  }
}
.gray-box {
  width: 100%;
  height: 348rpx;
  background-repeat: no-repeat;
  background-size: 100%;
  text-align: center;
  border-radius: 8rpx;
  padding-top: 104rpx;
  box-sizing: border-box;
  &-text {
    font-size: 30rpx;
    color: #3c3d42;
    margin-bottom: 40rpx;
  }
  &-btn {
    margin: 0 auto;
    width: 240rpx;
    height: 72rpx;
    background: #e60000;
    border-radius: 44rpx;
    font-size: 24rpx;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.rank-area-box {
  width: 100%;
  margin-top: 8rpx;
  .title-name {
    display: flex;
    align-items: center;
    padding-right: 32rpx;
    .text {
      font-size: 26rpx;
      color: #c2c5cc;
    }
    .right-text {
      flex: 1;
      text-align: right;
    }
    .mt36 {
      margin-right: 36rpx;
    }
  }
  .rank-list {
    margin-top: 8rpx;
    .rank-list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 0;
      border-bottom: 1rpx solid #ebecf0;
      position: relative;
      .left {
        display: flex;
        align-items: center;
        .rank-num {
          width: 52rpx;
          text-align: center;
          font-family: "DINBold";
          font-size: 32rpx;
          font-weight: bold;
          color: #c2c5cc;
          margin-right: 32rpx;
          .rank-img {
            width: 100%;
          }
        }
        .cred {
          color: #e60000 !important;
        }
        .user-avatar {
          width: 56rpx;
          height: 56rpx;
          border-radius: 50%;
          margin-right: 16rpx;
          border: 1rpx solid #eaeaea;
        }
        .name-text {
          font-size: 28rpx;
          color: #3c3d42;
          max-width: 214rpx;
        }
        .font-bold {
          font-weight: bold !important;
        }
        .img-my {
          width: 32rpx;
          height: 32rpx;
          margin-left: 16rpx;
        }
      }
      .right {
        display: flex;
        align-items: center;
        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
          margin-left: 4rpx;
        }
        .arrow-box {
          width: 24rpx;
          height: 24rpx;
          margin-left: 4rpx;
        }
        .right-text {
          color: #919499;
          font-size: 24rpx;
          margin-left: 4rpx;
        }
        .score-num {
          font-weight: 500;
          font-size: 28rpx;
          color: #3c3d42;
        }
      }
    }
    .no-border {
      border: none;
    }
    .is-my-bg {
      width: calc(100% + 32rpx);
      height: 100%;
      background: rgba(230, 0, 0, 0.05);
      border-radius: 12rpx;
      position: absolute;
      top: 0;
      left: -16rpx;
    }
  }
}

.data-box {
  box-sizing: border-box;
  padding: 32rpx 48rpx;
  .data-box-list-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .list-item {
      height: 286rpx;
      width: 286rpx;
      margin-right: 18rpx;
      margin-bottom: 18rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: #ffffff;
      border-radius: 24rpx;
      box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(34, 36, 46, 0.08);
      .img-icon {
        width: 40rpx;
        height: 40rpx;
        margin-top: 64rpx;
      }
      .text {
        margin-top: 16rpx;
        font-size: 24rpx;
        color: #919499;
        margin-bottom: 32rpx;
      }
      .content-text {
        font-family: "DINBold";
        font-size: 40rpx;
        color: #3c3d42;
        font-weight: 500;
      }
    }
    .mr0 {
      margin-right: 0;
    }
  }
}
.rank-text {
  color: #919499;
  font-size: 24rpx;
  margin-left: 16rpx;
  .red-text {
    font-family: "DINBold";
    font-weight: 500;
    color: #e60000;
    font-size: 28rpx;
  }
}

.rank-table-box {
  .rank-list {
    display: flex;
    align-items: center;
    padding: 32rpx 0;
    background: rgba(242, 244, 247, 0.5);
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    .rank-list-item {
      flex: 1;
      text-align: center;
      font-size: 24rpx;
      color: #3c3d42;
    }
  }
  .header-list {
    padding: 40rpx 0 24rpx 0;
    margin-bottom: 0;
    background: inherit;
    .rank-list-item {
      color: #919499;
    }
  }
  .mb0 {
    margin-bottom: 0;
  }
}
