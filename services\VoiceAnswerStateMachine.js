const MAIN_STATE = Object.freeze({
  INIT: "init",
  OPENING: "opening",
  THINKING: "thinking",
  ANSWERING: "answering",
  FINISHED: "finished",
})

const QUESTION_STATE = Object.freeze({
  PREPARE: "prepare",
  ANSWER: "answer",
  COMPLETE: "complete",
})

class VoiceAnswerStateMachine {
  constructor() {
    // 主状态控制系统
    this._mainState = MAIN_STATE.INIT

    // 题目管理系统
    this._questions = []
    this._currentIndex = 0

    this._mainStateRecord = this._createMainStateDurationMap() // 状态持续时间记录

    // 计时系统
    this._globalTimer = null // 全局定时器ID

    this._useTime = 0 // 使用计时

    this._paused = true // 新增暂停状态

    // 事件回调系统
    this._eventHandlers = {
      mainStateChange: null,
      questionStateChange: null,
      countdown: null,
      alert: null,
      totalTimeout: null,
      error: null,
    }
  }

  /**
   * 初始化状态机
   * @param {Array<Object>} questions - 题目配置数组，每个元素需包含 duration 属性
   * @param {Array<Object>} [userRecord=[]] - 用户历史记录（需包含与题目对应的记录）
   * @param {Object} [options={}] - 配置选项
   * @param {number} [options.totalDuration=0] - 总答题时长（秒）
   * @throws {TypeError} 当 questions 参数不合法时抛出
   */
  init(questions = [], userRecord = [], options = {}) {
    if (!Array.isArray(questions)) {
      throw new TypeError("Questions must be an array")
    }

    this._questions = this._initializeQuestions(questions, userRecord)
    this._currentIndex = 0
    this._startCountdown()
  }

  // 创建主状态持续时间记录映射
  _createMainStateDurationMap() {
    let result = {}
    Object.values(MAIN_STATE).map((key) => {
      result[key] = { time: 0 }
    })

    return result
  }

  // 新增初始化题目方法
  _initializeQuestions(questions, userRecord) {
    return questions
      .map((q, index) => {
        if (typeof q.duration !== "number" || q.duration <= 0) {
          this._emit(
            "error",
            new Error(`Invalid duration for question ${index}`)
          )
          return null
        }

        return {
          ...q,
          state: QUESTION_STATE.PREPARE,
          remainTime: q.duration,
          isTimeEndTips: false, // 有没有时间提示
          isUploading: false, //上传中
          isSubmitCompleted: false, // 上传完成
          isSubmitFailed: false, // 上传失败
          ...(userRecord[index] || {}),
        }
      })
      .filter(Boolean)
  }

  // 重置某一题的状态
  resetQuestion(index) {
    const question = this._questions[index]
    question.remainTime = question.duration
    question.isTimeEndTips = false // 有没有时间提示
    question.isUploading = false
    question.isSubmitCompleted = false
    question.isSubmitFailed = false

    this.questionStateManager.setPrepare(index)
  }

  setQuestionIsTimeEndTips(index, data) {
    const question = this._questions[index]
    question.isTimeEndTips = data

    this._emit("questionStateChange", question, this._currentIndex)
  }

  setQuestionIsUploading(index, data) {
    const question = this._questions[index]
    question.isUploading = data

    this._emit("questionStateChange", question, this._currentIndex)
  }
  setQuestionSubmitFailed(index, data) {
    const question = this._questions[index]
    question.isSubmitFailed = data
    if (data === true) {
      question.isSubmitCompleted = false
    }

    this._emit("questionStateChange", question, this._currentIndex)
  }

  setQuestionSubmitCompleted(index, data) {
    const question = this._questions[index]
    question.isSubmitCompleted = data
    if (data === true) {
      question.isSubmitFailed = false
    }

    this._emit("questionStateChange", question, this._currentIndex)
  }

  // 主流程状态管理 ---------------------------------------------------

  // 题目流程控制 -----------------------------------------------------
  /**
   * 启动指定题目
   * @param {number} index - 题目索引
   * @returns {Promise<void>}
   */
  async startQuestion(index) {
    if (!this._validateQuestionIndex(index)) return

    try {
      this._currentIndex = index
      this.questionStateManager.setPrepare(index)
    } catch (error) {
      this._emit("error", error)
      this._resetQuestion(index)
    }
  }
  /**
   * 设置题目状态（外部调用）
   * @param {number} index - 题目索引
   * @param {string} newState - 新的题目状态
   * @returns {Promise<void>}
   */
  async setQuestionState(index, newState) {
    if (!this._validateQuestionIndex(index)) {
      throw new Error(`Invalid question index: ${index}`)
    }
    const question = this._questions[index]
    await this._switchQuestionState(question, newState)
  }

  // 计时器系统 -------------------------------------------------------
  pause() {
    if (!this._paused) {
      this._paused = true
      this._emit("pauseStateChange", true)
    }
  }

  resume() {
    this._paused = false
    this._emit("pauseStateChange", false)
  }

  _startCountdown() {
    // 启动全局定时器（如果尚未启动）
    if (!this._globalTimer) {
      this._globalTimer = setInterval(() => this._handleGlobalInterval(), 1000)
    }
  }

  /**
   * 处理全局定时器间隔事件
   */
  _handleGlobalInterval() {
    if (this._paused) {
      return // 暂停状态下跳过时间处理
    }
    const question = this.getCurrentQuestion()

    if (this._mainState === MAIN_STATE.ANSWERING) {
      question.time = question.time >>> 0
      question.time++
      question.remainTime = question.duration - question.time
    }

    if (question.state === QUESTION_STATE.PREPARE) {
      question.prepareTime = question.prepareTime >>> 0
      question.prepareTime += 1
    }

    this._mainStateRecord[this._mainState].time =
      (this._mainStateRecord[this._mainState].time || 0) + 1

    this._useTime++
    this._emit("countdown")
    this._emit("questionStateChange", question, this._currentIndex)
  }

  destroy() {
    this._clearAllTimers()
  }

  // 状态转换校验 -----------------------------------------------------

  // 状态管理
  get mainStateManager() {
    let _self = this
    return {
      setInit() {
        return _self._switchMainState(MAIN_STATE.INIT)
      },
      setOpening() {
        return _self._switchMainState(MAIN_STATE.OPENING)
      },
      setThinking() {
        return _self._switchMainState(MAIN_STATE.THINKING)
      },
      setAnswering() {
        return _self._switchMainState(MAIN_STATE.ANSWERING)
      },
      setFinished() {
        return _self._switchMainState(MAIN_STATE.FINISHED)
      },
    }
  }

  // 题目状态管理
  get questionStateManager() {
    let _self = this

    return {
      // 预定义状态方法
      setPrepare(index) {
        return _self.setQuestionState(index, QUESTION_STATE.PREPARE)
      },
      setAnswer(index) {
        return _self.setQuestionState(index, QUESTION_STATE.ANSWER)
      },
      setComplete(index) {
        return _self.setQuestionState(index, QUESTION_STATE.COMPLETE)
      },
    }
  }

  isLastQuestionIndex() {
    // console.log(this._currentIndex, this._questions.length)
    return this._currentIndex === this._questions.length - 1
  }

  getMainStateRecord(key) {
    return this._mainStateRecord[key]
  }
  getMainState() {
    return this._mainState
  }

  async _switchMainState(newState) {
    this._mainState = newState
    this._emit("mainStateChange", newState)
  }

  _switchQuestionState(question, newState) {
    question.state = newState

    this._emit("questionStateChange", question, this._currentIndex)
  }

  // 异常处理机制 -----------------------------------------------------
  _emergencyShutdown() {
    this._clearAllTimers()
    this.mainStateManager.setFinished()
  }

  // 工具方法 --------------------------------------------------------
  _validateQuestionIndex(index) {
    const isValid = index >= 0 && index < this._questions.length
    if (!isValid) {
      this._emit("error", new Error(`Question index ${index} out of range`))
    }
    return isValid
  }

  _clearAllTimers() {
    console.log("停止", this._globalTimer)
    clearInterval(this._globalTimer)
  }

  // 统一事件触发器 --------------------------------------------------
  _emit(event, ...args) {
    const handler = this._eventHandlers[event]
    if (typeof handler === "function") {
      handler(...args)
    }
  }

  // 公共 API -------------------------------------------------------
  getCurrentQuestion() {
    return this._validateQuestionIndex(this._currentIndex)
      ? this._questions[this._currentIndex]
      : null
  }
  get questions() {
    return this._questions
  }

  get mainState() {
    return this._mainState
  }
  get currentIndex() {
    return this._currentIndex
  }
  get useTime() {
    return this._useTime
  }

  // 事件监听器设置方法 ----------------------------------------------
  onMainStateChange(handler) {
    this._eventHandlers.mainStateChange = handler
  }

  onQuestionStateChange(handler) {
    this._eventHandlers.questionStateChange = handler
  }

  onCountdown(handler) {
    this._eventHandlers.countdown = handler
  }

  onError(handler) {
    this._eventHandlers.error = handler
  }
}

exports.VoiceAnswerStateMachine = VoiceAnswerStateMachine

// // 初始化状态机
// 初始化状态机
// const machine = new VoiceAnswerStateMachine()

// // 设置事件监听
// machine.onMainStateChange((state) => console.log("Main State:", state))
// machine.onError((error) => console.error("Error:", error))

// // 初始化题目
// const questions = [
//   { duration: 30, text: "问题1" },
//   { duration: 45, text: "问题2" },
// ]
// machine.init(questions, [], { totalDuration: 180 })

// // 启动流程
// machine.startFlow()
