const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    modelList: [
      {
        bg_url:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/practice_default_bg.png",
        title: "普通模式",
        desc: "显示答题时长，试题内容",
      },
      {
        bg_url:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/practice_mirror_bg.png",
        title: "对镜模式",
        desc: "回答时会开启摄像头，能看到自己",
      },
    ],
    setttingIndex: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const setttingIndex = wx.getStorageSync("isOpenCamera") ? 1 : 0
    this.setData({
      setttingIndex,
    })
  },
  changeSelect(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      setttingIndex: index,
    })
    wx.setStorageSync("isOpenCamera", !!index)
  },
})
