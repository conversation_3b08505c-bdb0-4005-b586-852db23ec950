const APP = getApp()

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    list: {},
    level: {
      type: Number,
      value: 1,
    },
    activeNames: {
      type: Array,
      value: [],
    },
    isLogin: {
      type: Boolean,
      value: true,
    },
    continueKey: String,
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      })
    },
    toggleAccordion(e) {
      const key = e.currentTarget.dataset.item.key
      this.onTriggerChange(key)
    },
    onTriggerChange(key) {
      this.triggerEvent("onChange", {
        key,
      })
    },
    onChildChange(e) {
      const key = e.detail.key
      this.onTriggerChange(key)
    },
    handleChildRightButton(e) {
      const item = e.detail
      this.onRightButton(item)
    },
    handleRightButton(e) {
      const item = e.detail
      console.log(item, "12312312312")
      // this.onRightButton(item)
    },
    goContent(e) {
      const item = e.currentTarget.dataset.item
      console.log(item, "12312312")
      this.onRightButton(item)
    },
    onRightButton(item) {
      console.log(item, "数据呢")
      this.triggerEvent("onRightButton", item)
    },
  },
})
