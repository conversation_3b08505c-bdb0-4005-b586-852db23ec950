<wxs module="utils">
  function getActiveClass(arr, key) {


    if (arr.indexOf(key) >= 0) {
      return 'active'
    }
    return ''
  }

  function getActiveText(arr, obj, isTrue) {
    if (arr.indexOf(obj.key) >= 0) {
      if (isTrue) {
        if (obj.child && obj.child.length) {
          var flag = false
          for (var i = 0; i < obj.child.length; i++) {
            var item = obj.child[i];
            if (item.isTrue) {
              flag = true
            }
          }
          return flag ? '' : '1'
        } else {
          return '1'
        }
        if (!flag) {
          return '1'
        }
      }
    } else {
      if (isTrue) {
        return '1'
      }
    }
    return ''
  }

  module.exports.getActiveClass = getActiveClass;
  module.exports.getActiveText = getActiveText;

</wxs>

<view class="accordion">
  <view class="accordion-item  {{utils.getActiveClass(activeNames,item.key)}} accordion-item-{{level}}" wx:for="{{list}}" wx:key="index">
    <view class="accordion-body">
      <view class="accordion-body-header" catch:tap="toggleAccordion" data-item="{{item}}">
        <block wx:if="{{level===1&&item.child.length}}">
          <image wx:if="{{utils.getActiveClass(activeNames,item.key)}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/structure_collapse_reduce_1.png" mode="widthFix" class="icon" />
          <image wx:else="" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/structure_collapse_add_1.png" mode="widthFix" class="icon" />
        </block>
        <block wx:if="{{level===2&&item.child.length}}">
          <image wx:if="{{utils.getActiveClass(activeNames,item.key)}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/structure_collapse_reduce_1.png" mode="widthFix" class="icon" />
          <image wx:else="" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/structure_collapse_add_1.png" mode="widthFix" class="icon" />
        </block>
        <image wx:if="{{item.child.length==0&&level!=3}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/no-junior.png" mode="widthFix" class="icon" />
      </view>
      <view class="accordion-body-content" catchtap="goContent" data-item="{{item}}">
        <!-- <view class="accordion-left accordion-info" catch:tap="toggleAccordion" data-item="{{item}}">
          <view class="title">{{item.name}}</view>
        </view> -->
        <view class="accordion-left accordion-info">
          <view class="title">{{item.name}}</view>
        </view>
        <view class="accordion-right">
          <!-- <button-authorize isBindPhone="{{isLogin}}" key="{{item}}" bind:onAuthorize="handleRightButton">
            <view class="right-info">
              <text class="text">{{item.right_question_num?item.right_question_num>item.q_amount?item.q_amount:item.right_question_num:0}}/{{item.q_amount}}</text>
              <view class="icon-box">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/structure_pen.png" class="icon" />
              </view>
            </view>
          </button-authorize> -->
          <view class="right-info">
            <text class="text">{{item.right_question_num?item.right_question_num>item.q_amount?item.q_amount:item.right_question_num:0}}/{{item.q_amount}}</text>
            <!-- <text class="text" wx:if="{{ utils.getActiveText(activeNames,item,item.isTrue ) }}">继续做题</text> -->
            <view class="icon-box">
              <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/structure_pen.png" class="icon" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="accordion-children" wx:if="{{item.child}}">
      <home-structure-collapse isLogin="{{isLogin}}" list="{{item.child}}" activeNames="{{activeNames}}" level="{{level+1}}" continueKey="{{ continueKey }}" bind:onChange="onChildChange" bind:onRightButton="handleChildRightButton"></home-structure-collapse>
    </view>
  </view>
</view>