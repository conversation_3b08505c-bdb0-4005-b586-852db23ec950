page {
  box-sizing: border-box;
  background: rgba(242, 244, 247, 1);
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/banner_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-bottom: 64rpx;
}
.main-content {
  padding: 5rpx 32rpx;
}

.rule-box {
  box-sizing: border-box;
  margin-top: 15rpx;
  background: rgba(255, 255, 255, 1);
  padding: 60rpx 40rpx 48rpx 40rpx;
  border-radius: 24rpx;
  position: relative;
  .title {
    font-size: 32rpx;
    color: rgba(60, 61, 66, 1);
    line-height: 48rpx;
    padding-left: 32rpx;
  }
  .rule-comma {
    width: 72rpx;
    height: 72rpx;
    position: absolute;
    top: 20rpx;
  }
  .face-time {
    background: rgba(242, 244, 247, 0.5);
    padding: 32rpx;
    margin-top: 48rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text {
      font-size: 28rpx;
      color: rgba(60, 61, 66, 1);
    }
    .time {
      font-size: 24rpx;
      color: #e60000;
      text {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }

  .question-setting {
    font-size: 24rpx;
    color: rgba(194, 197, 204, 1);
    .question-setting-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .titles {
      margin-top: 32rpx;
      margin-bottom: 24rpx;
    }
    .question-setting-item {
      width: 292rpx;
      height: 158rpx;
      border-radius: 24rpx;
      background: rgba(242, 244, 247, 0.5);
      padding: 24rpx 40rpx;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      .select-bg {
        display: none;
      }
      .icon-select {
        display: none;
      }
      .icon {
        width: 48rpx;
        height: 48rpx;
      }
      text {
        font-size: 28rpx;
        color: rgba(60, 61, 66, 1);
        margin-top: 32rpx;
        display: block;
        position: relative;
        z-index: 2;
      }
      .select {
        display: none;
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        top: 16rpx;
        right: 16rpx;
      }
      &.active {
        background: linear-gradient(180deg, #ef2828 0%, #e60000 100%);
        .icon-select {
          display: block;
          width: 48rpx;
          height: 48rpx;
        }
        .icon {
          display: none;
        }
        text {
          color: #fff;
        }
        .select {
          display: block;
        }
        .select-bg {
          display: block;
          position: absolute;
          width: 158rpx;
          height: 158rpx;
          top: -10rpx;
          right: -20rpx;
        }
      }
    }
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar .button {
  flex: 1;
  height: 100%;
  background-color: var(--main-color);
  font-size: 30rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-collect {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  &.blue {
    text {
      color: #e60000;
    }
  }
  text {
    font-size: 20rpx;
    color: rgba(145, 148, 153, 1);
  }
  image {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
  }
}
.opc-05 {
  opacity: 0.5;
}

.tab-box {
  margin-top: 32rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  .tab-list-area {
    display: flex;
    align-items: center;
    .list-item {
      margin-right: 96rpx;
      color: #666666;
      font-size: 32rpx;
      font-weight: 500;
      line-height: 44rpx;
      position: relative;
      &:last-of-type {
        margin-right: 0;
      }
      &.active {
        color: #22242e;
        font-weight: bold;
        font-size: 32rpx;
        &::after {
          content: "";
          position: absolute;
          bottom: -22rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 6rpx;
          background: #e60000;
          border-radius: 12rpx;
        }
      }
    }
  }
  .zuoda-answer {
    margin-top: 48rpx;
    .progress-list {
      display: flex;
      align-items: flex-start;
      padding-top: 24rpx;
      .num-img {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
        margin-top: 4rpx;
      }
      &-item {
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid #ebecf0;
        .title {
          font-weight: 500;
          font-size: 28rpx;
          color: #3c3d42;
          margin-bottom: 16rpx;
        }
        .desc {
          font-size: 24rpx;
          color: #919499;
        }
      }
    }
  }
  .text-box {
    .text-title {
      font-size: 32rpx;
      color: rgba(60, 61, 66, 1);
      font-weight: bold;
      margin-bottom: 20rpx;
      margin-top: 32rpx;
    }
    text {
      display: block;
      font-size: 26rpx;
      color: #3c3d42;
      line-height: 46rpx;
    }
  }
}
.question-list {
  margin-top: 70rpx;
  .list-card-item {
    box-sizing: border-box;
    padding: 40rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    background: rgba(245, 246, 247, 0.5);
    border-radius: 16rpx;
    .left {
      flex: 1;
      font-size: 26rpx;
      color: #3c3d42;
      font-weight: 400;
      line-height: 40rpx;
      .text-name {
        background: rgba(76, 96, 133, 0.1);
        color: #4c6085;
        font-size: 22rpx;
        line-height: 40rpx;
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
        margin-right: 10rpx;
      }
      .title-span {
        vertical-align: middle; /* 添加这个属性来帮助垂直对齐 */
      }
    }
    .right-arrow {
      width: 24rpx;
      height: 24rpx;
      margin-left: 32rpx;
    }
  }
  .bottom-text {
    width: 100%;
    font-weight: 400;
    font-size: 24rpx;
    color: #c2c5cc;
    text-align: center;
  }
}
.answering-process {
  margin-top: 64rpx;
  padding: 0 32rpx;
  .answering-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #3c3d42;
  }
  .answering-process-box {
    margin-top: 32rpx;
    .process-item {
      position: relative;
      padding-bottom: 32rpx;
      .waiting {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #e60000;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-right: 4rpx;
        }
      }
      &:last-child::after {
        display: none;
      }
      .yuan {
        width: 14rpx;
        height: 14rpx;
        border: 4rpx solid #e60000;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 7rpx;
        background: #fff;
        z-index: 3;
      }
      padding-left: 38rpx;
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 1px;
        height: 100%;
        border-left: 1px dashed rgba(57, 160, 237, 0.2);
        left: 10rpx;
        top: 7rpx;
        z-index: 2;
      }

      .time {
        font-size: 24rpx;
        color: #919499;
        margin-bottom: 24rpx;
      }
      .right-box {
        flex: 1;
        background: rgba(242, 244, 247, 0.5);
        padding: 24rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        .left-font {
          flex: 1;
          min-width: 0;
          padding-right: 30rpx;
        }
        .right-arrow {
          width: 24rpx;
          height: 24rpx;
        }
        .titles {
          font-size: 28rpx;
          color: #3c3d42;
        }

        .time-all {
          font-size: 24rpx;
          color: #919499;
          text {
            color: #666666;
          }
          margin-right: 26rpx;
        }

        .line {
          width: 2rpx;
          height: 24rpx;
          background: #ebecf0;
        }

        .score-all {
          font-size: 24rpx;
          color: #919499;
          margin-left: 26rpx;
          text {
            color: #e60000;
          }
        }

        .font-bottom {
          display: flex;
          align-items: center;
          margin-top: 20rpx;
        }

        .label {
          font-size: 24rpx;
          color: #919499;
          margin-top: 14rpx;
        }
      }
    }
  }
}
