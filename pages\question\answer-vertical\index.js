const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")

const QUESTION_SETUP_CACHE = require("@/utils/cache/questionSetupCache")
const MediaRecorder = require("@/services/MediaRecorder")

const Player = require("@/utils/AudioPlayer")
const {
  VoiceAnswerStateMachine,
} = require("@/services/VoiceAnswerStateMachine")
const { parseQuestionHtml } = require("@/utils/QuestionParseHtml")
const COSUploader = require("@/utils/COSUploader")

let answerInstance = new VoiceAnswerStateMachine()
let mediaRecorder = new MediaRecorder()
let playerInstance = new Player()
let playerBackgroundInstance = new Player()

let PAGE_OPTIONS = {}

let timerManager = {
  submitPollingTimer: null, // 提交轮询计时器
  playQuestionBeforTimer: null, // 题目前置音频计时器
  fileAnalysisTimer: null, // 文件解析计时器

  questionAudioWaitTimer: null, // 题目音频等待计时器
}

let isCameraError = false // 摄像头错误
let isPageUnload = false // 页面是否卸载
Page({
  data: {
    mediaRecords: {},
    currentQuestionId: 1, // 当前题目id
    questionCosMap: {}, // 存储用户的作答文件
    answerStateQuestions: {}, // 所有答题状态
    currentAnswerQuestion: {}, // 当前题目数据
    mainState: "init", // 主状态
    dialogStateManager: {
      isRuleDialog: false, // 显示规则弹窗
      isQuitDialog: false, //  显示退出弹窗
      isDialogUploadFailed: false, // 上传文件失败弹窗
      isDialogUploadRetryFailed: false, // 重试失败弹窗
      isDialogSubmitFailed: false, // 提交失败弹窗
      isDialogSubmitRetryFailed: false, // 重新提交失败弹窗
      isDialogSubmitConfirm: false, // 显示题目确认弹窗
      isDialogTriesLimit: false, // 显示次数超限弹窗

      isDialogRecordAuthorize: false, // 录音授权
      isDialogCameraAuthorize: false, // 相机授权

      isDialogRecordAudioBreak: false, // 录音中断
      isDialogRecordVideoBreak: false, // 视频中断

      isDialogQuestionPlayError: false, // 音频播放中断
      isDialogQuestionPlayFailed: false, // 音频播放重试后失败

      isPopupQuestion: false, // 显示提示层
      isPopupQuestionPlayTime: false, // 显示听题倒计时
      isPopupMaterial: false, // 显示提示层
      isTipsQuestionTimeEnd: false, // 显示题目事件结束
      isTipsThink: false, // 显示思考时长提示
      isTipsQuestionAudioLoading: false, // 显示题目语音加载提示
    },
    questionData: null, // 题目数据
    pageState: {
      useTime: 0, // 用户总用时
      questionSubmitText: "结束后查看评分报告", // 提交时的文案
      hasThinkTips: false, // 有没有显示过思考弹窗
      isOpenCamera: false, // 是否打开摄像头
      answserUploadState: "normal", // 作答上传状态 normal 正常 uploading 上传中 completed 完成 fail 失败

      isUploadFailed: false, // 是否上传失败
      retryUploadcount: 0, // 重新上传次数
      isSubmitFailed: false, // 是否提交失败
      submitFailedCount: 0, // 提交失败的次数
      isUnexpectedSubmit: false, // 是否出现意外提交

      isVideoLoadCompleted: false, // 视频加载完成
    },

    questionPlayBeforeTime: 0, // 显示题目听题倒计时
    questionPlayRecord: {}, // 题目播放记录

    isVideoCanPlay: false, // 视频是否可以播放
    isVideoMuted: false, // 视频是否静音

    questionType: "manyQuestion", //  singleQuestion 单题 manyQuestion 多个题
    manyMode: "look", // listen 听题  look 看题

    currentMaterial: [],
    previewImageUrl: "", // 预览的图片路径
    isCompleted: false, // 页面加载完成
  },

  async onLoad(options) {
    PAGE_OPTIONS = options
    isPageUnload = false
    timerManager.fileAnalysisTimer = null
    APP.globalData.audioService.stop()

    await APP.checkLoadRequest()
    try {
      answerInstance = new VoiceAnswerStateMachine()
      mediaRecorder = new MediaRecorder()
      playerInstance = new Player()
      playerBackgroundInstance = new Player()

      this.cos = new COSUploader({
        Bucket: "interview-1253756937",
        Region: "ap-guangzhou",
        Directory: `/mp_ai_answer/${this.getCurrentDate()}/${
          APP.globalData?.userInfo?.ucid || 0
        }/`,
        requestMethod: () => UTIL.request(API.getCosTempKeys),
      })
    } catch (error) {}

    this.setData({
      "pageState.isOpenCamera": wx.getStorageSync("isOpenCamera"),
      manyMode: options.manyMode === "look" ? "look" : "listen",
    })

    await this.getQuestionDetail()

    // 初始值
    const questionCosMap = {}
    this.data.questionData.question_list.forEach((item) => {
      questionCosMap[item.question_id] = {
        audio: this.getMediaFactory({ time: 0 }),
      }
    })

    this.setData({ questionCosMap })

    this.initMediaRecorder()
    try {
      this.initAnswerStateInstance()
    } catch (error) {
      console.log("绑定异常", error)
    }
  },

  // 获取题目详情
  getQuestionDetail() {
    return UTIL.request(API.getVoiceQuestionDetail, {
      // type: "accumulation",
      // type: "booklet",
      key: PAGE_OPTIONS.key || "",
      occasion_key: PAGE_OPTIONS.occasion_key || "",
      type: PAGE_OPTIONS.type || "",
      item_no: PAGE_OPTIONS.item_no || "",
      question_id: PAGE_OPTIONS.question_id || "",
    })
      .then((res) => {
        // this.setData({
        //   isCompleted: true,
        // })
        // return
        const resData = res.data
        // const resData = tData.data
        // resData.think_allow_time = 5
        // resData.end_record_time = 290
        // resData.answer_min_time = 0 // 最少作答时长
        // resData.answer_allow_time = 30 // 允许作答时长
        // resData.start_listen_time = 5

        resData.question_list.map((item) => {
          item.reference_answer = parseQuestionHtml(item.reference_answer)
          if (item.content_delta.indexOf("image") >= 0) {
            item.hasQuestionImage = true
          } else {
            item.hasQuestionImage = false
          }
        })

        // 按钮禁用时长，使用最大值，避免逻辑冲突
        resData.multiple_allow_time = Math.max(
          resData.start_listen_time,
          resData.multiple_allow_time
        )
        this.setData({
          isCompleted: true,
          questionType:
            resData.question_type === "multiple"
              ? "manyQuestion"
              : "singleQuestion",
          questionData: resData,
          currentQuestionId: resData.question_list[0].question_id,
        })
        // 没有作答次数
        if (resData?.verify_record?.error_code === 7009) {
          this.showDialogTriesLimit()
          return
        }

        setTimeout(() => {
          if (this.data.questionType === "singleQuestion") {
            // 单题且没有点过弹窗
            if (!QUESTION_SETUP_CACHE.getQuestionSetupPopupCache("1")) {
              // this.toBackgroundVideoRule()
              this.showRuleDialog()
            } else {
              // this.toBackgroundVideoRule()
              this.confimRule()
            }

            // 多题模式下，去到规则
          } else {
            this.toBackgroundVideoRule()
          }
        })
      })
      .catch((res) => {
        this.setData({
          isCompleted: true,
        })
        console.log("接口异常", res)
      })
  },
  onHide() {},
  onShow() {},
  getCurrentDate() {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, "0") // 月份从0开始，需要+1
    const day = String(today.getDate()).padStart(2, "0")

    return `${year}-${month}-${day}`
  },
  getBackgroundVideo() {
    return wx.createVideoContext("backgroundVideo")
  },

  // 设置背景播放器进度
  backgroundVideoSeek(time) {
    console.log("设置视频点", time)

    try {
      // 提高进度设置成功率
      setTimeout(() => {
        this.getBackgroundVideo().seek(time)
      }, 300)
    } catch (error) {
      console.log(error)
    }
  },

  setBackgroundVideoPlayState(state) {
    this.setData({
      isVideoCanPlay: state,
    })
  },
  // 背景视频播放
  backgroundVideoPlay() {
    this.setBackgroundVideoPlayState(true)
    this.getBackgroundVideo().play()
  },
  // 跳转到视频规则
  toBackgroundVideoRule() {
    this.backgroundVideoSeek(this.data.questionData.video_info.start_time)
    this.backgroundVideoPlay()
  },
  // 跳转到视频规则
  toBackgroundVideoContent() {
    this.backgroundVideoSeek(this.data.questionData.video_info.middle_time)

    this.backgroundVideoPlay()
  },
  // 监听背景视频播放
  onBackgroundVideoTimeupdate(e) {
    if (!this.data.isVideoCanPlay) {
      this.getBackgroundVideo().pause()
    }

    const videoInfo = this.data.questionData.video_info
    // 播放时间超过一定时长后，开始循环播放
    if (e.detail.currentTime >= videoInfo.end_time) {
      this.toBackgroundVideoContent()
    }

    // 规则读完后自动静音
    if (
      e.detail.currentTime >= videoInfo.middle_time &&
      !this.data.isVideoMuted
    ) {
      this.setData({
        isVideoMuted: true,
      })

      // 多题时，自动进入到下一状态
      if (
        this.data.questionType === "manyQuestion" &&
        this.data.mainState === "init"
      ) {
        this.handleEntryFirstAnswer()
      }
    }
  },
  // 监听背景视频加载完成
  onBackgroundVideoLoadedmetadata(e) {
    console.log("视频加载完成")
    if (this.data.pageState.isVideoLoadCompleted === false) {
      this.setData({
        "pageState.isVideoLoadCompleted": true,
      })
    }
  },
  // 监听背景视频播放出错
  onBackgroundVideoError(e) {
    console.log("视频加载失败")
    console.log(e)
  },
  onCameraError() {
    wx.showToast({
      title: "摄像头启动失败",
      icon: "none",
    })
    this.closeCamera()
  },
  onCameraInitDone() {
    this.setData({
      "pageState.isOpenCamera": true,
    })
  },

  onUnload() {
    isPageUnload = true
    console.log("页面销毁")
    this.closeSubmitPolling()
    clearInterval(timerManager.playQuestionBeforTimer)
    answerInstance.destroy()
    try {
      playerInstance.destroy()
    } catch (error) {}
    try {
      playerBackgroundInstance.destroy()
    } catch (error) {}
    try {
      mediaRecorder.destroy()
    } catch (error) {}
  },

  // 初始化录制
  initMediaRecorder() {
    console.log("初始化音频")

    mediaRecorder.init({
      enableVideo: this.data.isOpenCamera,
      duration: this.data.questionData.answer_allow_time * 1000, // 单位是ms
      // duration: 10 * 1000, // 单位是ms
      onTimeUpdate: (ms, data) => {
        const audioTime = Math.round(ms / 1000)
        const questionId = data.question_id
        const index = this.getQuestionIndex(data.question_id)

        this.setQuestionAudio(questionId, {
          time: audioTime,
        })

        const currentAudio = this.data.questionCosMap[questionId].audio
        const questionData = this.data.questionData

        // 作答中，提示结束将要结束
        if (
          currentAudio.time >=
            questionData.answer_allow_time - questionData.end_record_time &&
          !this.data.currentAnswerQuestion.isTimeEndTips &&
          this.data.currentAnswerQuestion.state === "answer"
        ) {
          answerInstance.setQuestionIsTimeEndTips(index, true)
          this.showTipsQuestionTimeEnd()
        }
      },
      onTimeout: (res) => {
        console.log("已达到最大录制时长", res)
        // wx.showToast({ title: "已达到最大录制时长", icon: "none" })
        this.stopRecording()
      },
      onError(err) {
        const request_params = {
          query: PAGE_OPTIONS,
          err: err,
        }

        // 上报数据
        try {
          console.log("触发 answer_record_error 上报")
          wx.reportEvent("answer_record_error", {
            request_params: JSON.stringify(request_params),
          })
        } catch (error) {
          wx.reportEvent("answer_record_error", {
            request_params: request_params,
          })
        }

        console.log("页面级，监听错误", err)
        isCameraError = true
      },
      onResume: () => {
        this.hideDialogRecordAudioBreak()
      },
      onPause: () => {
        console.log("页面级监听到暂停", this)

        // 单题暂停时直接提交
        if (this.data.questionType === "singleQuestion") {
          this.stopRecording()
          this.setData({
            "pageState.isUnexpectedSubmit": true,
          })
          return
        }

        // 多题根据当前是否打开视频，显示不同弹窗
        if (this.data.pageState.isOpenCamera) {
          this.showDialogRecordVideoBreak()
        } else {
          this.showDialogRecordAudioBreak()
        }
      },
    })
    mediaRecorder.onUpload = this.handleUpload.bind(this)
  },

  // 继续录制
  resumeRecord() {
    mediaRecorder.resumeRecord()
  },

  // 确认规则
  confimRule() {
    console.log("确认规则")
    this.setEntryThinking()
    this.toBackgroundVideoContent()
    this.hideRuleDialog()
  },

  // 初始化答题实例
  initAnswerStateInstance() {
    const questions = this.data.questionData.question_list.map((item) => {
      item.duration = this.data.questionData.answer_remaining_time
      return item
    })

    // 设置事件监听
    answerInstance.onMainStateChange((state) => {
      console.log("z主流程状态:", state)
      if (this.data.mainState !== state) {
        this.setData({
          mainState: state,
        })
      }
    })

    // 监听答题状态变化
    answerInstance.onQuestionStateChange((state) => {
      this.updateAnswerState()
    })

    // 计时监听
    answerInstance.onCountdown(() => {
      this.setData({
        "pageState.useTime": answerInstance.useTime,
      })

      const currentMainStateRecord = answerInstance.getMainStateRecord(
        this.data.mainState
      )

      // 显示思考时间(单题)
      if (
        !this.data.pageState.hasThinkTips &&
        this.data.questionType === "singleQuestion" &&
        this.data.mainState === "thinking" &&
        currentMainStateRecord.time >= this.data.questionData.think_allow_time
      ) {
        this.showTipsThink()
      }
    })

    answerInstance.onError((error) => console.error("Error:", error))
    answerInstance.init(questions, [])

    this.updateAnswerState()
  },

  // 设置进入思考
  setEntryThinking() {
    answerInstance.resume()
    answerInstance.mainStateManager.setThinking()
    this.toBackgroundVideoContent()
  },

  getQuestionIndex(questionId) {
    return (
      this.data.questionData.question_list.findIndex(
        (item) => Number(questionId) === Number(item.question_id)
      ) || 0
    )
  },

  updateAnswerState() {
    const obj = {}
    answerInstance.questions.forEach((item, index) => {
      obj[item.question_id] = item
      obj[item.question_id].index = index
    })
    const currentQuestion = answerInstance.getCurrentQuestion()
    this.setData({
      answerStateQuestions: obj,
      currentAnswerQuestion: currentQuestion,
      currentQuestionId: currentQuestion.question_id,
    })
  },
  // 处理开始作答
  handleStartAnswer: UTIL.throttle(async function () {
    const currentAnswerQuestion = this.data.currentAnswerQuestion
    const questionType = this.data.questionType
    // 多题模式下，小于5s不允许作答
    if (
      questionType === "manyQuestion" &&
      parseInt(currentAnswerQuestion.prepareTime) <=
        this.data.questionData.multiple_allow_time
    ) {
      return
    }

    // 关闭题目提示
    this.hidePopupQuestion()

    // 标记当前音频是可播放的，避免录制中提示播放错误
    this.onPlayPageAudioonPlay(currentAnswerQuestion.question_id)

    // 多题模式下，且开摄像头，需要隐藏题干
    if (questionType === "manyQuestion" && this.data.pageState.isOpenCamera) {
      this.closeQuestionStem()
    }

    //  是否有开启录音授权
    const isRecordPermission = await mediaRecorder.checkRecordPermission()
    if (!isRecordPermission) {
      this.showDialogRecordAuthorize()
      return
    }
    console.log(isRecordPermission)

    // 恢复用户作答计时
    answerInstance.resume()

    // 暂停题目音频
    this.pauseQuestionAudio(currentAnswerQuestion.question_id)
    // 停止题目音频
    playerInstance.stop()

    const index = currentAnswerQuestion.index || 0
    this.startRecording(currentAnswerQuestion.question_id)
      .then(() => {
        answerInstance.startQuestion(index)
        answerInstance.questionStateManager.setAnswer(index)
        answerInstance.mainStateManager.setAnswering()
        this.setData({
          isRecording: true,
        })
      })
      .catch((err) => {
        console.error("启动失败:", err)
        this.setData({
          isRecording: false,
        })
      })
  }),
  // 处理麦克风授权
  handleRecordAuthorize() {
    wx.openSetting({
      success: async () => {
        if (await mediaRecorder.checkRecordPermission()) {
          wx.showToast({
            title: "授权成功",
            icon: "none",
          })
          this.hideDialogRecordAuthorize()
        }
      },
      fail: () => {},
    })
  },
  // 处理录像授权
  handleCameraAuthorize() {
    wx.openSetting({
      success: async () => {
        if (await mediaRecorder.checkCameraPermission()) {
          wx.showToast({
            title: "授权成功",
            icon: "none",
          })
          this.hideDialogCameraAuthorize()
        }
      },
      fail: () => {},
    })
  },
  /**
   * 处理进入第一道题作答的逻辑
   * 根据题目类型和作答模式执行不同的操作
   */
  handleEntryFirstAnswer: UTIL.throttle(function () {
    // 获取当前作答题目的索引，如果未定义则默认为 0
    const index = this.data.currentAnswerQuestion.index || 0
    answerInstance.startQuestion(index)
    console.log(this.data.questionType, this.data.manyMode)

    // 如果当前题目类型是多题模式
    if (this.data.questionType === "manyQuestion") {
      // 设置进入思考状态
      this.setEntryThinking()

      // 如果作答模式是听题模式
      if (this.data.manyMode === "listen") {
        // 关闭题目题干显示
        this.closeQuestionStem()
        // 跳转到背景视频内容
        this.toBackgroundVideoContent()
        // 设置主状态为作答中
        answerInstance.mainStateManager.setAnswering()
        // 播放当前题目的音频
        this.playQuestionAudio(this.data.currentQuestionId)
      }
      // 如果作答模式是看题模式
      if (this.data.manyMode === "look") {
        // 打开题目题干显示
        this.openQuestionStem()
      }
    }

    // 如果当前题目有图片
    if (this.data.currentAnswerQuestion.hasQuestionImage) {
      // 打开题目题干显示
      this.openQuestionStem()
    }
  }),

  pauseQuestionAudio(questionId) {
    playerInstance.pause()
    playerBackgroundInstance.pause()
    this.onPlayPageAudioonPause(questionId)
  },

  async playQuestionAudio(questionId) {
    console.log("触发 playQuestionAudio")

    const currentQuestionPlayRecord =
      this.data.questionPlayRecord[questionId] || {}

    // 播放题目音频
    const question = this.data.answerStateQuestions[questionId]

    // 优化音频加载
    UTIL.request(question.audio_url, {}, "get")

    this.setData({
      [`questionPlayRecord.${questionId}.count`]:
        currentQuestionPlayRecord?.count || 0,
      [`questionPlayRecord.${questionId}.isPlaying`]: true,
      [`questionPlayRecord.${questionId}.isPause`]: false,
      [`questionPlayRecord.${questionId}.isPlayCompleted`]: false,
    })

    // 是否需要播放前置语音
    let isNeedPlayQuestionBeforAudio = true

    // 播放完成过或者次数大于1，则不再播放
    if (
      currentQuestionPlayRecord.isBeforAudioPlayCompleted ||
      Number(currentQuestionPlayRecord?.count) >= 1
    ) {
      isNeedPlayQuestionBeforAudio = false
    }

    //  多题套题时，不播放
    if (
      this.data.questionType === "manyQuestion" &&
      this.data.manyMode === "look"
    ) {
      isNeedPlayQuestionBeforAudio = false
    }

    console.log("触发 playQuestionAudio - 等待前置语音播放完成")

    if (isNeedPlayQuestionBeforAudio) {
      try {
        await this.playQuestionBeforAudio()
        // 更新播放状态
        this.setData({
          [`questionPlayRecord.${questionId}.isBeforAudioPlayCompleted`]: true,
        })
      } catch (error) {
        console.log(error)
      }
    }

    // 更新播放地址缓存
    let audioUrl = question.audio_url
    // let audioUrl = "question.audio_url"

    let errorCount = this.data.questionPlayRecord[questionId].errorCount
    if (errorCount) {
      audioUrl += "?" + errorCount
    }

    this.playQuestionAudioUrl(questionId, audioUrl)
  },

  // 播放器题目语音地址
  playQuestionAudioUrl(questionId, audioUrl) {
    console.log("playQuestionAudioUrl", audioUrl)
    playerInstance.play(audioUrl)
    playerInstance.on(
      "onPause",
      () => this.onPlayPageAudioonPause(questionId),
      true
    )

    playerInstance.on(
      "onPlay",
      () => this.onPlayPageAudioonPlay(questionId),
      true
    )

    playerInstance.on(
      "onEnded",
      () => this.onPlayPageAudioEnded(questionId),
      true
    )
    playerInstance.on(
      "onError",
      () => this.onPlayPageAudioError(questionId),
      true
    )

    this.startOnQuestionAudioWait(questionId)
  },
  // 播放题目音频
  handlePlayQuestion(e) {
    const question = e.detail.question
    this.playQuestionAudio(question.question_id)
  },
  // 暂停题目音频
  handlePauseQuestion(e) {
    const question = e.detail.question
    this.pauseQuestionAudio(question.question_id)
  },

  onPlayPageAudioonPlay(question_id) {
    console.log("监听到题目音频播放")
    this.setData({
      [`questionPlayRecord.${question_id}.isWait`]: false,
      [`questionPlayRecord.${question_id}.isFailed`]: false,
      [`questionPlayRecord.${question_id}.isCanPlay`]: true,
    })
  },

  onPlayPageAudioEnded(question_id) {
    let count = this.data.questionPlayRecord[question_id].count || 0
    count++
    this.setData({
      [`questionPlayRecord.${question_id}.count`]: count,
      [`questionPlayRecord.${question_id}.isPlaying`]: false,
      [`questionPlayRecord.${question_id}.isPause`]: false,
      [`questionPlayRecord.${question_id}.isPlayCompleted`]: true,
      [`questionPlayRecord.${question_id}.isWait`]: false,
    })
  },
  onPlayPageAudioonPause(question_id) {
    this.setData({
      [`questionPlayRecord.${question_id}.isPlaying`]: false,
      [`questionPlayRecord.${question_id}.isPause`]: true,
    })
  },

  onPlayPageAudioError(question_id) {
    console.log("监听到 onPlayPageAudioError")

    // 一旦可以播放了，将不会再抛出异常
    if (this.data.questionPlayRecord[question_id].isCanPlay) {
      console.log("onPlayPageAudioError 监听到了错误")
      return
    }

    this.setData({
      [`questionPlayRecord.${question_id}.isPlaying`]: false,
      [`questionPlayRecord.${question_id}.isPause`]: false,
    })

    let errorCount = this.data.questionPlayRecord[question_id].errorCount || 0
    if (errorCount > 0) {
      this.showDialogQuestionPlayFaild()
      return
    }

    this.showDialogQuestionPlayError()
  },

  startOnQuestionAudioWait(question_id) {
    if (timerManager.questionAudioWaitTimer) {
      return
    }

    // 显示音频等待
    timerManager.questionAudioWaitTimer = setTimeout(() => {
      this.handleQuestionAudioWait(question_id)
    }, this.data.questionData.audio_loading_time)
  },

  handleQuestionAudioWait(question_id) {
    if (!this.data.questionPlayRecord[question_id].isCanPlay) {
      this.setData({
        [`questionPlayRecord.${question_id}.isWait`]: true,
      })
    }
  },

  // 处理重新播放音频
  handleTryPlayAudioButton() {
    // 获取当前正在作答的题目的 ID
    const question = this.data.currentAnswerQuestion
    let quesitonId = question.question_id
    let errorCount = this.data.questionPlayRecord[quesitonId].errorCount || 0

    this.setData({
      [`questionPlayRecord.${quesitonId}.errorCount`]: errorCount + 1,
    })

    this.playQuestionAudio(quesitonId)
    this.hideDialogQuestionPlayError()
  },

  // 获取音频加载失败
  handlePlayAudioFailedButton() {
    const question = this.data.currentAnswerQuestion
    this.setData({
      [`questionPlayRecord.${question.question_id}.isFailed`]: true,
    })

    this.hideDialogQuestionPlayFaild()
    this.openQuestionStem()
  },

  // 播放题目之前的音频
  playQuestionBeforAudio() {
    return new Promise((resolve, reject) => {
      try {
        console.log("触发 playQuestionBeforAudio")
        // 前置音频
        const mp3 = this.data.currentAnswerQuestion.prefix_audio
        let time = 0
        let currentQuestionId = this.data.currentQuestionId
        let questionData = this.data.questionData

        // 多题且非第一题时，插入语音
        if (
          this.data.questionType === "manyQuestion" &&
          currentQuestionId !== questionData.question_list[0].question_id
        ) {
          time = questionData.start_listen_time || 0
        }

        this.setData({
          [`questionPlayRecord.${currentQuestionId}.isPlaying`]: false,
        })

        this.playQuestionBeforAudioCountdown(time).finally(() => {
          this.setData({
            [`questionPlayRecord.${currentQuestionId}.isPlaying`]: true,
            questionPlayBeforeTime: time,
          })
          this.hidePopupQuestionPlayTime()
          console.log("播放开始播放", mp3)
          playerBackgroundInstance.play(
            mp3,
            (data) => {
              resolve()
            },
            (error) => {
              console.log("我播放失败了")
              resolve()
            }
          )
        })
      } catch (error) {
        resolve() //
      }
    })
  },
  playQuestionBeforAudioCountdown(initialTime) {
    return new Promise((resolve) => {
      let time = initialTime
      if (!time || time <= 0) {
        resolve()
        return
      }

      this.setData({ questionPlayBeforeTime: time })
      this.showPopupQuestionPlayTime()

      if (timerManager.playQuestionBeforTimer) {
        return
      }
      timerManager.playQuestionBeforTimer = setInterval(() => {
        if (isPageUnload) {
          clearInterval(timerManager.playQuestionBeforTimer)
          timerManager.playQuestionBeforTimer = null
          return
        }
        time--
        this.setData({ questionPlayBeforeTime: time })

        this.showPopupQuestionPlayTime()
        if (time <= 0) {
          clearInterval(timerManager.playQuestionBeforTimer)
          timerManager.playQuestionBeforTimer = null
          resolve()
        }
      }, 1000)
    })
  },
  // 处理上传
  handleUpload({ type, questionId, url }) {
    this.setData({
      [`mediaRecords.${questionId}.${type}`]: url,
    })
  },

  // 开始录制
  startRecording(questionId) {
    isCameraError = false
    mediaRecorder.setEnableVideo(this.data.pageState.isOpenCamera)
    return mediaRecorder.start(questionId)
  },

  /**
   * 停止录制方法
   * 该方法用于停止当前题目的录制，并处理录制完成后的文件分析和上传操作
   */
  stopRecording: UTIL.throttle(function () {
    // 获取当前正在作答的题目的 ID
    const questionId = this.data.currentQuestionId
    // 根据题目 ID 获取该题目在问题列表中的索引
    const index = this.getQuestionIndex(questionId)
    // 隐藏提交确认对话框
    this.hideDialogSubmitConfirm()
    // 检查该题目是否正在上传，如果正在上传则直接返回
    if (this.data.answerStateQuestions[questionId].isUploading) {
      return
    }
    // 设置录制状态为停止
    this.setData({ isRecording: false })
    // 设置该题目正在上传的状态
    answerInstance.setQuestionIsUploading(index, true)

    // 检查文件分析定时器是否已经存在，如果存在则直接返回
    if (timerManager.fileAnalysisTimer) {
      return
    }

    // 记录开始时间
    const startTime = Date.now()
    let audioGenerated = false
    let audioGeneratedTime = 0

    console.log(
      "开启定时器timerManager.fileAnalysisTimer====================================",
      timerManager.fileAnalysisTimer
    )
    // 设置文件分析定时器，每秒检查一次录制文件是否准备好
    timerManager.fileAnalysisTimer = setInterval(() => {
      // 检查页面是否已经卸载，如果卸载则清除定时器
      if (isPageUnload) {
        clearInterval(timerManager.fileAnalysisTimer)
        timerManager.fileAnalysisTimer = null
        return
      }

      let isComplete = true
      let audio = mediaRecorder.getQuestionAudio(questionId)

      // 如果音频文件存在临时路径
      if (audio?.tempFilePath) {
        // 设置该题目的音频信息
        this.setQuestionAudio(questionId, {
          tempPath: audio.tempFilePath,
          time: Math.round(audio.duration / 1000),
        })
        // 记录音频生成时间
        if (!audioGenerated) {
          audioGenerated = true
          audioGeneratedTime = Date.now()
        }
      } else {
        // 如果音频文件不存在临时路径，则表示录制未完成
        isComplete = false
      }

      // 如果开启了摄像头且没有摄像头错误
      if (this.data.pageState.isOpenCamera && !isCameraError) {
        let video = mediaRecorder.getQuestionVideo(questionId)

        // 如果视频文件存在临时路径
        if (video?.tempVideoPath) {
          // 设置该题目的视频信息
          this.setQuestionVideo(questionId, {
            tempPath: video.tempVideoPath,
            time: Math.round(video.duration / 1000),
          })
        } else {
          // 如果视频文件不存在临时路径，则表示录制未完成
          isComplete = false
        }
      }
      console.log(audio, isComplete, isCameraError)

      // 检查超时情况
      const currentTime = Date.now()

      // 如果60秒内没有生成音频
      // if (!audioGenerated && currentTime - startTime > 60000) {
      //   console.log("音频生成超时")
      //   isCameraError = true
      //   clearInterval(timerManager.fileAnalysisTimer)
      //   timerManager.fileAnalysisTimer = null
      //   return
      // }

      // 如果生成了音频，但60秒内没有生成视频
      if (
        audioGenerated &&
        this.data.pageState.isOpenCamera &&
        currentTime - audioGeneratedTime > 60000
      ) {
        isCameraError = true
      }

      // 如果录制失败了，就重置值
      if (isCameraError) {
        // 设置该题目的视频信息
        this.setQuestionVideo(questionId, {
          tempPath: "",
          time: 0,
        })
      }

      // 如果音频和视频文件都准备好
      if (isComplete) {
        console.log("监听到解析完成")
        // 清除文件分析定时器
        clearInterval(timerManager.fileAnalysisTimer)
        timerManager.fileAnalysisTimer = null
        // 处理录制完成且文件上传完成的逻辑
        this.handleStopRecordingInUploadcompleted(questionId)
      }
    }, 1000)

    // 调用媒体录制器的停止方法
    mediaRecorder
      .stop()
      .then((res) => {})
      .catch((err) => {
        clearInterval(timerManager.fileAnalysisTimer)
        timerManager.fileAnalysisTimer = null

        console.error("停止失败:", err)
        answerInstance.setQuestionIsUploading(index, false)
      })
  }),

  // 处理停止录制且文件上传完成
  handleStopRecordingInUploadcompleted(questionId) {
    console.log("进入 handleStopRecordingInUploadcompleted")
    const index = this.getQuestionIndex(questionId)
    const question = this.data.answerStateQuestions[questionId]

    // 多题时每作答一题就提交
    if (this.data.questionType === "manyQuestion") {
      const isLastQuestionIndex = answerInstance.isLastQuestionIndex()
      this.handleManySubmit(question.question_id, isLastQuestionIndex)

      // 判断是否为最后一题
      if (!isLastQuestionIndex) {
        // 若不是最后一题，开始下一题的作答
        answerInstance.startQuestion(question.index + 1)

        // 如果作答模式是看题模式或者当前题目有图片
        if (
          this.data.manyMode === "look" ||
          this.data.currentAnswerQuestion.hasQuestionImage
        ) {
          // 打开题目题干显示
          this.openQuestionStem()
        } else {
          // 播放当前题目的音频
          this.playQuestionAudio(this.data.currentQuestionId)
          // 关闭题目题干显示
          this.closeQuestionStem()
        }
      }
    }

    // 单题，当做完成
    if (this.data.questionType === "singleQuestion") {
      // 设置单题状态为已完成
      answerInstance.questionStateManager.setComplete(question.index)
      // 暂停作答计时
      answerInstance.pause()
    }
    // 设置当前题目上传状态为未上传
    answerInstance.setQuestionIsUploading(index, false)
  },

  handleStopButton() {
    if (
      this.data.questionCosMap[this.data.currentQuestionId].audio.time <=
      this.data.questionData.answer_min_time
    ) {
      return
    }

    this.showDialogSubmitConfirm()
  },

  // 提交请求
  async sendSubmitRequst(question_id) {
    const question = this.data.answerStateQuestions[question_id]
    const cosData = this.data.questionCosMap[question_id]
    const lastQuestionId = this.data.questionData.question_list[
      this.data.questionData.question_list.length - 1
    ].question_id
    const parms = {
      uuid: this.data.questionData.uuid,
      type: PAGE_OPTIONS.type,
      item_no: PAGE_OPTIONS.item_no || "",
      question_id: question.question_id,
      question_sort: question.question_sort,
      key: PAGE_OPTIONS.key || "",
      occasion_key: PAGE_OPTIONS.occasion_key || "",
      answer_audio: cosData?.audio?.cosPath || "",
      answer_video: cosData?.video?.cosPath || "",
      answer_time: cosData?.audio.time || 0,
      finish_record: question.question_id == lastQuestionId,
      record_answer_time: this.data.pageState.useTime,
      question_type: this.data.questionData.question_type,
    }

    try {
      const data = await UTIL.request(API.submitQuestionItem, parms)

      if (data.data === true) {
        answerInstance.setQuestionSubmitCompleted(question.index, true)
      } else {
        console.log(question.index, this.data.answerStateQuestions)
        answerInstance.setQuestionSubmitFailed(question.index, true)
      }
      return data
    } catch (error) {
      answerInstance.setQuestionSubmitFailed(question.index, true)
      throw error
    }
  },

  // 处理重新提交
  handleRetrySubmit() {
    this.hideDialogSubmitFailed()
    let count = this.data.pageState.submitFailedCount
    if (count >= 2) {
      this.showDialogRetrySubmitFailed()
      return
    }

    this.setData({
      "pageState.submitFailedCount": count + 1,
    })

    if (this.data.questionType === "singleQuestion") {
      this.handleSingleSubmit()
    }
    if (this.data.questionType === "manyQuestion") {
      this.showAnswerUploadLoading()
      this.submitAllFailedQuestion()
      this.startSubmitPolling()
    }
  },

  // 处理重新上传
  handleRetryUpload() {
    this.hideDialogUploadFailed()
    let count = this.data.pageState.retryUploadcount
    if (count >= 2) {
      this.showDialogRetryUploadFailed()
      return
    }
    this.setData({
      "pageState.retryUploadcount": count + 1,
    })

    if (this.data.questionType === "singleQuestion") {
      this.handleSingleSubmit()
    }
    if (this.data.questionType === "manyQuestion") {
      this.showAnswerUploadLoading()
      this.submitAllFailedQuestion()
      this.startSubmitPolling()
    }
  },

  showAnswerUploadLoading() {
    wx.showLoading({
      title: "作答上传中",
    })
    this.setData({
      "pageState.answserUploadState": "uploading",
    })
  },

  // 单题提交
  async handleSingleSubmit() {
    const currentQuestionId = this.data.currentQuestionId
    const answerResultComponent = this.selectComponent(`#answerResult`)
    try {
      answerResultComponent.pause()
    } catch (error) {
      console.log(error)
    }
    wx.showLoading({
      title: "作答上传中",
    })
    try {
      await this.uploadMedia(currentQuestionId)
    } catch (error) {
      wx.hideLoading()
      this.showDialogUploadFailed()
      return
    }

    try {
      const res = await this.sendSubmitRequst(currentQuestionId)
      if (res.data === true) {
        this.toResult()
        return
      }
      wx.hideLoading()

      this.showDialogSubmitFailed()
    } catch (error) {
      wx.hideLoading()
      this.showDialogSubmitFailed()
    }
  },

  // 处理多题提交
  async handleManySubmit(questionId, isLastQuestionIndex) {
    if (isLastQuestionIndex) {
      this.startSubmitPolling()
      this.showAnswerUploadLoading()
    }

    this.uploadMedia(questionId)
      .then((res) => {
        this.sendSubmitRequst(questionId)
      })
      .catch((res) => {})
  },

  // 提交多少失败的题目
  async submitAllFailedQuestion() {
    let faildIdList = []
    for (const id in this.data.questionCosMap) {
      let item = this.data.questionCosMap[id]
      if (
        item.audio?.isUploadFailed ||
        item.video?.isUploadFailed ||
        this.data.answerStateQuestions[id].isSubmitFailed
      ) {
        faildIdList.push(id)
      }
    }
    const uploadList = faildIdList.map((questionId) =>
      this.uploadMedia(questionId).then((res) => {
        return this.sendSubmitRequst(questionId)
      })
    )

    try {
      const result = await Promise.all(uploadList)
      return result
    } catch (error) {
      return error
    }
  },

  // 检查文件上传状态是否有异常
  checkFileUploadState(list) {
    let isFileUploadFailed = false
    let isUploadCompleted = true
    list.forEach((item) => {
      if (item.audio?.isUploadFailed || item.video?.isUploadFailed) {
        isFileUploadFailed = true
      }

      if (item.audio?.tempPath) {
        if (item.audio.isFailed) {
          isFileUploadFailed = true
        }
        if (!item.audio.cosPath) {
          isUploadCompleted = false
        }
      }

      if (item.video?.tempPath) {
        if (item.video.isFailed) {
          isFileUploadFailed = true
        }
        if (!item.video.cosPath) {
          isUploadCompleted = false
        }
      }
    })
    return { isFileUploadFailed, isUploadCompleted }
  },

  // 检查文件提交状态是否有异常
  checkSubmtState() {
    let list = Object.values(this.data.answerStateQuestions)
    let isSubmitFailed = false
    let isSubmitCompleted = true

    list.forEach((item) => {
      if (item.isSubmitFailed) {
        isSubmitFailed = true
      }
      if (!item.isSubmitCompleted) {
        isSubmitCompleted = false
      }
    })
    return { isSubmitFailed, isSubmitCompleted }
  },

  // 开启提交轮询
  startSubmitPolling() {
    if (timerManager.submitPollingTimer) {
      return
    }
    timerManager.submitPollingTimer = setInterval(() => {
      if (isPageUnload) {
        clearInterval(timerManager.submitPollingTimer)
        timerManager.submitPollingTimer = null
        return
      }

      const {
        isFileUploadFailed,
        isUploadCompleted,
      } = this.checkFileUploadState(Object.values(this.data.questionCosMap))

      if (isFileUploadFailed) {
        wx.hideLoading()
        this.showDialogUploadFailed()
        this.closeSubmitPolling()
        this.setData({
          "pageState.isUploadFailed": true,
        })
        return
      }

      if (!isUploadCompleted) {
        return
      }

      const { isSubmitFailed, isSubmitCompleted } = this.checkSubmtState()
      if (isSubmitFailed) {
        wx.hideLoading()
        this.showDialogSubmitFailed()
      }

      if (isSubmitCompleted) {
        this.toResult()
      }
    }, 1000)
  },
  closeSubmitPolling() {
    clearInterval(timerManager.submitPollingTimer)
    timerManager.submitPollingTimer = null
  },
  // 去报告页
  toResult() {
    let path = "/pages/question/result/index"

    if (PAGE_OPTIONS.type === "drill") {
      path = "/pages/speech/report/index"
    }
    ROUTER.redirectTo({
      path: path,
      query: { uuid: this.data.questionData.uuid },
    })
  },
  // 重新作答
  handleRetryAnswer() {
    mediaRecorder.forceStop(this.data.currentQuestionId)
    answerInstance.resetQuestion(this.data.currentAnswerQuestion.index)
    answerInstance.resume()
    this.hideDialogRecordAudioBreak()
    this.hideDialogRecordVideoBreak()
    this.hideTipsQuestionTimeEnd()
    this.setQuestionAudio(this.data.currentQuestionId, {
      tempPath: "",
      cosPath: "",
      time: 0,
      isUploadCompleted: false,
      isUploadFailed: false,
    })
    this.setQuestionVideo(this.data.currentQuestionId, {
      tempPath: "",
      cosPath: "",
      time: 0,
      isUploadCompleted: false,
      isUploadFailed: false,
    })
    mediaRecorder.setQuestionVideo(this.data.currentQuestionId, null)

    const pageState = this.data.pageState

    pageState.isUploadFailed = false // 是否上传失败
    pageState.answserUploadState = "normal" // 作答上传状态 normal 正常 uploading 上传中 completed 完成 fail 失败
    pageState.isUnexpectedSubmit = false
    pageState.retryUploadcount = 0 // 重新作答次数
    pageState.isSubmitFailed = false // 是否上传失败
    pageState.submitFailedCount = 0 // 上传失败的次数

    this.setData({ pageState })
  },

  getMedia(questionId) {
    return this.data.mediaRecords[questionId] || {}
  },

  clearData() {
    this.setData({ mediaRecords: {} })
  },

  onMaterialOpen(e) {
    this.setData({
      currentMaterial: e.detail.material.map((item) => {
        item.text = parseQuestionHtml(item.text)
        return item
      }),
    })
    this.showPopupMaterial()
  },
  // 处理次数超限按钮点击
  handleTriesLimitButton() {
    let customer_config = this.data.questionData.verify_record?.customer_config
    if (customer_config?.url) {
      ROUTER.navigateTo({
        path: "/pages/webview/web/index",
        query: { url: customer_config?.url },
      })
    } else {
      this.toBack()
    }
  },
  showPopupMaterial() {
    this.setData({ "dialogStateManager.isPopupMaterial": true })
  },
  hidePopupMaterial() {
    this.setData({ "dialogStateManager.isPopupMaterial": false })
  },
  changePopupQuestion() {
    if (this.data.dialogStateManager.isPopupQuestion) {
      this.hidePopupQuestion()
    } else {
      this.showPopupQuestion()
    }
  },
  showPopupQuestion() {
    this.setData({ "dialogStateManager.isPopupQuestion": true })
  },
  hidePopupQuestion() {
    this.setData({ "dialogStateManager.isPopupQuestion": false })
  },

  showPopupQuestionPlayTime() {
    this.setData({ "dialogStateManager.isPopupQuestionPlayTime": true })
  },

  hidePopupQuestionPlayTime() {
    this.setData({ "dialogStateManager.isPopupQuestionPlayTime": false })
  },

  showDialogRecordAuthorize() {
    this.setData({ "dialogStateManager.isDialogRecordAuthorize": true })
  },

  hideDialogRecordAuthorize() {
    this.setData({ "dialogStateManager.isDialogRecordAuthorize": false })
  },

  showDialogCameraAuthorize() {
    this.setData({ "dialogStateManager.isDialogCameraAuthorize": true })
  },

  hideDialogCameraAuthorize() {
    this.setData({ "dialogStateManager.isDialogCameraAuthorize": false })
  },

  showRuleDialog() {
    this.setData({ "dialogStateManager.isRuleDialog": true })
  },
  hideRuleDialog() {
    this.setData({ "dialogStateManager.isRuleDialog": false })
    QUESTION_SETUP_CACHE.setQuestionSetupPopupCache("1", true)
  },

  showDialogSubmitConfirm() {
    let text = ""
    if (this.data.questionType === "manyQuestion") {
      const isLastQuestionIndex = answerInstance.isLastQuestionIndex()
      if (isLastQuestionIndex) {
        text = "结束后查看评分报告"
      } else {
        const index = this.getQuestionIndex(this.data.currentQuestionId)
        text = `结束后进入第 ${index + 2} 题`
      }
    } else {
      text = "结束后查看评分报告"
    }

    this.setData({
      "dialogStateManager.isDialogSubmitConfirm": true,
      "pageState.questionSubmitText": text,
    })
  },
  hideDialogSubmitConfirm() {
    this.setData({ "dialogStateManager.isDialogSubmitConfirm": false })
  },

  showDialogRecordAudioBreak() {
    this.setData({
      "dialogStateManager.isDialogRecordAudioBreak": true,
    })
  },
  hideDialogRecordAudioBreak() {
    this.setData({ "dialogStateManager.isDialogRecordAudioBreak": false })
  },
  showDialogRecordVideoBreak() {
    this.setData({
      "dialogStateManager.isDialogRecordVideoBreak": true,
    })
  },
  hideDialogRecordVideoBreak() {
    this.setData({ "dialogStateManager.isDialogRecordVideoBreak": false })
  },

  showDialogQuestionPlayError() {
    this.setData({
      "dialogStateManager.isDialogQuestionPlayError": true,
    })
  },
  hideDialogQuestionPlayError() {
    this.setData({ "dialogStateManager.isDialogQuestionPlayError": false })
  },
  showDialogQuestionPlayFaild() {
    this.setData({
      "dialogStateManager.isDialogQuestionPlayFailed": true,
    })
  },
  hideDialogQuestionPlayFaild() {
    this.setData({ "dialogStateManager.isDialogQuestionPlayFailed": false })
  },

  showDialogTriesLimit() {
    this.setData({
      "dialogStateManager.isDialogTriesLimit": true,
    })
  },
  hideDialogTriesLimit() {
    this.setData({ "dialogStateManager.isDialogTriesLimit": false })
  },

  showTipsThink() {
    this.setData({
      "dialogStateManager.isTipsThink": true,
      "pageState.hasThinkTips": true,
    })
  },

  hideTipsThink() {
    this.setData({ "dialogStateManager.isTipsThink": false })
  },

  showTipsQuestionTimeEnd() {
    this.setData({
      "dialogStateManager.isTipsQuestionTimeEnd": true,
    })
  },
  hideTipsQuestionTimeEnd() {
    this.setData({ "dialogStateManager.isTipsQuestionTimeEnd": false })
  },

  showTipsQuestionAudioLoading() {
    this.setData({
      "dialogStateManager.isTipsQuestionAudioLoading": true,
    })
  },

  hideTipsQuestionAudioLoading() {
    this.setData({ "dialogStateManager.isTipsQuestionAudioLoading": false })
  },

  showQuitDialog() {
    this.setData({
      "dialogStateManager.isQuitDialog": true,
    })
  },
  hideQuitDialog() {
    this.setData({ "dialogStateManager.isQuitDialog": false })
  },

  showDialogUploadFailed() {
    this.setData({
      "dialogStateManager.isDialogUploadFailed": true,
    })
  },
  hideDialogUploadFailed() {
    this.setData({ "dialogStateManager.isDialogUploadFailed": false })
  },

  showDialogRetryUploadFailed() {
    this.setData({
      "dialogStateManager.isDialogUploadRetryFailed": true,
    })
  },
  hideDialogRetryUploadFailed() {
    this.setData({ "dialogStateManager.isDialogUploadRetryFailed": false })
  },

  showDialogSubmitFailed() {
    this.setData({
      "pageState.isSubmitFailed": true,
      "dialogStateManager.isDialogSubmitFailed": true,
    })
  },
  hideDialogSubmitFailed() {
    this.setData({ "dialogStateManager.isDialogSubmitFailed": false })
  },

  showDialogRetrySubmitFailed() {
    this.setData({
      "dialogStateManager.isDialogSubmitRetryFailed": true,
    })
  },
  hideDialogRetrySubmitFailed() {
    this.setData({ "dialogStateManager.isDialogSubmitRetryFailed": false })
  },

  switchCamera() {
    this.hidePopupQuestion()
    if (this.data.pageState.isOpenCamera) {
      this.closeCamera()
    } else {
      this.openCamera()
    }
  },
  async openCamera() {
    //  是否有开启录音授权
    const isCameraPermission = await mediaRecorder.checkCameraPermission()
    if (!isCameraPermission) {
      this.showDialogCameraAuthorize()
      return
    }

    this.setData({ "pageState.isOpenCamera": true })
    wx.setStorageSync("isOpenCamera", true)
  },
  closeCamera() {
    this.setData({ "pageState.isOpenCamera": false })
    wx.setStorageSync("isOpenCamera", false)
  },

  setQuestionAudio(questionId, newAudio = {}) {
    const questionCos = this.data.questionCosMap[questionId] || {}

    // 合并原有的 audio 属性和新的 audio 属性
    questionCos.audio = this.getMediaFactory({
      ...questionCos.audio,
      ...newAudio,
    })

    this.setData({
      [`questionCosMap.${questionId}`]: questionCos,
    })
  },

  setQuestionVideo(questionId, newVideo = {}) {
    const questionCos = this.data.questionCosMap[questionId] || {}

    // 合并原有的 video 属性和新的 video 属性
    questionCos.video = this.getMediaFactory({
      ...questionCos.video,
      ...newVideo,
    })

    this.setData({
      [`questionCosMap.${questionId}`]: questionCos,
    })
  },

  getMediaFactory(obj = {}) {
    const { tempPath, cosPath, time, isUploadCompleted, isUploadFailed } = obj
    return { tempPath, cosPath, time, isUploadCompleted, isUploadFailed }
  },

  /**
   * 上传文件到云存储
   * @param {string} type 文件类型（audio/video）
   * @param {string} path 临时文件路径
   * @returns {Promise<object>} 上传结果对象
   * @private
   */
  _uploadFile(type, path) {
    const upload = () => {
      return this.cos.upload([{ tempFilePath: path }], {
        onProgress: (data, err) => console.log("上传中", data, err),
      })
    }

    // 封装带有重试逻辑的上传操作
    const uploadWithRetry = () => {
      return upload().catch((error) => {
        console.log("上传失败，正在重试...", error)
        return upload()
      })
    }

    // 执行上传并处理结果
    return uploadWithRetry()
      .then((data) => {
        console.log("上传成功", data.Location)
        return { url: `https://${data.Location}`, type, tempPath: path }
      })
      .catch((error) => {
        console.error("最终上传失败:", error)
        throw error
      })
  },

  async uploadMedia(currentQuestionId) {
    const { audio, video } = this.data.questionCosMap[currentQuestionId] || {}

    const uploadAndUpdate = async (type, media, callback) => {
      if (!media?.tempPath || media?.cosPath) return // 如果没有临时路径或已经上传，直接返回

      try {
        const current = await this._uploadFile(type, media.tempPath)
        callback(currentQuestionId, {
          cosPath: current.url,
          isUploadCompleted: !!current.url,
          isUploadFailed: !current.url,
        })
      } catch (error) {
        callback(currentQuestionId, {
          isUploadFailed: true,
        })
        throw error
      }
    }

    // 并行上传音频和视频
    await Promise.all([
      uploadAndUpdate("audio", audio, this.setQuestionAudio.bind(this)),
      uploadAndUpdate("video", video, this.setQuestionVideo.bind(this)),
    ])
  },

  // 打开题干
  openQuestionStem() {
    wx.nextTick(() => {
      this.selectComponent(`#questionStem`)?.open()
    })
  },

  // 关闭题干
  closeQuestionStem() {
    try {
      wx.nextTick(() => {
        this.selectComponent(`#questionStem`)?.close()
      })
    } catch (error) {
      console.log(error)
    }
  },

  // 返回按钮点击
  onBackButton() {
    this.showQuitDialog()
  },
  // 返回
  toBack() {
    if (getCurrentPages().length <= 1) {
      ROUTER.reLaunch({
        path: "/pages/practice/home/<USER>",
      })
      return false
    }
    wx.navigateBack()
  },
  previewImage(e) {
    const item = e.currentTarget.dataset.item
    console.log(item)
    // wx.previewImage({ urls: [item] })
    this.previewComponentImage({ detail: { url: item } })
  },
  previewComponentImage(e) {
    this.setData({
      previewImageUrl: e.detail.url,
    })
    this.selectComponent("#preview").openModal()
  },
  getPageShareParams() {
    let query = JSON.parse(JSON.stringify(PAGE_OPTIONS))
    const shareInfo = this.data.questionData.share_info
    let path = "/pages/question/single-detail/index"
    if (query.type === "exercise" || query.type === "accumulation") {
      path = "/pages/question/single-detail/index"
    }

    if (query.type === "booklet") {
      if (this.data.questionData.question_type === "multiple") {
        query.id = query.item_no
        path = "/pages/situation/sheetDetail/index"
      } else {
        path = "/pages/question/single-detail/index"
      }
    }

    if (query.type === "drill") {
      query.id = query.item_no
      path = "/pages/speech/detail/index"
    }

    return APP.createShareParams({
      title: shareInfo.title || "",
      imageUrl: shareInfo.image || "",
      path,
      query: query,
    })
  },

  onShareAppMessage() {
    return this.getPageShareParams()
  },
})
