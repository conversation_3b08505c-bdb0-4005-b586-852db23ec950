.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 600rpx;
  // height: 336rpx;
  box-sizing: border-box;
  border-radius: 24rpx;
  background-color: #fff;
  &-content {
    padding: 80rpx 30rpx;
  }
  .title {
    font-size: 32rpx;
    color: #22242e;
    text-align: center;
    font-weight: bold;
  }
  .sub-title {
    font-size: 28rpx;
    color: #919499;
    text-align: center;
    margin-top: 24rpx;
  }
  .bottom-box {
    display: flex;
    align-items: center;
    padding: 0 48rpx;
    justify-content: space-between;
    padding-bottom: 48rpx;
    margin: 0 -20rpx;
    .item {
      margin: 0 20rpx;
      flex: 1;
      font-size: 28rpx;
      padding: 32rpx 0;
      box-sizing: border-box;
      width: 224rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #eaeaea;
      border-radius: 16rpx;
      &:last-child {
        color: #fff;
        background: #e60000;
      }
    }
  }
}
