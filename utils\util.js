const { request } = require("./request")

// 发起网络请求
exports.request = request

// 获取文件名称及后缀
exports.extractFileNameAndExtension = function (filePath) {
  if (!filePath) {
    return ""
  }
  const segments = filePath.split("/")
  const fileNameWithExtension = segments[segments.length - 1]

  const lastDotIndex = fileNameWithExtension.lastIndexOf(".")
  if (lastDotIndex !== -1) {
    const fileName = fileNameWithExtension.slice(0, lastDotIndex)
    const fileExtension = fileNameWithExtension.slice(lastDotIndex + 1)
    const fullName = `${fileName}.${fileExtension}`
    return fullName
  } else {
    return fileNameWithExtension
  }
}

// 将对象的Value 格式化为指定格式
exports.formatObjectValue = function (object, keyNameArray, formatCall) {
  keyNameArray.forEach(function (keyName) {
    object[`new_${keyName}`] = formatCall(object[keyName])
  })
  return object
}

exports.formatSingleNumber = function (number) {
  return number <= 9 ? "0" + number : number
}

// 格式化日期
exports.formatDate = function (time) {
  function formatSingleNumber(number) {
    return number <= 9 ? "0" + number : number
  }
  const date = new Date(time * 1000),
    year = date.getFullYear(),
    month = formatSingleNumber(date.getMonth() + 1),
    day = formatSingleNumber(date.getDate()),
    hour = formatSingleNumber(date.getHours()),
    minute = formatSingleNumber(date.getMinutes()),
    second = formatSingleNumber(date.getSeconds()),
    md = month + "月" + day + "日",
    ms = minute + ":" + second,
    hm = hour + ":" + minute,
    hms = hour + ":" + ms

  return {
    year,
    month,
    day,
    hour,
    minute,
    second,
    md,
    ms,
    hm,
    hms,
  }
}

// 格式化时间戳
exports.formatTimeStamp = function (time) {
  function formatSingleNumber(number) {
    return number <= 9 ? "0" + number : number
  }
  time = time >>> 0
  let hour = parseInt(time / 3600),
    minute = parseInt((time % 3600) / 60),
    second = parseInt(time % 60),
    ms = formatSingleNumber(minute) + ":" + formatSingleNumber(second),
    hms = formatSingleNumber(hour) + ":" + ms
  return {
    hour,
    minute,
    second,
    ms,
    hms,
  }
}

/**
 * 节流函数
 * @param {*} fn 是我们需要包装的事件回调
 * @param {*} wait 是每次推迟执行的等待时间
 */
exports.throttle = function (fn, wait = 3000) {
  let last
  let timer
  return function () {
    let context = this,
      args = arguments
    let now = new Date()

    if (last && now - last < wait) {
      // 如果在指定的时间内，则重置定时器
      clearTimeout(timer)
      timer = setTimeout(function () {
        last = now
        // fn.apply(context, args);
      }, wait)
    } else {
      // 如果超过了指定的时间，则直接执行函数
      last = now
      fn.apply(context, args)
    }
  }
}

/**
 * 防抖函数
 * @param {*} fn 是我们需要包装的事件回调
 * @param {*} wait 是每次推迟执行的等待时间
 */
export function debounce(fn, wait = 3000) {
  let timeout
  return function () {
    const context = this,
      args = arguments
    clearTimeout(timeout)
    timeout = setTimeout(function () {
      fn.apply(context, args)
    }, wait)
  }
}

exports.add = function (num1, num2) {
  return Number(((num1 * 100 + num2 * 100) / 100).toFixed(2))
}

// 定义一个函数来获取当前时间戳，精确到秒
exports.getCurrentTimestampInSeconds = function () {
  return Math.floor(Date.now() / 1000)
}

// 清除价格零
exports.clearPriceZero = (old) => {
  //拷贝一份 返回去掉零的新串
  let newstr = old
  //循环变量 小数部分长度
  var leng = old.length - old.indexOf(".") - 1
  //判断是否有效数
  if (old.indexOf(".") > -1) {
    //循环小数部分
    for (let i = leng; i > 0; i--) {
      //如果newstr末尾有0
      if (
        newstr.lastIndexOf("0") > -1 &&
        newstr.substr(newstr.length - 1, 1) == 0
      ) {
        var k = newstr.lastIndexOf("0")
        //如果小数点后只有一个0 去掉小数点
        if (newstr.charAt(k - 1) == ".") {
          return newstr.substring(0, k - 1)
        } else {
          //否则 去掉一个0
          newstr = newstr.substring(0, k)
        }
      } else {
        //如果末尾没有0
        return newstr
      }
    }
  }
  return old
}

// 提取html中的图片路径
exports.extractHtmlImageUrl = (html) => {
  let rgx_lable = /<img.*?(jpg|png)/g,
    matchList = html.match(rgx_lable) || [],
    arr = []
  arr = matchList.map(function (lable) {
    let urlIndex = lable.indexOf('src="')
    return lable.slice(urlIndex + 5, lable.length)
  })
  return arr
}

/**
 * 通用数组转对象函数，支持多种模式
 * @param {Array} array - 输入数组
 * @param {Object} options - 配置选项
 * @param {string} options.key - 键的属性名（如 'id'）
 * @param {string} options.value - 值的属性名（可选，默认为整个对象）
 * @returns {Object} 转换后的对象
 */
exports.arrayToObjectAdvanced = (array, options = {}) => {
  const { key, value } = options
  return array.reduce((acc, item, index) => {
    let currentKey, currentValue

    if (key && value) {
      currentKey = item[key]
      currentValue = item[value]
    } else if (key) {
      currentKey = item[key]
      currentValue = item
    } else {
      // 默认处理键值对数组
      if (Array.isArray(item) && item.length === 2) {
        currentKey = item[0]
        currentValue = item[1]
      } else {
        throw new Error("无效的数组结构，需提供 key 或 value 参数")
      }
    }

    acc[currentKey] = currentValue
    return acc
  }, {})
}
