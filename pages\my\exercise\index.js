const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isComplete: false,
    isChangeOver: false,
    activeIndex: 1,
    listHeight: null,
    tabList: [
      {
        id: 1,
        title: "套题",
        dataList: [],
        page: 1,
        limit: 20,
        isRequest: true,
      },
      {
        id: 2,
        title: "单题",
        dataList: [],
        page: 1,
        limit: 20,
        isRequest: true,
      },
    ],
    subjectTabs: {},
    isNeedFreshData: {}, // 是否需要刷新数据的页码和题目
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    await this.getList(1)
  },
  getListHeight() {
    if (this.data.listHeight) {
      return
    }
    const query = wx.createSelectorQuery()
    query
      .select(".main-content") // 替换为你的类名
      .boundingClientRect((rect) => {
        if (rect) {
          let heightText = "height: calc(100vh - " + rect.top + "px)"
          this.setData({
            listHeight: heightText,
          })
          console.log("距离顶部高度: ", rect.top) // 输出距离顶部的高度
        }
      })
      .exec()
  },
  async getNewList() {
    let arr = []
    let tabArr = this.data.tabList
    let activeIndex = tabArr.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    const param = {
      page: this.data.isNeedFreshData.page,
      limit: tabArr[activeIndex].limit,
    }
    let url =
      this.data.activeIndex == 2
        ? API.getMyExerciseRecordList
        : API.getMyRecordBookletList
    const res = await UTIL.request(url, param)
    arr = this.data.activeIndex == 2 ? res.data.record_list : res.data.list
    let data = {}
    if (arr.length) {
      data = arr.find(
        (item) => item.record_id == this.data.isNeedFreshData.record_id
      )
      data.page = this.data.isNeedFreshData.page
    }
    let oldArr = tabArr[activeIndex].dataList
    for (let i = 0; i < oldArr.length; i++) {
      if (oldArr[i].record_id == this.data.isNeedFreshData.record_id) {
        oldArr[i] = data
      }
    }
    tabArr[activeIndex].dataList = oldArr
    this.setData({
      tabList: tabArr,
    })
  },

  async getList(type) {
    let tabArr = this.data.tabList
    let activeIndex = tabArr.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    let url =
      this.data.activeIndex == 2
        ? API.getMyExerciseRecordList
        : API.getMyRecordBookletList
    const param = {
      page: tabArr[activeIndex].page,
      limit: tabArr[activeIndex].limit,
    }
    const res = await UTIL.request(url, param)
    let list = this.data.activeIndex == 2 ? res.data.record_list : res.data.list
    let isRequest = tabArr[activeIndex].isRequest
    isRequest = list.length < tabArr[activeIndex].limit ? false : true
    let arr = []
    let newArr = list
    if (newArr.length) {
      newArr.forEach((item) => {
        item.page = tabArr[activeIndex].page
      })
    }
    arr = type == 1 ? newArr : tabArr[activeIndex].dataList.concat(newArr)
    tabArr[activeIndex].dataList = arr
    tabArr[activeIndex].isRequest = isRequest
    this.setData({
      tabList: tabArr,
      isComplete: true,
      isChangeOver: true,
    })
    this.getListHeight()
  },

  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      activeIndex: index,
    })
    let activeIndex = this.data.tabList.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    if (!this.data.tabList[activeIndex].dataList.length) {
      this.getList(1)
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (
      this.data.activeIndex &&
      this.data.isComplete &&
      this.data.isNeedFreshData.page
    ) {
      this.getNewList()
    }
  },
  goPractice() {
    ROUTER.switchTab({ path: "/pages/practice/home/<USER>" })
  },
  goDetail(e) {
    const { data } = e.currentTarget.dataset
    ROUTER.navigateTo({
      path: "/pages/situation/sheetDetail/index",
      query: {
        id: data.id,
      },
    })
  },

  toSingeQuestion(e) {
    const { data } = e.currentTarget.dataset
    let obj = {
      page: data.page,
      record_id: data.record_id,
    }
    this.setData({
      isNeedFreshData: obj,
    })
    let query = {}
    let path = ""
    if (data.correct_status == -2) {
      path = "/pages/situation/sheetDetail/index"
      query.id = data.id
    } else {
      query.record_id = data.record_id
      path = "/pages/question/result/index"
    }

    if (this.data.activeIndex == 2) {
      query.to_question_id = data.question_id
    }
    ROUTER.navigateTo({
      path,
      query,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onScrollToLower() {
    let tabArr = this.data.tabList
    let activeIndex = tabArr.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    if (!tabArr[activeIndex].isRequest) {
      return
    }
    tabArr[activeIndex].page = tabArr[activeIndex].page + 1
    this.setData({
      //改变页码
      accumulateTab: tabArr,
    })
    this.getList(2)
  },
})
