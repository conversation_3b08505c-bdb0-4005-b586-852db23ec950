<tabbar-header show_white="{{show_white}}" back="{{true}}"></tabbar-header>
<view class="{{hideenShow?'oh-hideen':''}}">
  <block wx:if="{{tabShow}}">
    <block wx:if="{{examTabs.active}}">
      <view wx:if="{{examTabs.active && examTabs.list.length>1}}" class="tab-list {{show_white?'bgf':''}}" style="{{stickyHeight}}">
        <home-exam-tabs list="{{examTabs.list}}" active='{{examTabs.active}}' bind:clickItem="handleExamTabsItemEvent"></home-exam-tabs>
      </view>
      <view class="main-content" wx:if="{{isComplete}}">
        <view class="speech-box" wx:if="{{ speechData.online.length && speechData.online[0].drill_list.length>0 }}">
          <speech-home-card card="{{speechData.online[0].drill_list[0]}}" isLogin="{{isLogin}}" catch:joinSpeech="joinSpeech" catch:onHistoryButton="toHistoy" catch:onDetail="goDetail" catch:onJoinButton="handleJoinButton" catch:openWeichat="openWeichatCustomerService" />
        </view>
        <view class="deafult-box" wx:if="{{speechData.online.length ==0 && speechData.offline.length>0}}">
          <image class="deafult-bg" mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/speech_deafult.png"></image>
          <view class="pr-box">
            <image mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/speech_no.png"></image>
            <view class="text">面试PK赛筹备中，敬请期待</view>
          </view>
        </view>
        <block wx:if="{{speechData.offline.length>0}}">
          <view class="list-area" wx:for="{{speechData.offline}}" wx:key="index">
            <view class="top-title-area" wx:if="{{item.drill_list.length>0}}">
              <view class="title">{{item.tag_title}}</view>
              <view class="top-title-area-right" catchtap="seeMore" data-index="{{index}}" wx:if="{{item.has_more}}">
                <view>查看全部</view>
                <image class="image-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
              </view>
            </view>
            <block wx:if="{{ item.drill_list.length>0 }}">
              <min-card list="{{item.drill_list}}"></min-card>
            </block>
          </view>
        </block>
        <tips-default wx:if="{{speechData.online.length==0 && speechData.offline.length==0 }}" text="演练活动筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
      </view>
    </block>
    <tips-default wx:else text="演练活动筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
  </block>
</view>
<!-- 弹窗客服 -->
<wechat-popup weichatShow="{{weichatShow}}" info="{{weichatCustomerService}}" bindonClickHide="closeWeichatCustomerService" />
<!-- 底部导航栏 -->
<home-tabbar active="speech" bind:closeHidden="closeHidden" bind:openHideen="openHideen" />