// components/nav-tab/index.js
Component({
  /**
   * 组件的属性列表
   */
  options: {
    multipleSlots: true, //在组件定义时的选项中启用多slot支持
    addGlobalClass: true,
  },
  properties: {
    list: {
      type: Array,
      value: [],
    },
    active: {
      type: String,
      value: "",
    },
    className: {
      type: String,
      value: ''
    }
  },
  observers: {
    tabsIndex: function (key) {
      this.updateTabsItemOffset(key)
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    tabsItemOffset: null,
    scrollLeft: 0,
    renderNumber: 0,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    updateTabsItemOffset(key) {
      let _self = this
      let query = wx.createSelectorQuery().in(this)
      query.select("#" + key).boundingClientRect()
      query.selectViewport().scrollOffset()
      query.exec(function (res) {
        if (res[0]) {
          _self.computeTabsItemOffset(res[0])
        }
      })
    },
    computeTabsItemOffset(itemOffset) {
      let _self = this,
        renderNumber = _self.data.renderNumber

      if (this.boxLeft) {
        itemOffset.left = itemOffset.left - this.boxLeft

        _self.setData({
          tabsItemOffset: itemOffset,
          scrollLeft: itemOffset.left,
          renderNumber: 1,
        })
        return
      }
      let query = wx.createSelectorQuery().in(this)
      query.select(".tab-nav-box").boundingClientRect()
      query.selectViewport().scrollOffset()
      query.exec(function (res) {
        let boxLeft = res[0].left
        _self.boxLeft = boxLeft
        _self.computeTabsItemOffset(itemOffset)
      })
    },

    // tabs nav 点击事件
    clickTabsItem: function (e) {
      let data = e.currentTarget.dataset.data
      this.triggerEvent("clickItem", {
        data,
      })
    },
  },
})