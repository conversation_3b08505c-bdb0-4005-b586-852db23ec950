<wxs module="utils2">
  function formatTime(input) {
    // 尝试将输入转换为整数
    var seconds = parseInt(input, 10);

    // 如果秒数为0，直接返回"00:00"
    if (seconds == 0) {
      return "00:00";
    }

    var hours = Math.floor(seconds / 3600);
    var minutes = Math.floor((seconds % 3600) / 60);
    seconds = Math.floor(seconds % 60);

    // 使用padZero确保每部分都是两位数
    var formattedHours = padZero(hours);
    var formattedMinutes = padZero(minutes);
    var formattedSeconds = padZero(seconds);

    // 根据是否有小时来决定返回的格式
    if (hours > 0) {
      // 如果有时，则格式化为 "XX:XX:XX"
      return formattedHours + ":" + formattedMinutes + ":" + formattedSeconds;
    } else {
      // 如果没有小时，则仅格式化为 "XX:XX"
      return formattedMinutes + ":" + formattedSeconds;
    }
  }

  function padZero(number) {
    return number < 10 ? "0" + number : "" + number;
  }

  function handleList(list) {
    // 如果列表长度大于3，则返回前三个元素；否则返回整个列表
    return list.length > 3 ? list.slice(0, 3) : list;
  }

  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };

  function hexToRgba(hex, alpha) {
    // 手动移除可能存在的 #
    var cleanHex = "";
    for (var i = 0; i < hex.length; i++) {
      if (hex[i] !== '#') {
        cleanHex += hex[i];
      }
    }

    // 如果是三位数的十六进制颜色值，扩展为六位
    if (cleanHex.length === 3) {
      cleanHex = cleanHex.split('').reduce(function (acc, char) {
        return acc + char + char;
      }, '');
    }

    // 检查是否是有效的十六进制颜色值（现在已经是6位）
    // if (!/^[A-Fa-f0-9]{6}$/.test(cleanHex)) {
    //   throw new Error('Invalid hexadecimal color format');
    // }

    // 将十六进制转换为RGB
    var r = parseInt(cleanHex.slice(0, 2), 16);
    var g = parseInt(cleanHex.slice(2, 4), 16);
    var b = parseInt(cleanHex.slice(4, 6), 16);

    // 返回 rgba 字符串
    return "rgba(" + r + ", " + g + ", " + b + ", " + alpha + ")";
  }

  module.exports = {
    formatTime: formatTime,
    handleList: handleList,
    getNum: getNum,
    hexToRgba: hexToRgba
  };
</wxs>
<view class="drill-detail" wx:if="{{speechData}}">
  <view class="top-bg" wx:if="{{speechData.img_data.detail_img}}" style="background-image: url({{speechData.img_data.detail_img}})">
    <view class="name-title {{speechData.title.length>24?' text-ellipsis-3':''}}">{{speechData.title}}</view>
    <view class="label-title text-ellipsis-2" wx:if="{{speechData.title.length<24}}">{{speechData.summary}}</view>
    <view class="icon-text-area">
      <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_detail_date.png" mode="" />
      <view class="text">{{speechData.formatStartTime}}开始</view>
    </view>
  </view>
  <view class="main-content">
    <view class="top-box" style="background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_detail_middle_bg.png)">
      <block wx:if="{{speechData.join_record}}">
        <view class="subheading-item">
          <view class="subheading-title">我的得分</view>
          <view class="subheading-c red">
            <block wx:if="{{speechData.join_record.score}}">
              {{utils2.getNum(speechData.join_record.score)}}<text class="">分</text>
            </block>
            <block wx:else>-</block>
          </view>
        </view>
        <view class="subheading-item">
          <view class="subheading-title">我的排名</view>
          <view class="subheading-c">
            <block wx:if="{{speechData.join_record.rank}}">
              {{speechData.join_record.rank}}<text>/{{speechData.participants}}</text>
            </block>
            <block wx:else>-</block>
          </view>
        </view>
        <view class="subheading-item">
          <view class="subheading-title">作答时长</view>
          <view class="subheading-c">{{utils2.formatTime(speechData.join_record.answer_time)}}</view>
        </view>
      </block>
      <block wx:else>
        <view class="subheading-item">
          <view class="subheading-title">出题模式</view>
          <view class="subheading-c">{{speechData.qv_data.question_mod==1?'听题模式':'看题模式'}}</view>
        </view>
        <view class="subheading-item">
          <view class="subheading-title">作答题量</view>
          <view class="subheading-c">{{speechData.qv_data.question_number}}道题</view>
        </view>
        <view class="subheading-item">
          <view class="subheading-title">答题限时</view>
          <view class="subheading-c red">{{speechData.qv_data.answer_time}}分钟</view>
        </view>
      </block>
    </view>
    <view class="commont-box" wx:if="{{speechData.join_record}}">
      <view class="commont-top" wx:if="{{speechData.join_record.correct_status == 1}}">
        <view class="title-area">
          <image class="classmates-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_title_line.png" mode="" />
          <view class="text">AI点评</view>
        </view>
        <view class="commont-content">
          <block wx:if="{{speechData.join_record.correct_status == 1}}">
            <rich-text wx:if="{{ speechData.original_correct.info }}" class="report-card-text" nodes="{{speechData.original_correct.info}}" />
            <view wx:else class="point-area">
              <view class="point-area-item" wx:if="{{ speechData.original_correct.adv }}">
                <view class="title-area">
                  <view>优点</view>
                  <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                </view>
                <rich-text style="white-space: pre-wrap;" class="content-area" nodes="{{speechData.original_correct.adv}}" />
              </view>
              <view class="point-area-item" wx:if="{{speechData.original_correct.problem}}">
                <view class="title-area">
                  <view>不足</view>
                  <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                </view>
                <rich-text style="white-space: pre-wrap;" class="content-area" nodes="{{speechData.original_correct.problem}}" />
              </view>
            </view>
          </block>
          <view class="hideen-box">
            <view class="hui"></view>
            <view class="btn-box">
              <view class="btn" wx:if="{{speechData.join_record.correct_status == 0}}" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};" catchtap="goResult">
                报告生成中
              </view>
              <view class="btn" wx:if="{{speechData.join_record.correct_status == 1}}" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};" catchtap="goResult">
                查看完整报告
              </view>
              <view class="btn" wx:if="{{speechData.join_record.correct_status == -1}}" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};" catchtap="joinSpeech">
                <block wx:if="{{speechData.join_record.correct_status == -1}}">
                  报告生成失败，重新作答
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
      <block wx:else>
        <view class="desc-box">
          <view class="desc-title">演练说明</view>
          <view class="demand-text">1. 本套题共{{speechData.qv_data.question_number}}道题， 进入作答页面开始计时，作答限时{{speechData.qv_data.answer_time}}分钟，超出总时长将自动停止录音。
          </view>
          <view class="demand-text">2. 作答完毕AI会进行智能点评。
          </view>
          <view class="demand-text">3. 请确保使用期间网络环境良好及周围环境安静。</view>
        </view>

        <view class="btn-box mt64">
          <view class="btn" wx:if="{{speechData.join_record.correct_status == 0}}" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};" catchtap="goResult">
            报告生成中
          </view>
          <view class="btn" wx:if="{{speechData.join_record.correct_status == -1}}" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};" catchtap="joinSpeech">
            <block wx:if="{{speechData.join_record.correct_status == -1}}">
              报告生成失败，重新作答
            </block>
          </view>
        </view>
      </block>
    </view>
    <view class="desc-area {{speechData.rank_list.list.length>0?'':'bor_0'}}" wx:else>
      <view class="desc-title">演练说明</view>
      <view class="demand-text">1. 本套题共{{speechData.qv_data.question_number}}道题， 进入作答页面开始计时，作答限时{{speechData.qv_data.answer_time}}分钟，超出总时长将自动停止录音。
      </view>
      <view class="demand-text">2. 作答完毕AI会进行智能点评。
      </view>
      <view class="demand-text">3. 请确保使用期间网络环境良好及周围环境安静。</view>

      <block wx:if="{{speechData.rank_list.list.length>0}}">
        <view class="no-permission" style="margin-top: 64rpx;" wx:if="{{!speechData.has_qualification}}" catch:tap="openWeichatCustomerService">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/no_permission.png"></image>
          本场演练为线下学员专享，去了解
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/detail_img.png"></image>
        </view>
        <authorize-phone wx:else isBindPhone="{{isLogin}}" bind:onAuthorize="joinSpeech">
          <view class="btn" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};">参与{{speechData.is_online?'PK':'演练'}}</view>
        </authorize-phone>
      </block>

      <!-- <view class="people-area" wx:if="{{speechData.rank_list.list.length>0}}">
        <view class="people-area">
          <image class="people-area-photo" src="{{item.portrait}}" wx:for="{{utils2.handleList(speechData.rank_list.list)}}" wx:key="index" />
          <view class="people-area-text">{{speechData.participants}}人已参与</view>
        </view>
      </view> -->
    </view>
    <!-- 底部操作栏 没参加且没有排行榜的状态-->
    <view class="action-bar-box" wx:if="{{speechData.rank_list.list.length<=0 && !speechData.join_record}}">
      <view class="action-bar container flex-justify_between">
        <view class="no-permission" wx:if="{{!speechData.has_qualification}}" catch:tap="openWeichatCustomerService">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/no_permission.png"></image>
          本场演练为线下学员专享，去了解
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/detail_img.png"></image>
        </view>
        <authorize-phone wx:else isBindPhone="{{isLogin}}" bind:onAuthorize="joinSpeech">
          <view class="btn" style="background: {{speechData.btn_color}};  box-shadow: 0rpx 8rpx 24rpx 2rpx {{utils2.hexToRgba(speechData.btn_color,0.2)}};">参与{{speechData.is_online?'PK':'演练'}}</view>
        </authorize-phone>
      </view>
    </view>
  </view>
  <view class="classmate-box" wx:if="{{speechData.rank_list.list.length>0}}">
    <view class="classmate-top">
      <view class="title-area">
        <image class="classmates-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_title_line.png" mode="" />
        <view class="text">排名Top10</view>
      </view>
      <view class="see-more" wx:if="{{speechData.join_record}}" catchtap="goMore">完整榜单<image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow_hui.png"></image>
      </view>
    </view>
    <view class="rank-list" wx:if="{{speechData.rank_list.list.length>0}}">
      <view class="rank-list-top">
        <view class="name w52" style="padding-left: 16rpx;">排名</view>
        <view class="name flex1">同学</view>
        <view class="name" style="padding-right: 28rpx;">得分</view>
      </view>
      <view class="rank-list-item {{item.is_my?'active':''}}" catchtap="goOther" data-item="{{item}}" wx:for="{{speechData.rank_list.list}}" wx:key="index">
        <view class="rank">
          <image wx:if="{{item.rank ===1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_one.png" mode="widthFix"></image>
          <image wx:elif="{{item.rank ===2}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_two.png" mode="widthFix"></image>
          <image wx:elif="{{item.rank ===3}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_three.png" mode="widthFix"></image>
          <block wx:else>
            {{item.rank}}
          </block>
        </view>
        <view class="rank-student">
          <image src="{{item.portrait}}"></image>
          <text class="text-ellipsis-1 name-text">{{item.nickname}}</text>
          <image class="me" wx:if="{{item.is_my}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_me.png"></image>
        </view>
        <view class="rank-score">
          <view class="number">{{utils2.getNum(item.score)}}<text>分</text></view>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
        </view>
      </view>
      <view class="rank-list-item active" wx:if="{{speechData.rank_list.my.rank>10}}" style="margin-top: 20rpx;" catchtap="goResult">
        <view class="rank">
          {{speechData.rank_list.my.rank}}
        </view>
        <view class="rank-student">
          <image src="{{speechData.rank_list.my.portrait}}"></image>
          <text class="text-ellipsis-1 name-text">{{speechData.rank_list.my.nickname}}</text>
          <image class="me" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_me.png"></image>
        </view>
        <view class="rank-score">
          <view class="number">{{utils2.getNum(speechData.rank_list.my.score)}}<text>分</text></view>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 弹窗客服 -->
<wechat-popup weichatShow="{{weichatShow}}" info="{{weichatCustomerService}}" bindonClickHide="closeWeichatCustomerService" />