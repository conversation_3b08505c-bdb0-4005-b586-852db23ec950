page {
  box-sizing: border-box;
  background: #fff;
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
}

.title-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text {
    font-size: 20rpx;
    color: rgba(255, 242, 221, 1);
  }
  image {
    width: 340rpx;
  }
  padding-bottom: 70rpx;
}

.main-content {
  background: #fff;
  position: relative;
  border-radius: 24rpx 24rpx 0 0;
  // margin-top: 50rpx;
  position: relative;
  z-index: 1;
  .top-bg {
    background: #fff;
    position: absolute;
    top: -40rpx;
    left: 0;
    z-index: 2;
    width: 100%;
    border-radius: 24rpx 24rpx 0 0;
    image {
      width: 100%;
    }
  }
}

.rank-list {
  // margin-top: 40rpx;
  position: relative;
  z-index: 2;
  .rank-list-top {
    display: flex;
    align-items: center;
    padding: 8rpx 32rpx;
    .name {
      font-size: 26rpx;
      color: #c2c5cc;
    }
    .flex1 {
      flex: 1;
      min-width: 0;
      padding-left: 32rpx;
    }
    .w52 {
      width: 52rpx;
    }
  }
}

.rank-box {
  // padding: 0 40rpx;
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 126rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 0.5px solid #ebecf0;
  // padding: 24rpx 40rpx 34rpx 40rpx;
  .btn {
    margin: 0 auto;
    width: 622rpx;
    height: 88rpx;
    background: #4550c4;
    box-shadow: 0rpx 8rpx 24rpx 2rpx rgba(69, 80, 196, 0.2);
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 32rpx;
    color: #ffffff;
  }
}

.action-bar-box {
  display: flex;
  height: 126rpx;
  z-index: 998;
  position: relative;
  box-sizing: border-box;
}

.action-bar-box-ones {
  display: flex;
  z-index: 998;
}

.action-bar .button {
  width: 100%;
  height: 100%;
  background-color: var(--main-color);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &.hui {
    background: rgba(230, 0, 0, 0.5);
  }
}

.rank-list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 0.5px solid #ebecf0;
  &:last-child {
    border-bottom: 0;
  }
  &.active {
    background: rgba(230, 0, 0, 0.05);
    border-radius: 12rpx;
    border-bottom: 0;
    .rank {
      color: rgba(230, 0, 0, 1);
    }
  }
  .rank {
    width: 52rpx;
    image {
      width: 52rpx;
    }
    font-size: 32rpx;
    font-weight: bold;
    color: #c2c5cc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "DINBold";
    // padding-left: 16rpx;
  }
  .rank-student {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    padding-left: 32rpx;
    image {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      margin-right: 16rpx;
      border: 1rpx solid #eaeaea;
    }
    text {
      font-size: 28rpx;
      color: #3c3d42;
    }
    .name-text {
      max-width: 214rpx;
    }
    .me {
      width: 32rpx !important;
      height: 32rpx !important;
      border-radius: 0 !important;
      margin-left: 16rpx;
    }
  }
  .rank-score {
    display: flex;
    align-items: center;
    .number {
      font-size: 28rpx;
      color: #3c3d42;
      text {
        color: #919499;
      }
    }
    image {
      width: 24rpx;
      height: 24rpx;
    }
  }
}
