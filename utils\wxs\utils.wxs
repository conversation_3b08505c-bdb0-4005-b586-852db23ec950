function clearPriceZero(price) {
  // 拷贝一份价格字符串
  var newPrice = price
  // 找到小数点的位置
  var dotIndex = (price + "").indexOf(".")

  if (dotIndex > -1) {
    // 从末尾开始遍历
    while (newPrice[newPrice.length - 1] === "0") {
      newPrice = newPrice.slice(0, -1)
    }

    // 如果最后一位是小数点，去掉小数点
    if (newPrice[newPrice.length - 1] === ".") {
      newPrice = newPrice.slice(0, -1)
    }
  }

  return newPrice
}

function secondsToMinutes(seconds) {
  if (typeof seconds !== "number" || seconds < 0) {
    return "" // 如果输入不是数字或小于0，返回空字符串
  }

  var minutes = seconds / 60 // 计算分钟数（可能包含小数）

  // 转换为字符串并处理小数部分
  var formattedMinutes = clearPriceZero(minutes.toFixed(1))

  return formattedMinutes
}

module.exports = {
  clearPriceZero: clearPriceZero,
  secondsToMinutes: secondsToMinutes,
}
