Component({
  options: {
    addGlobalClass: true,
  },
  properties: {
    open: {
      type: Boolean,
      value: false, // 默认值
    },
    title: {
      type: String,
      value: "规则宣讲中",
    },
    content: {
      type: String,
      value: "规则宣讲中",
    },
    isDark: {
      type: Boolean,
      value: false, // 默认值
    },
  },
  methods: {
    changePop() {
      this.setData({
        open: !this.data.open,
      })
    },
  },
})
