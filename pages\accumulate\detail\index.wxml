<view class="top-box">
  {{title}}
</view>
<view class="main-content {{ type === 3? 'person-content':''}}">
  <rich-text wx:if="{{ type === 3 }}" class="rich-text" nodes="{{ dataList[0].content }}" />
  <view class="politics-list" wx:else>
    <view class="politics-list-item" wx:for="{{dataList}}" wx:key="index">
      <view class="title" wx:if="{{ item.title }}">
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_tips.png"></image>
        <text class="text-ellipsis-1">{{ item.title }}</text>
      </view>
      <rich-text class="rich-text" nodes="{{item.content}}" />
    </view>
  </view>
</view>
<view class="action-bar-box" wx:if="{{ isComplete }}">
  <!-- <remain-time wx:if="{{data.verify_record && data.question_id !== 0}}" verify-record="{{data.verify_record}}"></remain-time> -->
  <view class="action-bar container flex-justify_between">

    <authorize-phone isBindPhone="{{isLogin}}" bind:onAuthorize="collectTap">
      <view class="left-collect">
        <image wx:if="{{data.is_collect == 1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_star_select.png"></image>
        <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_star.png"></image>
        <text>{{data.is_collect==1?'取消':'收藏'}}</text>
      </view>
    </authorize-phone>

    <view class="left-collect blue" wx:if="{{data.video_url}}" catchtap="changePlay" data-item="{{data}}">
      <image wx:if="{{isPlaying &&musciId==data.id}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif"></image>
      <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_listen.png"></image>
      <text>{{(isPlaying &&musciId==data.id)?'暂停':'听素材'}}</text>
    </view>
    <view class="button-area">
      <view class="button {{ data.id == data.accum_list[data.accum_list.length - 1]?'opc-05':''}} {{data.question_id == 0?'':'next-btn'}} {{data.question_id == 0?'w100':''}}" bind:tap="goNext" data-data="{{data}}">下一篇</view>
      <block wx:if="{{data.question_id != 0}}">
        <view class="button opc-05" wx:if="{{(data.verify_record.error_code == '7009'|| data.verify_record.error_code == '7010' || data.verify_record.error_code == '7011') &&isLogin}}">去练习</view>
        <authorize-phone wx:else class="button" customClass="button" isBindPhone="{{isLogin}}" bind:onAuthorize="goQuestion">
          去练习
        </authorize-phone>
      </block>
    </view>
    <!-- <block wx:if="{{data.question_id === 0 }}">
      <view class="button {{ data.id === data.accum_list[data.accum_list.length - 1]?'opc-05':''}}" bind:tap="goNext" data-data="{{data}}">下一篇</view>
    </block>
    <block wx:else>
      <view class="button opc-05" wx:if="{{(data.verify_record.error_code == '7009'|| data.verify_record.error_code == '7010' || data.verify_record.error_code == '7011') &&isLogin}}">去练习</view>
      <authorize-phone wx:else class="button" customClass="button" isBindPhone="{{isLogin}}" bind:onAuthorize="goQuestion">
        去练习
      </authorize-phone>
    </block> -->
  </view>
</view>