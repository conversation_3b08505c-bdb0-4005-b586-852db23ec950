<wxs module="utils2">
  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };

  module.exports = {
    getNum: getNum
  };
</wxs>
<navigation-bar back="{{true}}" isResult="{{true}}" isSticky="{{ true }}" showWhite="{{show_white}}">
</navigation-bar>
<view class="title-box">
  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_title.png" mode="widthFix"></image>
  <text>更新于 {{rank_list.rank_update_time}}</text>
</view>
<view class="main-content">
  <view class="top-bg">
    <image mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_top.png"></image>
  </view>
  <view class="rank-box">
    <view class="rank-list" wx:if="{{rank_list.list.length>0}}">
      <view class="rank-list-top">
        <view class="name w52">排名</view>
        <view class="name flex1">同学</view>
        <view class="name" style="padding-right: 28rpx;">得分</view>
      </view>
      <view class="rank-list-item {{item.is_my?'active':''}}" catchtap="goOther" data-item="{{item}}" wx:for="{{rank_list.list}}" wx:key="index">
        <view class="rank">
          <image wx:if="{{item.rank ===1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_one.png" mode="widthFix"></image>
          <image wx:elif="{{item.rank ===2}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_two.png" mode="widthFix"></image>
          <image wx:elif="{{item.rank ===3}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rank_three.png" mode="widthFix"></image>
          <block wx:else>
            {{item.rank}}
          </block>
        </view>
        <view class="rank-student">
          <image src="{{item.portrait}}"></image>
          <text class="text-ellipsis-1 name-text">{{item.nickname}}</text>
          <image class="me" wx:if="{{item.is_my}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_me.png"></image>
        </view>
        <view class="rank-score">
          <view class="number">{{utils2.getNum(item.score)}}<text>分</text></view>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
        </view>
      </view>
      <!-- <view class="rank-list-item active" wx:if="{{speechData.rank_list.my.rank>10}}" style="margin-top: 20rpx;" catchtap="goMore">
        <view class="rank">
          {{speechData.rank_list.my.rank}}
        </view>
        <view class="rank-student">
          <image src="{{speechData.rank_list.my.portrait}}"></image>
          <text class="text-ellipsis-1 name-text">{{speechData.rank_list.my.nickname}}</text>
          <image class="me" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_me.png"></image>
        </view>
        <view class="rank-score">
          <view class="number">{{utils2.getNum(speechData.rank_list.my.score)}}<text>分</text></view>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
        </view>
      </view> -->
    </view>
  </view>
  <!-- 底部操作栏 没参加且没有排行榜的状态-->
  <view class="action-bar-box" wx:if="{{rank_list.my.rank>10}}">
    <view class="action-bar container flex-justify_between">
      <view class="rank-list-item active" wx:if="{{rank_list.my}}" catchtap="goResult">
        <view class="rank">
          {{rank_list.my.rank}}
        </view>
        <view class="rank-student">
          <image src="{{rank_list.my.portrait}}"></image>
          <text class="text-ellipsis-1 name-text">{{rank_list.my.nickname}}</text>
          <image class="me" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_me.png"></image>
        </view>
        <view class="rank-score">
          <view class="number">{{utils2.getNum(rank_list.my.score)}}<text>分</text></view>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
        </view>
      </view>
    </view>
  </view>
</view>