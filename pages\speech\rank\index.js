const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
let PAGE_OPTIONS = {}
Page({
  data: {
    show_white: false,
    rank_list: {
      list: [],
      my: null,
      share_info: null,
      rank_update_time: null,
    },
    isRequest: true,
    page: 1,
  },
  onLoad(options) {
    PAGE_OPTIONS = options
    console.log(options)
    this.getRankList(1)
  },

  async getRankList(type) {
    const res = await UTIL.request(API.getDrillRankList, {
      id: PAGE_OPTIONS.id,
      page: this.data.page,
    })
    const resData = res.data || {}
    let arr = []
    arr =
      type == 1 ? resData.list : this.data.rank_list.list.concat(resData.list)
    let isRequest = this.data.isRequest
    isRequest = resData?.list?.length > 0 ? true : false
    this.setData({
      isRequest,
      "rank_list.list": arr,
      "rank_list.my": resData.my,
      "rank_list.share_info": resData.share_info,
      "rank_list.rank_update_time": resData.rank_update_time,
    })
  },
  // 触底加载
  onReachBottom() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getRankList(2)
  },

  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  // 他人报告页
  goOther(e) {
    const data = e.currentTarget.dataset.item
    ROUTER.navigateTo({
      path: "/pages/speech/report/index",
      query: {
        record_id: data.id,
        item_no: PAGE_OPTIONS.id,
      },
    })
  },
  // 查看完整报告
  goResult() {
    ROUTER.navigateTo({
      path: "/pages/speech/report/index",
      query: {
        record_id: this.data.rank_list.my.id,
        item_no: PAGE_OPTIONS.id,
      },
    })
  },
  getPageShareParams() {
    let query = { ...PAGE_OPTIONS }
    return APP.createShareParams({
      title: this.data?.rank_list?.share_info?.title || "",
      imageUrl: this.data?.rank_list?.share_info?.image || "",
      path: "/pages/speech/detail/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  // onShareTimeline() {
  //   const params = this.getPageShareParams()
  //   delete params.imageUrl
  //   params.query = ROUTER.convertPathQuery(params.query)
  //   return params
  // },
})
