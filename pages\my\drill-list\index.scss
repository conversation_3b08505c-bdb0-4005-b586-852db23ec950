page {
  background: #f2f4f7;
}
.drill-list {
  .pd-style {
    padding: 0 80rpx;
  }
  .tab-list {
    background: #fff;
    padding-bottom: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    &-item {
      flex: 1;
      font-size: 30rpx;
      padding: 32rpx 0;
      color: #666666;
      font-weight: 400;
      text-align: center;
      &.active {
        font-weight: bold;
        font-size: 36rpx;
        color: #22242e;
        .text {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            bottom: 2rpx;
            left: -6rpx;
            width: 80rpx;
            height: 12rpx;
            background: linear-gradient(
              131deg,
              rgba(230, 0, 0, 0.3) 0%,
              rgba(230, 0, 0, 0) 100%
            );
            border-radius: 8rpx;
          }
        }
      }
    }
  }
  .list-area {
    margin-top: 34rpx;
    box-sizing: border-box;
    padding: 0 32rpx;
    &-item {
      background: #ffffff;
      box-shadow: 0rpx 4rpx 16rpx 2rpx rgba(76, 96, 133, 0.05);
      border-radius: 16rpx;
      box-sizing: border-box;
      padding: 32rpx;
      margin-bottom: 24rpx;
      .top-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 32rpx;
        .top-text {
          font-size: 28rpx;
          color: #919499;
          font-weight: 400;
        }
        .red-text {
          color: #e60000;
        }
        .right {
          display: flex;
          align-items: center;
          .arrow-icon {
            width: 24rpx;
            height: 24rpx;
            margin-left: 8rpx;
          }
        }
      }
      .teacher-box {
        display: flex;
        align-items: flex-start;
        box-sizing: border-box;
        padding: 20rpx 24rpx;
        border-radius: 12rpx;
        margin-top: 32rpx;
        background: rgba(242, 244, 247, 0.5);
        .teachers-icon {
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background-color: red;
          margin-right: 8rpx;
        }
        .right {
          flex: 1;
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
          line-height: 36rpx;
          .name {
            color: #e94442;
          }
        }
      }
      .content-area {
        font-size: 30rpx;
        font-weight: 500;
        color: #3c3d42;
        line-height: 46rpx;
      }
      .gray-text {
        font-size: 24rpx;
        color: #919499;
        font-weight: 400;
        margin-top: 24rpx;
      }
    }
  }
}
.no-data-box {
  overflow: hidden;
}
.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}
