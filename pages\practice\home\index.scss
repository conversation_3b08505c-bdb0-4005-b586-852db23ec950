page {
  box-sizing: border-box;
  background: #f7f5f5;
  // background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/banner_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
}

.card-list {
  padding: 24rpx;
  margin-top: 25rpx;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 12rpx 2rpx rgba(34, 36, 46, 0.05);
  border-radius: 24rpx;
  position: relative;
  z-index: 2;
  .content-c {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8rpx 8rpx 32rpx 8rpx;
    border-bottom: 0.5px solid #ebecf0;
  }
  &-item {
    width: 96rpx;
    // width: 149rpx;
    // height: 172rpx;
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    image {
      width: 88rpx;
      height: 88rpx;
      display: block;
    }
    text {
      font-size: 22rpx;
      color: #3c3d42;
      // position: absolute;
      font-weight: bold;
      margin-top: 8rpx;
      // left: 50%;
      // transform: translateX(-50%);
      // top: 24rpx;
    }
  }
}

// .video-bo {
//   padding-left: 175rpx !important;
//   padding-right: 32rpx !important;
// }

// .swiper-item {
//   position: relative;
// }

.pr {
  position: relative;
}

.banner-swiper {
  margin-top: 20rpx;
  position: relative;
  z-index: 3;
  .swiper {
    // height: 156rpx;
    position: relative;
    // padding: 0 32rpx;
    .swiper-item {
      // padding-top: 20rpx;
      display: flex;
      align-items: flex-end;
      height: 156rpx;
    }
    .walkman {
      height: 132rpx;
      width: 100%;
      box-shadow: 0rpx 4rpx 16rpx 2rpx rgba(34, 36, 46, 0.05);
      border-radius: 24rpx;
      background: #ffffff;
      .video-bg {
        height: 132rpx;
        width: 100%;
      }
      .video-content {
        height: 132rpx;
        position: absolute;
        left: 0;
        right: 0;
        top: 25rpx;
        bottom: 0;
        display: flex;
        align-items: center;
      }
      .film {
        height: 130rpx;
        width: 130rpx;
        position: absolute;
        left: 20rpx;
        top: -10rpx;
        z-index: 222;
        .film-img {
          width: 130rpx;
          height: 130rpx;
          border-radius: 50%;
          box-shadow: 0rpx 2rpx 8rpx 2rpx rgba(22, 48, 67, 0.4);
        }
        text {
          font-size: 24rpx;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 42rpx;
          text-shadow: 0px 1px 2px rgba(198, 39, 39, 0.4);
          color: #fff;
          font-weight: 800;
          width: 50rpx;
        }
        .pointer {
          width: 46rpx;
          height: 58rpx;
          position: absolute;
          top: 20rpx;
          left: 95rpx;
        }
      }
      .fonts-box {
        padding-left: 175rpx;
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        padding-right: 32rpx;
        .left-box {
          flex: 1;
          min-width: 0;
          padding-right: 30rpx;
          .title {
            font-size: 30rpx;
            font-weight: bold;
            display: flex;
            align-items: center;
            padding-right: 5rpx;
            text {
              max-width: 360rpx;
            }
            image {
              width: 24rpx;
              height: 24rpx;
            }
          }
          .label {
            font-size: 24rpx;
            color: #919499;
            margin-top: 5rpx;
          }
        }

        .right-box {
          image {
            width: 64rpx;
            height: 64rpx;
          }
        }
      }
    }
  }
}

.custom-indicator {
  position: absolute;
  top: 330rpx;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  .indicator-dot {
    width: 6rpx;
    height: 6rpx;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6rpx;
    margin: 0 4rpx;

    &.active {
      width: 24rpx;
      background: #fff;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate {
  // height: 166rpx;
  // width: 166rpx;
  width: 100%;
  height: 100%;
  // animation: rotate 3s linear infinite;
  &.xuan {
    animation: rotate 5s linear infinite;
  }
}

.ins-card-content {
  // padding: 0 32rpx;
  margin-top: 32rpx;
  // padding-bottom: 24rpx;
}

.exercise-content {
  position: relative;
  padding-top: 40rpx;
  padding-left: 8rpx;
  padding-right: 8rpx;
  position: relative;
  // background: #def0ff;
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx 8rpx 8rpx 8rpx;
  .top-bg {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    // height: 170rpx;
    display: block;
    z-index: 2;
  }
}

.exercise-bg {
  width: 100%;
  position: absolute;
  z-index: 0;
  left: 0;
  right: 0;
  top: 0;
}

.exercise-title {
  position: relative;
  z-index: 3;
  font-size: 40rpx;
  color: #3c3d42;
  font-weight: bold;
  padding-left: 40rpx;
  margin-bottom: 5rpx;
  .line {
    display: block;
    content: " ";
    position: absolute;
    background: linear-gradient(90deg, #fff079 0%, rgba(255, 240, 121, 0) 100%);
    width: 140rpx;
    height: 20rpx;
    left: 40rpx;
    bottom: 0;
    z-index: -1;
  }
}

.floating {
  position: fixed;
  bottom: 250rpx;
  right: 0;
  z-index: 2;
  image {
    width: 136rpx;
  }
}

.roll-box {
  // padding: 0 32rpx;
  margin-top: 32rpx;
  box-sizing: border-box;
  .roll-content {
    position: relative;
    .top-bg {
      position: absolute;
      width: 100%;
      top: 0;
      left: 0;
      right: 0;
      // height: 170rpx;
      display: block;
      z-index: 1;
    }
    padding: 40rpx;
    background: #fff;
    border-radius: 32rpx;
    .roll-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .exercise-title {
        padding-left: 0;
        .line {
          left: 0;
          background: linear-gradient(
            90deg,
            rgba(191, 74, 0, 0.35) 0%,
            rgba(191, 74, 0, 0) 100%
          );
        }
      }
      .see-more {
        position: relative;
        z-index: 2;
        font-size: 24rpx;
        color: #96400a;
        display: flex;
        align-items: center;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-left: 8rpx;
        }
      }
    }

    .roll-list {
      margin-top: 50rpx;
      &-item {
        display: flex;
        align-items: center;
        padding: 40rpx 0;
        border-bottom: 0.5px solid #ebecf0;
        &:last-child {
          border-bottom: 0;
          padding-bottom: 0;
        }
        .title {
          font-size: 30rpx;
          color: #3c3d42;
          line-height: 46rpx;
          flex: 1;
          min-width: 0;
          padding-right: 32rpx;
        }
        image {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }
  }
}

.bottom-advert {
  width: 100%;
  border-radius: 24rpx;
}

.advert-content {
  // padding: 0 32rpx;
  margin-top: 32rpx;
}

.main-content {
  padding: 0 32rpx;
  padding-bottom: 40rpx;
  position: relative;
  z-index: 2;
  margin-top: -80rpx;
  .banner-bg {
    width: 100%;
    position: absolute;
    left: 0;
    right: 0;
    top: 40rpx;
    z-index: 1;
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  .img-wrap {
    width: 600rpx;
  }
  .close-yuan {
    width: 56rpx;
    height: 56rpx;
    margin-top: 60rpx;
  }
}

.home-banner {
  position: relative;
  .swiper {
    height: 420rpx;
  }
}

.common-tips {
  display: flex;
  align-items: center;
  padding-top: 24rpx;
  .left {
    display: flex;
    align-items: center;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
    font-size: 26rpx;
    color: #3c3d42;
  }
  .lines {
    font-size: 22rpx;
    color: #c2c5cc;
    margin: 0 8rpx;
  }
  .top-text {
    flex: 1;
    min-width: 0;
    padding-right: 30rpx;
    font-size: 24rpx;
    color: #919499;
  }
  .arrow {
    width: 24rpx;
    height: 24rpx;
  }
}

.exam-box {
  position: absolute;
  left: 0;
  z-index: 222;
  display: flex;
  align-items: center;
  padding-left: 32rpx;
  height: 66rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
  // line-height: 44rpx;

  .icon {
    width: 32rpx;
    transform: translateY(2rpx);
    margin-left: 2rpx;
    height: 32rpx;
  }
}

.oh-hideen {
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  z-index: 0;
}

.pb_0 {
  padding-bottom: 8rpx !important;
  border-bottom: 0 !important;
}

.empty-button {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 288rpx;
  height: 72rpx;
  background: var(--main-color);
  font-size: 28rpx;
  color: #fff;
  border-radius: 100rpx;
  margin-top: 42rpx;
}
