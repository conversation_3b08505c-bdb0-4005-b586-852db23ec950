// const API_BASE_URL = "https://mp.test.tikutech.com/aiinterview-online-mp"
const API_BASE_URL = "https://mp.tikutech.com/aiinterview-online-mp"

module.exports = {
  API_BASE_URL,
  getUserInfo: API_BASE_URL + "/api/mp/v1/home/<USER>", //获取配置信息
  userLogin: API_BASE_URL + "/api/mp/v1/home/<USER>", // 用户登录
  getHome: API_BASE_URL + "/api/mp/v1/home/<USER>", // 首页
  editUserInfo: API_BASE_URL + "/api/mp/v1/user/edit_user_info", // 修改用户个人信息
  getCosTempKeys: API_BASE_URL + "/api/mp/v1/common/get_cos_temp_keys", // 获取上传文件临时key
  getVoiceQuestionDetail: API_BASE_URL + "/api/mp/v1/item/get_item_data", // 获取语音题目

  getCommonConfiguration: API_BASE_URL + "/api/mp/v1/common/get_config", //获取配置信息
  changeUserSetting: API_BASE_URL + "/api/mp/v1/user/change_setting", // 修改用户选择省市区

  getNewsList: API_BASE_URL + "/api/mp/v1/news/get-list-by-tag", // 练习（考情）列表
  getNewsDetail: API_BASE_URL + "/web/wv/news/detail", // 练习（考情）详情
  getShareInfo: API_BASE_URL + "/api/mp/v1/news/get-share-info", // 获取资讯分享信息
  getBookLetData: API_BASE_URL + "/api/mp/v1/booklet-list/list-by-tag", // 练习（套卷列表)
  getBookLetList: API_BASE_URL + "/api/mp/v1/booklet/list", // 某个套卷列表下习题单列表
  getBookLetDetail: API_BASE_URL + "/api/mp/v1/booklet/detail", // 套卷详情
  getRecordHistory: API_BASE_URL + "/api/mp/v1/item/get_record_history", // 获取答题历史

  getExcerciseList: API_BASE_URL + "/api/mp/v1/item/get_exercise_question_list", // 获取专项练习题目列表

  getCcumulationListTag: API_BASE_URL + "/api/mp/v1/accumulation/list-tag", // 积累顶部标签列表
  getCcumulationList: API_BASE_URL + "/api/mp/v1/accumulation/list", // 积累列表
  getCcumulationDetail: API_BASE_URL + "/api/mp/v1/accumulation/detail", // 积累详情

  getRecommendList: API_BASE_URL + "/api/mp/v1/recommend/list", // 提升列表

  getRreeList: API_BASE_URL + "/api/mp/v1/home/<USER>", // 根据省份考试获取知识结构
  getGuideInfo: API_BASE_URL + "/api/mp/v1/common/get_guide_info ", // 获取引导页信息

  getRecordData: API_BASE_URL + "/api/mp/v1/item/get_record_data", // 获取作答报告数据
  submitQuestionItem: API_BASE_URL + "/api/mp/v1/item/submit", // 提交题目
  getQuestionDetail: API_BASE_URL + "/api/mp/v1/item/get_question_list_data", // 获取题目数据
  getHomeExerciseProgress:
    API_BASE_URL + "/api/mp/v1/item/get_exercise_done_question_data", // 获取题目进度
  toggleCollect: API_BASE_URL + "/api/mp/v1/collect/toggle-collect", // 收藏
  getSubjectList: API_BASE_URL + "/api/mp/v1/question-list/detail", // 获取题目列表
  getDrillDetail: API_BASE_URL + "/api/mp/v1/drill/detail", // 演练详情
  getDrillList: API_BASE_URL + "/api/mp/v1/drill/list-by-tag-id", // 演练列表
  getJobList: API_BASE_URL + "/api/mp/v1/drill/job-list", // 演练顶部岗位列表
  getJobHome: API_BASE_URL + "/api/mp/v1/drill/index-by-job-id", // 演练首页

  getShareData: API_BASE_URL + "/api/mp/v1/item/get_guide_share_data", // 获取分享领取次数活动的数据
  getHandleGuide: API_BASE_URL + "/api/mp/v1/item/handle_guide_share_activity", //处理分享领取次数活动

  // 我的
  getMyExamTabList:
    API_BASE_URL + "/api/mp/v1/item/get_my_exercise_structure_map", // 获取我的练习顶部tab(单题)
  getMyExerciseRecordList:
    API_BASE_URL + "/api/mp/v1/item/get_my_exercise_record_list", // 获取我的练习列表(单题)
  getBookletListMap: API_BASE_URL + "/api/mp/v1/item/get_booklet_list_map", // 获取我的练习顶部tab(套题)
  getMyRecordBookletList:
    API_BASE_URL + "/api/mp/v1/item/get_my_record_booklet_list", // 获取我的练习列表(套题)
  getCollectTab: API_BASE_URL + "/api/mp/v1/collect/get-collect-tab", // 获取收藏顶部tab
  getCollectList: API_BASE_URL + "/api/mp/v1/collect/get-collect-list", // 获取收藏列表

  getMyDrillTag: API_BASE_URL + "/api/mp/v1/drill/my-drill-tag", // 我的演练标签
  getMyDrillList: API_BASE_URL + "/api/mp/v1/drill/my-drill-list", // 我的演练列表

  getDrillRankList: API_BASE_URL + "/api/mp/v1/drill/rank-list", // 演练排行榜
  getIndexCodePath: API_BASE_URL + "/api/mp/v1/qrcode/get-path-by-code", // 获取扫码地址
  submitPlayTime: API_BASE_URL + "/api/mp/v1/accumulation/submit-play-time", // 提交音频进度
  submitPlayerRecord:
    API_BASE_URL + "/api/mp/v1/accumulation/submit-player-record", // 注册单词播放行为
}
