import * as echarts from "../../ec-canvas/echarts"
Component({
  properties: {
    scoreMap: {
      type: Array,
      value: [],
    },
  },
  data: {
    num: 0,
  },

  lifetimes: {
    attached() {
      this.setData({
        num: Math.random(100000),
        ec: {
          onInit: this.initChart.bind(this),
        },
      })
    },
  },
  methods: {
    initChart(canvas, width, height, dpr) {
      this.echartsComponnet1 = this.selectComponent("#mychart-dom-turnover")
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr,
      })
      canvas.setChart(chart)

      var scoreMap = this.data.scoreMap
      var newScores = this.data.scoreMap.map((item) => item.score)
      // var avgScores = this.data.scoreMap.map((item) => item.avg_score)
      var option = {
        animation: false, // 禁用整个图表的动画
        backgroundColor: "#ffffff",
        xAxis: {
          show: false,
        },
        yAxis: {
          show: false,
        },
        radar: {
          radius: "65%",
          indicator: [
            {
              name:
                "{a|" +
                scoreMap[0].itemName +
                "}\n{b|" +
                scoreMap[0].score +
                "分" +
                "}",
              max: 5,
            },
            {
              name:
                "{a|" +
                scoreMap[1].itemName +
                "}\n{b|" +
                scoreMap[1].score +
                "分" +
                "}",
              max: 5,
            },
            {
              name:
                "{a|" +
                scoreMap[2].itemName +
                "}\n{b|" +
                scoreMap[2].score +
                "分" +
                "}",
              max: 5,
            },
            {
              name:
                "{a|" +
                scoreMap[3].itemName +
                "}\n{b|" +
                scoreMap[3].score +
                "分" +
                "}",
              max: 5,
            },
            {
              name:
                "{a|" +
                scoreMap[4].itemName +
                "}\n{b|" +
                scoreMap[4].score +
                "分" +
                "}",
              max: 5,
            },
            {
              name:
                "{a|" +
                scoreMap[5].itemName +
                "}\n{b|" +
                scoreMap[5].score +
                "分" +
                "}",
              max: 5,
            },
          ],
          name: {
            textStyle: {
              fontSize: 12,
              color: "#000",
              rich: {
                a: {
                  lineHeight: 18,
                  align: "center", // 居中对齐
                  color: "rgba(145, 148, 153, 1)",
                },
                b: {
                  lineHeight: 18,
                  align: "center", // 居中对齐
                  color: "rgba(102, 102, 102, 1)",
                },
              },
            },
          },
        },
        series: [
          {
            name: "预算",
            type: "radar",
            data: [
              // {
              //   value: avgScores,
              //   areaStyle: {
              //     normal: {
              //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              //         { offset: 0, color: "rgba(145, 148, 153, 0.40)" },
              //         { offset: 1, color: "rgba(145, 148, 153, 0.20)" },
              //       ]),
              //     },
              //   },
              //   itemStyle: {
              //     color: "transparent", // 空心圆：内部填充为无色
              //     borderColor: "rgba(145,148,153,0.8)", // 蓝色边框
              //     borderWidth: 0, // 边框宽度
              //   },
              //   lineStyle: {
              //     color: "rgba(145,148,153,0.8)", // 设置线条颜色为蓝色
              //     width: 1.5, // 线条宽度
              //   },
              // },
              {
                value: newScores,
                areaStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: "rgba(244, 52, 52, 0.60)" },
                      { offset: 1, color: "rgba(244, 52, 52, 0.20)" },
                    ]),
                  },
                },
                itemStyle: {
                  color: "#fff", // 空心圆：内部填充为无色
                  borderColor: "rgba(230, 0, 0, 1)", // 蓝色边框
                  borderWidth: 2, // 边框宽度
                },
                lineStyle: {
                  color: "rgba(230, 0, 0, 1)", // 设置线条颜色为蓝色
                  width: 1.5, // 线条宽度
                },
              },
            ],
          },
        ],
      }

      chart.setOption(option)
      return chart
    },
  },
})
