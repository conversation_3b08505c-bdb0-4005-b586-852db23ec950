page {
  background: rgba(245, 246, 247, 1);
}
.speech-history-list {
  box-sizing: border-box;
  padding: 40rpx 32rpx;
  .speech-top-box {
    position: relative;
    width: 100%;
    height: 388rpx;
    margin-bottom: 24rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
    .speech-bg {
      width: 100%;
      height: 100%;
    }
    .font {
      position: absolute;
      left: 0;
      bottom: 0;
      top: 0;
      right: 0;
      padding: 42rpx 40rpx 40rpx 40rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .top-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24rpx;
        width: 100%;
        .top-left {
          display: flex;
          align-items: center;
          .drill_fire {
            width: 40rpx;
            height: 40rpx;
            margin-left: 8rpx;
          }
          .title {
            font-size: 44rpx;
            font-weight: bold;
            color: #ffffff;
            line-height: 46rpx;
          }
        }
        .top-right {
          background: linear-gradient(90deg, #f94f4f 0%, #ff886c 100%);
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 400;
          font-size: 22rpx;
          color: #ffffff;
          padding: 8rpx 12rpx;
        }
        .blue-top-right {
          background: linear-gradient(90deg, #0092ff 0%, #e60000 100%);
        }
      }
      .desc-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #c2c5cc;
        width: 440rpx;
        line-height: 36rpx;
        margin-bottom: 48rpx;
      }
      .time-text {
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
      }
      .bottom-area {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex: 1;
        .text {
          color: #c2c5cc;
          font-size: 24rpx;
          font-weight: 400;
        }
      }
    }
    .blank {
      align-items: center;
      justify-content: center;
      &-img {
        width: 96rpx;
        height: 96rpx;
        margin-bottom: 40rpx;
      }
      &-text {
        font-weight: 500;
        font-size: 24rpx;
        color: #eaeaea;
        line-height: 46rpx;
      }
    }
  }
}

.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}

.list-item {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
  padding: 32rpx 32rpx 24rpx 32rpx;
  .top-area {
    display: flex;
    align-items: center;
    .top-left {
      width: calc(100% - 48rpx);
      .list-title-text {
        color: #3c3d42;
        font-size: 28rpx;
        font-weight: 500;
        margin-bottom: 16rpx;
      }
      .list-text {
        width: 100%;
        font-weight: 400;
        font-size: 24rpx;
        color: #919499;
        white-space: nowrap; /* 强制不换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 溢出时显示省略号 */
      }
    }
    .arrow {
      width: 24rpx;
      height: 24rpx;
      margin-left: 24rpx;
    }
  }
  .line {
    width: 100%;
    height: 1rpx;
    background: #ebecf0;
    margin: 32rpx 0 16rpx 0;
  }
  .bottom-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .photo {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        margin-right: 12rpx;
        position: relative;
        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
        .index-label {
          display: flex;
          align-items: center;
          padding-left: 6rpx;
          font-size: 12rpx;
          background: linear-gradient(
            90deg,
            #fa9733 0%,
            rgba(250, 151, 51, 0.6) 58%,
            rgba(250, 151, 51, 0) 100%
          );
          &.hui {
            background: linear-gradient(
              90deg,
              #aeb0b4 0%,
              rgba(174, 176, 180, 0.6) 58%,
              rgba(174, 176, 180, 0) 100%
            );
          }
          &.ju {
            background: linear-gradient(
              90deg,
              #e66d57 0%,
              rgba(230, 109, 87, 0.6) 58%,
              rgba(230, 109, 87, 0) 100%
            );
          }
          color: #fff;
          width: 24rpx;
          height: 20rpx;
          position: absolute;
          bottom: 0;
          left: 0;
          border-radius: 28rpx;
        }
        // background: red;
      }
    }
    .right {
      font-weight: 400;
      font-size: 22rpx;
      color: #c2c5cc;
    }
  }
  .label-box {
    display: flex;
    align-items: center;
    .label-text {
      font-size: 24rpx;
      color: #919499;
      text {
        color: #666;
      }
      &.red {
        text {
          color: #e60000;
        }
      }
    }
    .lines {
      width: 0.5px;
      height: 24rpx;
      background-color: #ebecf0;
      margin: 0 25rpx;
    }
  }
}

.main-content {
  padding: 32rpx;
  padding-bottom: 64rpx;
}
