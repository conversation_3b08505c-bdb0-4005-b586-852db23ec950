const ROUTER = require("@/services/routerManager")
const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLogin: false,
    userInfo: {},
    optionList: [],
    optionList1: [
      {
        name: "我的练习",
        num: 1,
      },
      {
        name: "我的演练",
        num: 2,
      },
      {
        name: "我的收藏",
        num: 3,
      },
      {
        name: "面授咨询",
        num: 5,
      },
    ],
    optionList2: [
      {
        name: "我的练习",
        num: 1,
      },
      {
        name: "我的演练",
        num: 2,
      },
      {
        name: "我的收藏",
        num: 3,
      },
    ],
    weichatShow: false, // 微信客服弹窗
    weichatCustomerService: false, // 微信客服信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    if (APP.globalData.userInfo?.token) {
      this.setData({
        userInfo: APP.globalData.userInfo,
        isLogin: APP.getIsUserLogin(),
      })
    }
  },
  onShow() {
    this.getInfo()
  },
  getInfo() {
    this.setData({
      isLogin: APP.getIsUserLogin(),
      userInfo: APP.globalData.userInfo,
    })
    let configList = APP?.globalData?.serverConfig?.customer_list
    const obj = BASE_CACHE.getBaseCache() || {}
    let province = obj.province?.key
    let customerService = configList.find(
      (item) => item.province_key == province
    )?.customer_config
    let optList = customerService
      ? this.data.optionList1
      : this.data.optionList2
    this.setData({
      optionList: optList,
    })
  },
  goPage(e) {
    const num = e.currentTarget.dataset.data.num
    let url = ""
    if (num == 5) {
      this.openWeichatCustomerService()
    } else {
      switch (num) {
        case 1:
          url = "/pages/my/exercise/index"
          break
        case 2:
          url = "/pages/my/drill-list/index"
          break
        case 3:
          url = "/pages/my/collect-list/index"
          break
        case 4:
          url = "/pages/my/exercise-setting/index"
          break
      }

      ROUTER.navigateTo({
        path: url,
      })
    }
  },
  // 处理关闭校区微信客服
  closeWeichatCustomerService() {
    this.setData({
      weichatShow: false,
    })
  },
  // 处理打开校区微信客服
  openWeichatCustomerService() {
    let configList = APP?.globalData?.serverConfig?.customer_list
    const obj = BASE_CACHE.getBaseCache() || {}
    let province = obj.province?.key
    let customerService = configList.find(
      (item) => item.province_key == province
    ).customer_config
    if (customerService.type == "popup") {
      this.setData({
        weichatCustomerService: customerService,
        weichatShow: true,
      })
      return
    }
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: {
        url: customerService.url,
      },
    })
  },
})
