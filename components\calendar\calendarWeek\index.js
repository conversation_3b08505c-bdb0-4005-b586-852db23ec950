var utils = require("./util.js");

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    classPointObj: Object,
    startDate: {
      type: String, // 开始日期，格式为 "YYYY-MM-DD"
      value: "2024-11-01", // 默认开始日期
    },
    endDate: {
      type: String, // 截止日期，格式为 "YYYY-MM-DD"
      value: utils.formatTime2(new Date()), // 默认截止日期为今天
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    dateList: [], // 日历数据数组
    swiperCurrent: 0, // 日历轮播正处在哪个索引位置
    dateCurrent: new Date(), // 正选择的当前日期
    dateCurrentStr: "", // 正选择日期的 id
    dateMonth: "1月", // 正显示的月份
    dateListArray: ["日", "一", "二", "三", "四", "五", "六"],
  },

  ready: function () {
    var today = utils.formatTime2(new Date());
    this.setData({
      today,
    });

    // 解析开始日期和截止日期
    const startDate = new Date(this.properties.startDate);
    const endDate = new Date(this.properties.endDate);

    // 初始化日期范围
    this.initDateRange(startDate, endDate);
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化日期范围
    initDateRange(startDate, endDate) {
      const startWeek = utils.FirstDayInThisWeek(startDate); // 开始日期的周的第一天
      const endWeek = utils.FirstDayInThisWeek(endDate); // 截止日期的周的第一天

      let currentDate = new Date(startWeek);
      const dateList = [];

      // 生成从开始日期到截止日期的所有周
      while (currentDate <= endWeek) {
        dateList.push(this.calculateDate(currentDate));
        currentDate = utils.DateAddDay(currentDate, 7); // 增加一周
      }

      this.setData({
        dateList,
        swiperCurrent: dateList.length - 1, // 默认显示最后一周
        dateCurrent: endDate,
        dateCurrentStr: utils.formatTime2(endDate),
        dateMonth: (endDate.getMonth() + 1) + "月",
        dateYear: endDate.getFullYear() + "年",
      });
    },

    // 获取这周从周日到周六的日期
    calculateDate(_date) {
      var first = utils.FirstDayInThisWeek(_date);
      var d = {
        month: first.getMonth() + 1,
        days: [],
      };
      for (var i = 0; i < 7; i++) {
        var dd = utils.DateAddDay(first, i);
        var day = utils.addZero(dd.getDate()),
          year = utils.addZero(dd.getFullYear()),
          month = utils.addZero(dd.getMonth() + 1);

        d.days.push({
          day: day,
          id: dd.getFullYear() + "-" + month + "-" + day,
          ids: dd.getFullYear() + "," + month + "," + day,
        });
      }
      return d;
    },

    // 日历组件轮播切换
    dateSwiperChange(e) {
      // const lastIndex = this.data.swiperCurrent,
      //   currentIndex = e.detail.current,
      //   dateList = this.data.dateList;

      // // 更新当前显示的月份和年份
      // var dDateFormat = dateList[currentIndex].days[3].ids.split(",");
      // this.setData({
      //   swiperCurrent: currentIndex,
      //   dateMonth: dDateFormat[1] + "月",
      //   dateYear: dDateFormat[0] + "年",
      // });
    },

    // 点击日历某日
    chooseDate(e) {
      var str = e.currentTarget.id;
      console.log(str, '555');
      this.setData({
        dateCurrentStr: str,
      });
      this.triggerEvent("mydata", {
        data: str,
      });
    },
  },
});

