const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
let PAGE_OPTIONS = {}
Page({
  data: {
    isComplete: false, //是否加载完成
    questionData: null,
    historyList: [],
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    PAGE_OPTIONS = options
    await this.getQuestionDeatail()
  },
  onShow() {},
  getQuestionDeatail() {
    const param = {
      type: PAGE_OPTIONS.type,
      question_id: PAGE_OPTIONS?.question_id,
      item_no: PAGE_OPTIONS?.item_no,
    }
    // const param = {
    //   type: "accumulation",
    //   item_no: 1,
    //   question_id: 11448022,
    // }
    return UTIL.request(API.getRecordHistory, param).then((res) => {
      const resData = res.data
      this.setData({
        questionData: resData,
        historyList: resData.record_history,
        isComplete: true,
      })
    })
  },
  // 去报告页
  goRecordId(e) {
    const record_id = e.currentTarget.dataset.id
    ROUTER.navigateTo({
      path: "/pages/question/result/index",
      query: {
        record_id,
      },
    })
  },
  getPageShareParams() {
    let query = {}
    return APP.createShareParams({
      title: "练面试！金标尺。AI考官实战，真题免费刷；弱点秒杀，高分稳拿！",
      imageUrl: "",
      path: "/pages/question/single-detail/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    // const params = this.getPageShareParams()
    // delete params.imageUrl
    // const result = APP.createShareParams(params)
    // result.query = ROUTER.convertPathQuery(result.query)
    // return result
  },
})
