// components/home-header/index.js
// const APP = getApp()
// const API = require("@/config/api")
// const UTIL = require("@/utils/util")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
  },
  properties: {
    show_white: {
      type: Boolean,
      value: false,
    },
    pageTitle: {
      type: String,
      value: "模拟面试",
    },
  },
  data: {},
  attached() {
    // console.log("组件加载了")
  },
  ready() {
    // console.log("组件布局完成了")
  },
  methods: {
    tapButton() {
      this.triggerEvent("backPage")
    },
  },
})
