const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
Page({
  data: {
    accumulateTab: [
      {
        id: 99,
        isRequest: true,
        page: 1,
        dataList: [],
      },
    ],
    active: 99,
    tag_id: null, // 有tab的套卷列表
    isComplete: false,
    b_id: null, //无tab的套卷列表
    pageTitle: "",
    share_info: {},
    stickyHeight: null,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    const { tag_id, b_id } = options
    this.pageOptions = options
    if (tag_id) {
      this.setData({
        tag_id,
      })
      this.getTabList()
    } else {
      this.setData({
        b_id,
      })
      this.getList(1)
    }
  },
  async getTabList() {
    const newObj = BASE_CACHE.getBaseCache() || {}
    const res = await UTIL.request(API.getBookLetData, {
      tag_id: this.data.tag_id,
    })
    if (res?.error?.code === 0) {
      let arr = res.data.booklet_list || []
      let id = null
      if (arr.length) {
        arr.forEach((item) => {
          item.dataList = []
          item.page = 1
          item.isRequest = true
        })
        id = arr.find((item) => item.province == newObj.province?.key)?.id
      }

      this.setData({
        accumulateTab: arr,
        active: id ? id : arr[0]?.id || null,
        pageTitle: res.data.tag_title,
        share_info: res.data.share_info,
      })
      this.getList(1)
    }
  },
  getHeight() {
    if (this.data.stickyHeight) {
      return
    }
    const query = wx.createSelectorQuery()
    query
      .select(".main-content") // 替换为你的类名
      .boundingClientRect((rect) => {
        if (rect) {
          let heightText = "height: calc(100vh - " + rect.top + "px)"
          this.setData({
            stickyHeight: heightText,
          })
          console.log("距离顶部高度: ", rect.top) // 输出距离顶部的高度
        }
      })
      .exec()
  },
  onChange(e) {
    this.setData({
      active: this.data.accumulateTab[e.detail.index].id,
    })
    let activeIndex = this.data.accumulateTab.findIndex(
      (item) => item.id == this.data.active
    )
    if (!this.data.accumulateTab[activeIndex].dataList.length) {
      this.getList(1)
    }
  },
  async getList(type) {
    let accumulateTabArr = this.data.accumulateTab
    let activeIndex = accumulateTabArr.findIndex(
      (item) => item.id == this.data.active
    )
    const res = await UTIL.request(API.getBookLetList, {
      b_id: this.data.tag_id ? this.data.active : this.data.b_id,
      page: accumulateTabArr[activeIndex].page,
    })
    let title = this.data.tag_id ? this.data.tag_title : res.data.title
    let info = this.data.tag_id ? this.data.share_info : res.data.share_info
    let isRequest = accumulateTabArr[activeIndex].isRequest
    isRequest = res.data.list.length < 1 ? false : true
    let arr = []
    let newArr = res.data.list || []
    if (type == 1) {
      arr = newArr
    } else {
      arr = accumulateTabArr[activeIndex].dataList.concat(newArr)
    }
    accumulateTabArr[activeIndex].dataList = arr
    accumulateTabArr[activeIndex].isRequest = isRequest
    if (res?.error?.code === 0) {
      this.setData({
        accumulateTab: accumulateTabArr,
        isComplete: true,
        pageTitle: title,
        share_info: info,
      })
    }
    this.getHeight()
  },
  goDetail(e) {
    const { id } = e.currentTarget.dataset
    ROUTER.navigateTo({
      path: "/pages/situation/sheetDetail/index",
      query: {
        id,
      },
    })
  },
  onShow() {},

  onScrollToLower() {
    let accumulateTabArr = this.data.accumulateTab
    let activeIndex = accumulateTabArr.findIndex(
      (item) => item.id == this.data.active
    )
    if (!accumulateTabArr[activeIndex].isRequest) {
      return
    }
    accumulateTabArr[activeIndex].page = accumulateTabArr[activeIndex].page + 1
    this.setData({
      //改变页码
      accumulateTab: accumulateTabArr,
    })
    this.getList(2)
  },
  getPageShareParams() {
    let query = this.pageOptions

    return APP.createShareParams({
      title: this.data.share_info.title || "",
      imageUrl: this.data.share_info.image || "",
      path: "/pages/situation/sheetList/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },

  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
