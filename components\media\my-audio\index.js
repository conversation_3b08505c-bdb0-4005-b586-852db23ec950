const UTIL = require("@/utils/util")

Component({
  properties: {
    audioUrl: {
      type: String,
      value: "",
    },
    audioId: {
      type: String,
      value: null,
    },
    isPlaying: {
      type: Boolean,
    },
    isMultiple: {
      type: Boolean,
    },
  },
  data: {
    duration: "00:00", // 音频总时长
    currentTime: "00:00", // 当前播放时间
    isDragging: false,
    progressWidth: 0,
    progressBarWidth: 0,
    progressBarRect: null,
    isMultiple: false, // 单题模式下判断是否播放
    isComponentDestroyed: false, // 单题模式下判断是否播放
  },
  lifetimes: {
    attached() {
      this.initAudioContext()
    },
    detached() {
      // 组件销毁时停止播放并释放资源
      if (this.audioCtx) {
        this.audioCtx.stop()
        this.audioCtx.destroy()
      }
      this.setData({ isComponentDestroyed: true })
    },
  },
  methods: {
    initAudioContext() {
      this.audioCtx = wx.createInnerAudioContext()
      this.audioCtx.src = this.properties.audioUrl

      // 监听canplay事件
      this.audioCtx.onCanplay(() => {
        if (this.data.duration > 0) {
          return
        }
        this.checkDuration(this.audioCtx, (duration) => {
          this.setData({
            duration: this.formatTime(duration),
          })
        })
        this.audioCtx.play()
        this.audioCtx.pause()
      })

      // 监听ended事件
      this.audioCtx.onEnded(() => {
        this.setData({
          currentTime: "00:00",
          progressWidth: 0,
        })
        if (!this.data.isMultiple) {
          this.setData({
            showPlay: false,
          })
        } else {
          this.triggerEvent("pauseAudio", { mediaId: this.properties.audioId })
        }
      })

      this.audioCtx.onTimeUpdate(() => {
        this.setProgress(this.audioCtx.currentTime)
      })

      this.updateProgressBarRect()
    },
    // 定义一个函数用于检查音频时长
    checkDuration(audioCtx, callback) {
      if (this.data.isComponentDestroyed) {
        return // 如果组件已销毁，停止递归
      }
      if (audioCtx.duration > 0 && !isNaN(audioCtx.duration)) {
        // 如果已经获取到有效的时长，则调用回调函数
        callback(audioCtx.duration)
      } else {
        // 否则，稍候再试
        setTimeout(() => this.checkDuration(audioCtx, callback), 500) // 每隔500毫秒重试一次
      }
    },
    pause() {
      this.audioCtx.pause()
    },
    play() {
      this.audioCtx.play()
    },
    togglePlay() {
      console.log(
        this.data.isPlaying,
        this.data.isMultiple,
        this.data.audioId,
        "13123123123"
      )
      if (!this.data.isMultiple) {
        if (this.data.showPlay) {
          this.pause()
          this.setData({
            showPlay: false,
          })
        } else {
          this.play()
          this.setData({
            showPlay: true,
          })
          this.triggerEvent("singlePlayVideo")
        }
      } else {
        if (this.data.isPlaying) {
          this.pause()
          this.triggerEvent("pauseAudio", { mediaId: this.properties.audioId })
        } else {
          this.play()
          this.triggerEvent("playAudio", { mediaId: this.properties.audioId })
        }
      }
    },

    onTouchStart(e) {
      this.setData({ isDragging: true })
      this.updateProgressBarRect()
    },

    onTouchMove: UTIL.throttle(function (e) {
      if (this.data.isDragging) {
        const progressBarWidth = this.data.progressBarWidth
        if (progressBarWidth <= 0) return

        const progressBarRect = this.data.progressBarRect
        const touchXRelativeToProgressBar =
          e.touches[0].clientX - progressBarRect.left

        let progressWidth = Math.max(
          0,
          Math.min((touchXRelativeToProgressBar / progressBarWidth) * 100, 100)
        )
        let currentTime = (progressWidth / 100) * this.audioCtx.duration

        this.setData({
          progressWidth: progressWidth,
          currentTime: this.formatTime(currentTime),
        })
      }
    }, 10),

    onTouchEnd(e) {
      console.log(e, "222")
      const progressBarWidth = this.data.progressBarWidth
      const progressBarRect = this.data.progressBarRect
      const touchXRelativeToProgressBar =
        e.changedTouches[0].clientX - progressBarRect.left

      let progressWidth = Math.max(
        0,
        Math.min((touchXRelativeToProgressBar / progressBarWidth) * 100, 100)
      )
      let currentTime = (progressWidth / 100) * this.audioCtx.duration
      this.audioCtx.seek(currentTime)
      this.setData({
        progressWidth: progressWidth,
        currentTime: this.formatTime(currentTime),
        isDragging: false,
      })
    },

    updateProgressBarRect() {
      const that = this
      wx.createSelectorQuery()
        .in(this)
        .select("#progressBar")
        .boundingClientRect(function (rect) {
          if (rect) {
            that.setData({
              progressBarRect: rect,
              progressBarWidth: rect.width,
            })
          }
        })
        .exec()
    },

    setProgress(currentTime) {
      if (!this.data.isDragging) {
        const progressWidth = (currentTime / this.audioCtx.duration) * 100

        this.setData({
          currentTime: this.formatTime(currentTime),
          progressWidth: progressWidth,
        })
      }
    },

    formatTime(time) {
      const roundedTime = Math.round(time)
      const minutes = Math.floor(roundedTime / 60)
      const seconds = roundedTime % 60
      console.log(time, minutes, seconds)
      return `${minutes}:${seconds.toString().padStart(2, "0")}`
    },
  },
})
