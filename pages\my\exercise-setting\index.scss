.exercise-setting {
  box-sizing: border-box;
  padding: 32rpx;
  .model-list {
    position: relative;
    width: 100%;
    height: 188rpx;
    box-sizing: border-box;
    padding: 2rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    .bg-image {
      width: 184rpx;
      height: 184rpx;
      margin-right: 24rpx;
    }
    .right-area {
      flex: 1;
      padding-right: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .select-icon {
        width: 40rpx;
        height: 40rpx;
        margin-left: 44rpx;
      }
      .text-area {
        flex: 1;
        .title {
          font-weight: 500;
          font-size: 32rpx;
          color: #3c3d42;
          margin-bottom: 24rpx;
        }
        .desc {
          font-size: 24rpx;
          font-weight: 400;
          color: #919499;
        }
      }
    }
    .select-bg-image {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
    }
  }
}
.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}
