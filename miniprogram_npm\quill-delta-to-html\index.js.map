{"version": 3, "sources": ["main.js", "QuillDeltaToHtmlConverter.js", "InsertOpsConverter.js", "DeltaInsertOp.js", "value-types.js", "InsertData.js", "OpAttributeSanitizer.js", "mentions/MentionSanitizer.js", "OpLinkSanitizer.js", "helpers/url.js", "funcs-html.js", "helpers/array.js", "InsertOpDenormalizer.js", "helpers/string.js", "helpers/object.js", "OpToHtmlConverter.js", "grouper/Grouper.js", "grouper/group-types.js", "grouper/ListNester.js", "grouper/TableGrouper.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AQxBA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AQxBA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AQxBA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ADGA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ADGA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ADGA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,ADGA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,ADGA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AENA,AJYA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AENA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AENA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ALeA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ALeA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ALeA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ACHA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,ACHA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,ACHA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA;AV+BA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA;AV+BA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AENA;AV+BA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar QuillDeltaToHtmlConverter_1 = require(\"./QuillDeltaToHtmlConverter\");\r\nexports.QuillDeltaToHtmlConverter = QuillDeltaToHtmlConverter_1.QuillDeltaToHtmlConverter;\r\nvar OpToHtmlConverter_1 = require(\"./OpToHtmlConverter\");\r\nexports.OpToHtmlConverter = OpToHtmlConverter_1.OpToHtmlConverter;\r\nvar group_types_1 = require(\"./grouper/group-types\");\r\nexports.InlineGroup = group_types_1.InlineGroup;\r\nexports.VideoItem = group_types_1.VideoItem;\r\nexports.BlockGroup = group_types_1.BlockGroup;\r\nexports.ListGroup = group_types_1.ListGroup;\r\nexports.ListItem = group_types_1.ListItem;\r\nexports.BlotBlock = group_types_1.BlotBlock;\r\nvar DeltaInsertOp_1 = require(\"./DeltaInsertOp\");\r\nexports.DeltaInsertOp = DeltaInsertOp_1.DeltaInsertOp;\r\nvar InsertData_1 = require(\"./InsertData\");\r\nexports.InsertDataQuill = InsertData_1.InsertDataQuill;\r\nexports.InsertDataCustom = InsertData_1.InsertDataCustom;\r\nvar value_types_1 = require(\"./value-types\");\r\nexports.NewLine = value_types_1.NewLine;\r\nexports.ListType = value_types_1.ListType;\r\nexports.ScriptType = value_types_1.ScriptType;\r\nexports.DirectionType = value_types_1.DirectionType;\r\nexports.AlignType = value_types_1.AlignType;\r\nexports.DataType = value_types_1.DataType;\r\nexports.GroupType = value_types_1.GroupType;\r\n", "\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result[\"default\"] = mod;\r\n    return result;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar InsertOpsConverter_1 = require(\"./InsertOpsConverter\");\r\nvar OpToHtmlConverter_1 = require(\"./OpToHtmlConverter\");\r\nvar Grouper_1 = require(\"./grouper/Grouper\");\r\nvar group_types_1 = require(\"./grouper/group-types\");\r\nvar ListNester_1 = require(\"./grouper/ListNester\");\r\nvar funcs_html_1 = require(\"./funcs-html\");\r\nvar obj = __importStar(require(\"./helpers/object\"));\r\nvar value_types_1 = require(\"./value-types\");\r\nvar TableGrouper_1 = require(\"./grouper/TableGrouper\");\r\nvar BrTag = '<br/>';\r\nvar QuillDeltaToHtmlConverter = (function () {\r\n    function QuillDeltaToHtmlConverter(deltaOps, options) {\r\n        this.rawDeltaOps = [];\r\n        this.callbacks = {};\r\n        this.options = obj.assign({\r\n            paragraphTag: 'p',\r\n            encodeHtml: true,\r\n            classPrefix: 'ql',\r\n            inlineStyles: false,\r\n            multiLineBlockquote: true,\r\n            multiLineHeader: true,\r\n            multiLineCodeblock: true,\r\n            multiLineParagraph: true,\r\n            multiLineCustomBlock: true,\r\n            allowBackgroundClasses: false,\r\n            linkTarget: '_blank',\r\n        }, options, {\r\n            orderedListTag: 'ol',\r\n            bulletListTag: 'ul',\r\n            listItemTag: 'li',\r\n        });\r\n        var inlineStyles;\r\n        if (!this.options.inlineStyles) {\r\n            inlineStyles = undefined;\r\n        }\r\n        else if (typeof this.options.inlineStyles === 'object') {\r\n            inlineStyles = this.options.inlineStyles;\r\n        }\r\n        else {\r\n            inlineStyles = {};\r\n        }\r\n        this.converterOptions = {\r\n            encodeHtml: this.options.encodeHtml,\r\n            classPrefix: this.options.classPrefix,\r\n            inlineStyles: inlineStyles,\r\n            listItemTag: this.options.listItemTag,\r\n            paragraphTag: this.options.paragraphTag,\r\n            linkRel: this.options.linkRel,\r\n            linkTarget: this.options.linkTarget,\r\n            allowBackgroundClasses: this.options.allowBackgroundClasses,\r\n            customTag: this.options.customTag,\r\n            customTagAttributes: this.options.customTagAttributes,\r\n            customCssClasses: this.options.customCssClasses,\r\n            customCssStyles: this.options.customCssStyles,\r\n        };\r\n        this.rawDeltaOps = deltaOps;\r\n    }\r\n    QuillDeltaToHtmlConverter.prototype._getListTag = function (op) {\r\n        return op.isOrderedList()\r\n            ? this.options.orderedListTag + ''\r\n            : op.isBulletList()\r\n                ? this.options.bulletListTag + ''\r\n                : op.isCheckedList()\r\n                    ? this.options.bulletListTag + ''\r\n                    : op.isUncheckedList()\r\n                        ? this.options.bulletListTag + ''\r\n                        : '';\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype.getGroupedOps = function () {\r\n        var deltaOps = InsertOpsConverter_1.InsertOpsConverter.convert(this.rawDeltaOps, this.options);\r\n        var pairedOps = Grouper_1.Grouper.pairOpsWithTheirBlock(deltaOps);\r\n        var groupedSameStyleBlocks = Grouper_1.Grouper.groupConsecutiveSameStyleBlocks(pairedOps, {\r\n            blockquotes: !!this.options.multiLineBlockquote,\r\n            header: !!this.options.multiLineHeader,\r\n            codeBlocks: !!this.options.multiLineCodeblock,\r\n            customBlocks: !!this.options.multiLineCustomBlock,\r\n        });\r\n        var groupedOps = Grouper_1.Grouper.reduceConsecutiveSameStyleBlocksToOne(groupedSameStyleBlocks);\r\n        var tableGrouper = new TableGrouper_1.TableGrouper();\r\n        groupedOps = tableGrouper.group(groupedOps);\r\n        var listNester = new ListNester_1.ListNester();\r\n        return listNester.nest(groupedOps);\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype.convert = function () {\r\n        var _this = this;\r\n        var groups = this.getGroupedOps();\r\n        return groups\r\n            .map(function (group) {\r\n            if (group instanceof group_types_1.ListGroup) {\r\n                return _this._renderWithCallbacks(value_types_1.GroupType.List, group, function () {\r\n                    return _this._renderList(group);\r\n                });\r\n            }\r\n            else if (group instanceof group_types_1.TableGroup) {\r\n                return _this._renderWithCallbacks(value_types_1.GroupType.Table, group, function () {\r\n                    return _this._renderTable(group);\r\n                });\r\n            }\r\n            else if (group instanceof group_types_1.BlockGroup) {\r\n                var g = group;\r\n                return _this._renderWithCallbacks(value_types_1.GroupType.Block, group, function () {\r\n                    return _this._renderBlock(g.op, g.ops);\r\n                });\r\n            }\r\n            else if (group instanceof group_types_1.BlotBlock) {\r\n                return _this._renderCustom(group.op, null);\r\n            }\r\n            else if (group instanceof group_types_1.VideoItem) {\r\n                return _this._renderWithCallbacks(value_types_1.GroupType.Video, group, function () {\r\n                    var g = group;\r\n                    var converter = new OpToHtmlConverter_1.OpToHtmlConverter(g.op, _this.converterOptions);\r\n                    return converter.getHtml();\r\n                });\r\n            }\r\n            else {\r\n                return _this._renderWithCallbacks(value_types_1.GroupType.InlineGroup, group, function () {\r\n                    return _this._renderInlines(group.ops, true);\r\n                });\r\n            }\r\n        })\r\n            .join('');\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderWithCallbacks = function (groupType, group, myRenderFn) {\r\n        var html = '';\r\n        var beforeCb = this.callbacks['beforeRender_cb'];\r\n        html =\r\n            typeof beforeCb === 'function'\r\n                ? beforeCb.apply(null, [groupType, group])\r\n                : '';\r\n        if (!html) {\r\n            html = myRenderFn();\r\n        }\r\n        var afterCb = this.callbacks['afterRender_cb'];\r\n        html =\r\n            typeof afterCb === 'function'\r\n                ? afterCb.apply(null, [groupType, html])\r\n                : html;\r\n        return html;\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderList = function (list) {\r\n        var _this = this;\r\n        var firstItem = list.items[0];\r\n        return (funcs_html_1.makeStartTag(this._getListTag(firstItem.item.op)) +\r\n            list.items.map(function (li) { return _this._renderListItem(li); }).join('') +\r\n            funcs_html_1.makeEndTag(this._getListTag(firstItem.item.op)));\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderListItem = function (li) {\r\n        li.item.op.attributes.indent = 0;\r\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(li.item.op, this.converterOptions);\r\n        var parts = converter.getHtmlParts();\r\n        var liElementsHtml = this._renderInlines(li.item.ops, false);\r\n        return (parts.openingTag +\r\n            liElementsHtml +\r\n            (li.innerList ? this._renderList(li.innerList) : '') +\r\n            parts.closingTag);\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderTable = function (table) {\r\n        var _this = this;\r\n        return (funcs_html_1.makeStartTag('table') +\r\n            funcs_html_1.makeStartTag('tbody') +\r\n            table.rows.map(function (row) { return _this._renderTableRow(row); }).join('') +\r\n            funcs_html_1.makeEndTag('tbody') +\r\n            funcs_html_1.makeEndTag('table'));\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderTableRow = function (row) {\r\n        var _this = this;\r\n        return (funcs_html_1.makeStartTag('tr') +\r\n            row.cells.map(function (cell) { return _this._renderTableCell(cell); }).join('') +\r\n            funcs_html_1.makeEndTag('tr'));\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderTableCell = function (cell) {\r\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(cell.item.op, this.converterOptions);\r\n        var parts = converter.getHtmlParts();\r\n        var cellElementsHtml = this._renderInlines(cell.item.ops, false);\r\n        return (funcs_html_1.makeStartTag('td', {\r\n            key: 'data-row',\r\n            value: cell.item.op.attributes.table,\r\n        }) +\r\n            parts.openingTag +\r\n            cellElementsHtml +\r\n            parts.closingTag +\r\n            funcs_html_1.makeEndTag('td'));\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderBlock = function (bop, ops) {\r\n        var _this = this;\r\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(bop, this.converterOptions);\r\n        var htmlParts = converter.getHtmlParts();\r\n        if (bop.isCodeBlock()) {\r\n            return (htmlParts.openingTag +\r\n                funcs_html_1.encodeHtml(ops\r\n                    .map(function (iop) {\r\n                    return iop.isCustomEmbed()\r\n                        ? _this._renderCustom(iop, bop)\r\n                        : iop.insert.value;\r\n                })\r\n                    .join('')) +\r\n                htmlParts.closingTag);\r\n        }\r\n        var inlines = ops.map(function (op) { return _this._renderInline(op, bop); }).join('');\r\n        return htmlParts.openingTag + (inlines || BrTag) + htmlParts.closingTag;\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderInlines = function (ops, isInlineGroup) {\r\n        var _this = this;\r\n        if (isInlineGroup === void 0) { isInlineGroup = true; }\r\n        var opsLen = ops.length - 1;\r\n        var html = ops\r\n            .map(function (op, i) {\r\n            if (i > 0 && i === opsLen && op.isJustNewline()) {\r\n                return '';\r\n            }\r\n            return _this._renderInline(op, null);\r\n        })\r\n            .join('');\r\n        if (!isInlineGroup) {\r\n            return html;\r\n        }\r\n        var startParaTag = funcs_html_1.makeStartTag(this.options.paragraphTag);\r\n        var endParaTag = funcs_html_1.makeEndTag(this.options.paragraphTag);\r\n        if (html === BrTag || this.options.multiLineParagraph) {\r\n            return startParaTag + html + endParaTag;\r\n        }\r\n        return (startParaTag +\r\n            html\r\n                .split(BrTag)\r\n                .map(function (v) {\r\n                return v === '' ? BrTag : v;\r\n            })\r\n                .join(endParaTag + startParaTag) +\r\n            endParaTag);\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderInline = function (op, contextOp) {\r\n        if (op.isCustomEmbed()) {\r\n            return this._renderCustom(op, contextOp);\r\n        }\r\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(op, this.converterOptions);\r\n        return converter.getHtml().replace(/\\n/g, BrTag);\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype._renderCustom = function (op, contextOp) {\r\n        var renderCb = this.callbacks['renderCustomOp_cb'];\r\n        if (typeof renderCb === 'function') {\r\n            return renderCb.apply(null, [op, contextOp]);\r\n        }\r\n        return '';\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype.beforeRender = function (cb) {\r\n        if (typeof cb === 'function') {\r\n            this.callbacks['beforeRender_cb'] = cb;\r\n        }\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype.afterRender = function (cb) {\r\n        if (typeof cb === 'function') {\r\n            this.callbacks['afterRender_cb'] = cb;\r\n        }\r\n    };\r\n    QuillDeltaToHtmlConverter.prototype.renderCustomWith = function (cb) {\r\n        this.callbacks['renderCustomOp_cb'] = cb;\r\n    };\r\n    return QuillDeltaToHtmlConverter;\r\n}());\r\nexports.QuillDeltaToHtmlConverter = QuillDeltaToHtmlConverter;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar DeltaInsertOp_1 = require(\"./DeltaInsertOp\");\r\nvar value_types_1 = require(\"./value-types\");\r\nvar InsertData_1 = require(\"./InsertData\");\r\nvar OpAttributeSanitizer_1 = require(\"./OpAttributeSanitizer\");\r\nvar InsertOpDenormalizer_1 = require(\"./InsertOpDenormalizer\");\r\nvar OpLinkSanitizer_1 = require(\"./OpLinkSanitizer\");\r\nvar InsertOpsConverter = (function () {\r\n    function InsertOpsConverter() {\r\n    }\r\n    InsertOpsConverter.convert = function (deltaOps, options) {\r\n        if (!Array.isArray(deltaOps)) {\r\n            return [];\r\n        }\r\n        var denormalizedOps = [].concat.apply([], deltaOps.map(InsertOpDenormalizer_1.InsertOpDenormalizer.denormalize));\r\n        var results = [];\r\n        var insertVal, attributes;\r\n        for (var _i = 0, denormalizedOps_1 = denormalizedOps; _i < denormalizedOps_1.length; _i++) {\r\n            var op = denormalizedOps_1[_i];\r\n            if (!op.insert) {\r\n                continue;\r\n            }\r\n            insertVal = InsertOpsConverter.convertInsertVal(op.insert, options);\r\n            if (!insertVal) {\r\n                continue;\r\n            }\r\n            attributes = OpAttributeSanitizer_1.OpAttributeSanitizer.sanitize(op.attributes, options);\r\n            results.push(new DeltaInsertOp_1.DeltaInsertOp(insertVal, attributes));\r\n        }\r\n        return results;\r\n    };\r\n    InsertOpsConverter.convertInsertVal = function (insertPropVal, sanitizeOptions) {\r\n        if (typeof insertPropVal === 'string') {\r\n            return new InsertData_1.InsertDataQuill(value_types_1.DataType.Text, insertPropVal);\r\n        }\r\n        if (!insertPropVal || typeof insertPropVal !== 'object') {\r\n            return null;\r\n        }\r\n        var keys = Object.keys(insertPropVal);\r\n        if (!keys.length) {\r\n            return null;\r\n        }\r\n        return value_types_1.DataType.Image in insertPropVal\r\n            ? new InsertData_1.InsertDataQuill(value_types_1.DataType.Image, OpLinkSanitizer_1.OpLinkSanitizer.sanitize(insertPropVal[value_types_1.DataType.Image] + '', sanitizeOptions))\r\n            : value_types_1.DataType.Video in insertPropVal\r\n                ? new InsertData_1.InsertDataQuill(value_types_1.DataType.Video, OpLinkSanitizer_1.OpLinkSanitizer.sanitize(insertPropVal[value_types_1.DataType.Video] + '', sanitizeOptions))\r\n                : value_types_1.DataType.Formula in insertPropVal\r\n                    ? new InsertData_1.InsertDataQuill(value_types_1.DataType.Formula, insertPropVal[value_types_1.DataType.Formula])\r\n                    :\r\n                        new InsertData_1.InsertDataCustom(keys[0], insertPropVal[keys[0]]);\r\n    };\r\n    return InsertOpsConverter;\r\n}());\r\nexports.InsertOpsConverter = InsertOpsConverter;\r\n", "\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar value_types_1 = require(\"./value-types\");\r\nvar InsertData_1 = require(\"./InsertData\");\r\nvar lodash_isequal_1 = __importDefault(require(\"lodash.isequal\"));\r\nvar DeltaInsertOp = (function () {\r\n    function DeltaInsertOp(insertVal, attrs) {\r\n        if (typeof insertVal === 'string') {\r\n            insertVal = new InsertData_1.InsertDataQuill(value_types_1.DataType.Text, insertVal + '');\r\n        }\r\n        this.insert = insertVal;\r\n        this.attributes = attrs || {};\r\n    }\r\n    DeltaInsertOp.createNewLineOp = function () {\r\n        return new DeltaInsertOp(value_types_1.NewLine);\r\n    };\r\n    DeltaInsertOp.prototype.isContainerBlock = function () {\r\n        return (this.isBlockquote() ||\r\n            this.isList() ||\r\n            this.isTable() ||\r\n            this.isCodeBlock() ||\r\n            this.isHeader() ||\r\n            this.isBlockAttribute() ||\r\n            this.isCustomTextBlock());\r\n    };\r\n    DeltaInsertOp.prototype.isBlockAttribute = function () {\r\n        var attrs = this.attributes;\r\n        return !!(attrs.align || attrs.direction || attrs.indent);\r\n    };\r\n    DeltaInsertOp.prototype.isBlockquote = function () {\r\n        return !!this.attributes.blockquote;\r\n    };\r\n    DeltaInsertOp.prototype.isHeader = function () {\r\n        return !!this.attributes.header;\r\n    };\r\n    DeltaInsertOp.prototype.isTable = function () {\r\n        return !!this.attributes.table;\r\n    };\r\n    DeltaInsertOp.prototype.isSameHeaderAs = function (op) {\r\n        return op.attributes.header === this.attributes.header && this.isHeader();\r\n    };\r\n    DeltaInsertOp.prototype.hasSameAdiAs = function (op) {\r\n        return (this.attributes.align === op.attributes.align &&\r\n            this.attributes.direction === op.attributes.direction &&\r\n            this.attributes.indent === op.attributes.indent);\r\n    };\r\n    DeltaInsertOp.prototype.hasSameIndentationAs = function (op) {\r\n        return this.attributes.indent === op.attributes.indent;\r\n    };\r\n    DeltaInsertOp.prototype.hasSameAttr = function (op) {\r\n        return lodash_isequal_1.default(this.attributes, op.attributes);\r\n    };\r\n    DeltaInsertOp.prototype.hasHigherIndentThan = function (op) {\r\n        return ((Number(this.attributes.indent) || 0) >\r\n            (Number(op.attributes.indent) || 0));\r\n    };\r\n    DeltaInsertOp.prototype.isInline = function () {\r\n        return !(this.isContainerBlock() ||\r\n            this.isVideo() ||\r\n            this.isCustomEmbedBlock());\r\n    };\r\n    DeltaInsertOp.prototype.isCodeBlock = function () {\r\n        return !!this.attributes['code-block'];\r\n    };\r\n    DeltaInsertOp.prototype.hasSameLangAs = function (op) {\r\n        return this.attributes['code-block'] === op.attributes['code-block'];\r\n    };\r\n    DeltaInsertOp.prototype.isJustNewline = function () {\r\n        return this.insert.value === value_types_1.NewLine;\r\n    };\r\n    DeltaInsertOp.prototype.isList = function () {\r\n        return (this.isOrderedList() ||\r\n            this.isBulletList() ||\r\n            this.isCheckedList() ||\r\n            this.isUncheckedList());\r\n    };\r\n    DeltaInsertOp.prototype.isOrderedList = function () {\r\n        return this.attributes.list === value_types_1.ListType.Ordered;\r\n    };\r\n    DeltaInsertOp.prototype.isBulletList = function () {\r\n        return this.attributes.list === value_types_1.ListType.Bullet;\r\n    };\r\n    DeltaInsertOp.prototype.isCheckedList = function () {\r\n        return this.attributes.list === value_types_1.ListType.Checked;\r\n    };\r\n    DeltaInsertOp.prototype.isUncheckedList = function () {\r\n        return this.attributes.list === value_types_1.ListType.Unchecked;\r\n    };\r\n    DeltaInsertOp.prototype.isACheckList = function () {\r\n        return (this.attributes.list == value_types_1.ListType.Unchecked ||\r\n            this.attributes.list === value_types_1.ListType.Checked);\r\n    };\r\n    DeltaInsertOp.prototype.isSameListAs = function (op) {\r\n        return (!!op.attributes.list &&\r\n            (this.attributes.list === op.attributes.list ||\r\n                (op.isACheckList() && this.isACheckList())));\r\n    };\r\n    DeltaInsertOp.prototype.isSameTableRowAs = function (op) {\r\n        return (!!op.isTable() &&\r\n            this.isTable() &&\r\n            this.attributes.table === op.attributes.table);\r\n    };\r\n    DeltaInsertOp.prototype.isText = function () {\r\n        return this.insert.type === value_types_1.DataType.Text;\r\n    };\r\n    DeltaInsertOp.prototype.isImage = function () {\r\n        return this.insert.type === value_types_1.DataType.Image;\r\n    };\r\n    DeltaInsertOp.prototype.isFormula = function () {\r\n        return this.insert.type === value_types_1.DataType.Formula;\r\n    };\r\n    DeltaInsertOp.prototype.isVideo = function () {\r\n        return this.insert.type === value_types_1.DataType.Video;\r\n    };\r\n    DeltaInsertOp.prototype.isLink = function () {\r\n        return this.isText() && !!this.attributes.link;\r\n    };\r\n    DeltaInsertOp.prototype.isCustomEmbed = function () {\r\n        return this.insert instanceof InsertData_1.InsertDataCustom;\r\n    };\r\n    DeltaInsertOp.prototype.isCustomEmbedBlock = function () {\r\n        return this.isCustomEmbed() && !!this.attributes.renderAsBlock;\r\n    };\r\n    DeltaInsertOp.prototype.isCustomTextBlock = function () {\r\n        return this.isText() && !!this.attributes.renderAsBlock;\r\n    };\r\n    DeltaInsertOp.prototype.isMentions = function () {\r\n        return this.isText() && !!this.attributes.mentions;\r\n    };\r\n    return DeltaInsertOp;\r\n}());\r\nexports.DeltaInsertOp = DeltaInsertOp;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar NewLine = '\\n';\r\nexports.NewLine = NewLine;\r\nvar ListType;\r\n(function (ListType) {\r\n    ListType[\"Ordered\"] = \"ordered\";\r\n    ListType[\"Bullet\"] = \"bullet\";\r\n    ListType[\"Checked\"] = \"checked\";\r\n    ListType[\"Unchecked\"] = \"unchecked\";\r\n})(ListType || (ListType = {}));\r\nexports.ListType = ListType;\r\nvar ScriptType;\r\n(function (ScriptType) {\r\n    ScriptType[\"Sub\"] = \"sub\";\r\n    ScriptType[\"Super\"] = \"super\";\r\n})(ScriptType || (ScriptType = {}));\r\nexports.ScriptType = ScriptType;\r\nvar DirectionType;\r\n(function (DirectionType) {\r\n    DirectionType[\"Rtl\"] = \"rtl\";\r\n})(DirectionType || (DirectionType = {}));\r\nexports.DirectionType = DirectionType;\r\nvar AlignType;\r\n(function (AlignType) {\r\n    AlignType[\"Left\"] = \"left\";\r\n    AlignType[\"Center\"] = \"center\";\r\n    AlignType[\"Right\"] = \"right\";\r\n    AlignType[\"Justify\"] = \"justify\";\r\n})(AlignType || (AlignType = {}));\r\nexports.AlignType = AlignType;\r\nvar DataType;\r\n(function (DataType) {\r\n    DataType[\"Image\"] = \"image\";\r\n    DataType[\"Video\"] = \"video\";\r\n    DataType[\"Formula\"] = \"formula\";\r\n    DataType[\"Text\"] = \"text\";\r\n})(DataType || (DataType = {}));\r\nexports.DataType = DataType;\r\nvar GroupType;\r\n(function (GroupType) {\r\n    GroupType[\"Block\"] = \"block\";\r\n    GroupType[\"InlineGroup\"] = \"inline-group\";\r\n    GroupType[\"List\"] = \"list\";\r\n    GroupType[\"Video\"] = \"video\";\r\n    GroupType[\"Table\"] = \"table\";\r\n})(GroupType || (GroupType = {}));\r\nexports.GroupType = GroupType;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar InsertDataQuill = (function () {\r\n    function InsertDataQuill(type, value) {\r\n        this.type = type;\r\n        this.value = value;\r\n    }\r\n    return InsertDataQuill;\r\n}());\r\nexports.InsertDataQuill = InsertDataQuill;\r\nvar InsertDataCustom = (function () {\r\n    function InsertDataCustom(type, value) {\r\n        this.type = type;\r\n        this.value = value;\r\n    }\r\n    return InsertDataCustom;\r\n}());\r\nexports.InsertDataCustom = InsertDataCustom;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar value_types_1 = require(\"./value-types\");\r\nvar MentionSanitizer_1 = require(\"./mentions/MentionSanitizer\");\r\nvar array_1 = require(\"./helpers/array\");\r\nvar OpLinkSanitizer_1 = require(\"./OpLinkSanitizer\");\r\nvar OpAttributeSanitizer = (function () {\r\n    function OpAttributeSanitizer() {\r\n    }\r\n    OpAttributeSanitizer.sanitize = function (dirtyAttrs, sanitizeOptions) {\r\n        var cleanAttrs = {};\r\n        if (!dirtyAttrs || typeof dirtyAttrs !== 'object') {\r\n            return cleanAttrs;\r\n        }\r\n        var booleanAttrs = [\r\n            'bold',\r\n            'italic',\r\n            'underline',\r\n            'strike',\r\n            'code',\r\n            'blockquote',\r\n            'code-block',\r\n            'renderAsBlock',\r\n        ];\r\n        var colorAttrs = ['background', 'color'];\r\n        var font = dirtyAttrs.font, size = dirtyAttrs.size, link = dirtyAttrs.link, script = dirtyAttrs.script, list = dirtyAttrs.list, header = dirtyAttrs.header, align = dirtyAttrs.align, direction = dirtyAttrs.direction, indent = dirtyAttrs.indent, mentions = dirtyAttrs.mentions, mention = dirtyAttrs.mention, width = dirtyAttrs.width, target = dirtyAttrs.target, rel = dirtyAttrs.rel;\r\n        var codeBlock = dirtyAttrs['code-block'];\r\n        var sanitizedAttrs = booleanAttrs.concat(colorAttrs, [\r\n            'font',\r\n            'size',\r\n            'link',\r\n            'script',\r\n            'list',\r\n            'header',\r\n            'align',\r\n            'direction',\r\n            'indent',\r\n            'mentions',\r\n            'mention',\r\n            'width',\r\n            'target',\r\n            'rel',\r\n            'code-block',\r\n        ]);\r\n        booleanAttrs.forEach(function (prop) {\r\n            var v = dirtyAttrs[prop];\r\n            if (v) {\r\n                cleanAttrs[prop] = !!v;\r\n            }\r\n        });\r\n        colorAttrs.forEach(function (prop) {\r\n            var val = dirtyAttrs[prop];\r\n            if (val &&\r\n                (OpAttributeSanitizer.IsValidHexColor(val + '') ||\r\n                    OpAttributeSanitizer.IsValidColorLiteral(val + '') ||\r\n                    OpAttributeSanitizer.IsValidRGBColor(val + ''))) {\r\n                cleanAttrs[prop] = val;\r\n            }\r\n        });\r\n        if (font && OpAttributeSanitizer.IsValidFontName(font + '')) {\r\n            cleanAttrs.font = font;\r\n        }\r\n        if (size && OpAttributeSanitizer.IsValidSize(size + '')) {\r\n            cleanAttrs.size = size;\r\n        }\r\n        if (width && OpAttributeSanitizer.IsValidWidth(width + '')) {\r\n            cleanAttrs.width = width;\r\n        }\r\n        if (link) {\r\n            cleanAttrs.link = OpLinkSanitizer_1.OpLinkSanitizer.sanitize(link + '', sanitizeOptions);\r\n        }\r\n        if (target && OpAttributeSanitizer.isValidTarget(target)) {\r\n            cleanAttrs.target = target;\r\n        }\r\n        if (rel && OpAttributeSanitizer.IsValidRel(rel)) {\r\n            cleanAttrs.rel = rel;\r\n        }\r\n        if (codeBlock) {\r\n            if (OpAttributeSanitizer.IsValidLang(codeBlock)) {\r\n                cleanAttrs['code-block'] = codeBlock;\r\n            }\r\n            else {\r\n                cleanAttrs['code-block'] = !!codeBlock;\r\n            }\r\n        }\r\n        if (script === value_types_1.ScriptType.Sub || value_types_1.ScriptType.Super === script) {\r\n            cleanAttrs.script = script;\r\n        }\r\n        if (list === value_types_1.ListType.Bullet ||\r\n            list === value_types_1.ListType.Ordered ||\r\n            list === value_types_1.ListType.Checked ||\r\n            list === value_types_1.ListType.Unchecked) {\r\n            cleanAttrs.list = list;\r\n        }\r\n        if (Number(header)) {\r\n            cleanAttrs.header = Math.min(Number(header), 6);\r\n        }\r\n        if (array_1.find([value_types_1.AlignType.Center, value_types_1.AlignType.Right, value_types_1.AlignType.Justify, value_types_1.AlignType.Left], function (a) { return a === align; })) {\r\n            cleanAttrs.align = align;\r\n        }\r\n        if (direction === value_types_1.DirectionType.Rtl) {\r\n            cleanAttrs.direction = direction;\r\n        }\r\n        if (indent && Number(indent)) {\r\n            cleanAttrs.indent = Math.min(Number(indent), 30);\r\n        }\r\n        if (mentions && mention) {\r\n            var sanitizedMention = MentionSanitizer_1.MentionSanitizer.sanitize(mention, sanitizeOptions);\r\n            if (Object.keys(sanitizedMention).length > 0) {\r\n                cleanAttrs.mentions = !!mentions;\r\n                cleanAttrs.mention = mention;\r\n            }\r\n        }\r\n        return Object.keys(dirtyAttrs).reduce(function (cleaned, k) {\r\n            if (sanitizedAttrs.indexOf(k) === -1) {\r\n                cleaned[k] = dirtyAttrs[k];\r\n            }\r\n            return cleaned;\r\n        }, cleanAttrs);\r\n    };\r\n    OpAttributeSanitizer.IsValidHexColor = function (colorStr) {\r\n        return !!colorStr.match(/^#([0-9A-F]{6}|[0-9A-F]{3})$/i);\r\n    };\r\n    OpAttributeSanitizer.IsValidColorLiteral = function (colorStr) {\r\n        return !!colorStr.match(/^[a-z]{1,50}$/i);\r\n    };\r\n    OpAttributeSanitizer.IsValidRGBColor = function (colorStr) {\r\n        var re = /^rgb\\(((0|25[0-5]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),\\s*){2}(0|25[0-5]|2[0-4]\\d|1\\d\\d|0?\\d?\\d)\\)$/i;\r\n        return !!colorStr.match(re);\r\n    };\r\n    OpAttributeSanitizer.IsValidFontName = function (fontName) {\r\n        return !!fontName.match(/^[a-z\\s0-9\\- ]{1,30}$/i);\r\n    };\r\n    OpAttributeSanitizer.IsValidSize = function (size) {\r\n        return !!size.match(/^[a-z0-9\\-]{1,20}$/i);\r\n    };\r\n    OpAttributeSanitizer.IsValidWidth = function (width) {\r\n        return !!width.match(/^[0-9]*(px|em|%)?$/);\r\n    };\r\n    OpAttributeSanitizer.isValidTarget = function (target) {\r\n        return !!target.match(/^[_a-zA-Z0-9\\-]{1,50}$/);\r\n    };\r\n    OpAttributeSanitizer.IsValidRel = function (relStr) {\r\n        return !!relStr.match(/^[a-zA-Z\\s\\-]{1,250}$/i);\r\n    };\r\n    OpAttributeSanitizer.IsValidLang = function (lang) {\r\n        if (typeof lang === 'boolean') {\r\n            return true;\r\n        }\r\n        return !!lang.match(/^[a-zA-Z\\s\\-\\\\\\/\\+]{1,50}$/i);\r\n    };\r\n    return OpAttributeSanitizer;\r\n}());\r\nexports.OpAttributeSanitizer = OpAttributeSanitizer;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar OpLinkSanitizer_1 = require(\"../OpLinkSanitizer\");\r\nvar MentionSanitizer = (function () {\r\n    function MentionSanitizer() {\r\n    }\r\n    MentionSanitizer.sanitize = function (dirtyObj, sanitizeOptions) {\r\n        var cleanObj = {};\r\n        if (!dirtyObj || typeof dirtyObj !== 'object') {\r\n            return cleanObj;\r\n        }\r\n        if (dirtyObj.class && MentionSanitizer.IsValidClass(dirtyObj.class)) {\r\n            cleanObj.class = dirtyObj.class;\r\n        }\r\n        if (dirtyObj.id && MentionSanitizer.IsValidId(dirtyObj.id)) {\r\n            cleanObj.id = dirtyObj.id;\r\n        }\r\n        if (MentionSanitizer.IsValidTarget(dirtyObj.target + '')) {\r\n            cleanObj.target = dirtyObj.target;\r\n        }\r\n        if (dirtyObj.avatar) {\r\n            cleanObj.avatar = OpLinkSanitizer_1.OpLinkSanitizer.sanitize(dirtyObj.avatar + '', sanitizeOptions);\r\n        }\r\n        if (dirtyObj['end-point']) {\r\n            cleanObj['end-point'] = OpLinkSanitizer_1.OpLinkSanitizer.sanitize(dirtyObj['end-point'] + '', sanitizeOptions);\r\n        }\r\n        if (dirtyObj.slug) {\r\n            cleanObj.slug = dirtyObj.slug + '';\r\n        }\r\n        return cleanObj;\r\n    };\r\n    MentionSanitizer.IsValidClass = function (classAttr) {\r\n        return !!classAttr.match(/^[a-zA-Z0-9_\\-]{1,500}$/i);\r\n    };\r\n    MentionSanitizer.IsValidId = function (idAttr) {\r\n        return !!idAttr.match(/^[a-zA-Z0-9_\\-\\:\\.]{1,500}$/i);\r\n    };\r\n    MentionSanitizer.IsValidTarget = function (target) {\r\n        return ['_self', '_blank', '_parent', '_top'].indexOf(target) > -1;\r\n    };\r\n    return MentionSanitizer;\r\n}());\r\nexports.MentionSanitizer = MentionSanitizer;\r\n", "\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result[\"default\"] = mod;\r\n    return result;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar url = __importStar(require(\"./helpers/url\"));\r\nvar funcs_html_1 = require(\"./funcs-html\");\r\nvar OpLinkSanitizer = (function () {\r\n    function OpLinkSanitizer() {\r\n    }\r\n    OpLinkSanitizer.sanitize = function (link, options) {\r\n        var sanitizerFn = function () {\r\n            return undefined;\r\n        };\r\n        if (options && typeof options.urlSanitizer === 'function') {\r\n            sanitizerFn = options.urlSanitizer;\r\n        }\r\n        var result = sanitizerFn(link);\r\n        return typeof result === 'string' ? result : funcs_html_1.encodeLink(url.sanitize(link));\r\n    };\r\n    return OpLinkSanitizer;\r\n}());\r\nexports.OpLinkSanitizer = OpLinkSanitizer;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nfunction sanitize(str) {\r\n    var val = str;\r\n    val = val.replace(/^\\s*/gm, '');\r\n    var whiteList = /^((https?|s?ftp|file|blob|mailto|tel):|#|\\/|data:image\\/)/;\r\n    if (whiteList.test(val)) {\r\n        return val;\r\n    }\r\n    return 'unsafe:' + val;\r\n}\r\nexports.sanitize = sanitize;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar EncodeTarget;\r\n(function (EncodeTarget) {\r\n    EncodeTarget[EncodeTarget[\"Html\"] = 0] = \"Html\";\r\n    EncodeTarget[EncodeTarget[\"Url\"] = 1] = \"Url\";\r\n})(EncodeTarget || (EncodeTarget = {}));\r\nfunction makeStartTag(tag, attrs) {\r\n    if (attrs === void 0) { attrs = undefined; }\r\n    if (!tag) {\r\n        return '';\r\n    }\r\n    var attrsStr = '';\r\n    if (attrs) {\r\n        var arrAttrs = [].concat(attrs);\r\n        attrsStr = arrAttrs\r\n            .map(function (attr) {\r\n            return attr.key + (attr.value ? '=\"' + attr.value + '\"' : '');\r\n        })\r\n            .join(' ');\r\n    }\r\n    var closing = '>';\r\n    if (tag === 'img' || tag === 'br') {\r\n        closing = '/>';\r\n    }\r\n    return attrsStr ? \"<\" + tag + \" \" + attrsStr + closing : \"<\" + tag + closing;\r\n}\r\nexports.makeStartTag = makeStartTag;\r\nfunction makeEndTag(tag) {\r\n    if (tag === void 0) { tag = ''; }\r\n    return (tag && \"</\" + tag + \">\") || '';\r\n}\r\nexports.makeEndTag = makeEndTag;\r\nfunction decodeHtml(str) {\r\n    return encodeMappings(EncodeTarget.Html).reduce(decodeMapping, str);\r\n}\r\nexports.decodeHtml = decodeHtml;\r\nfunction encodeHtml(str, preventDoubleEncoding) {\r\n    if (preventDoubleEncoding === void 0) { preventDoubleEncoding = true; }\r\n    if (preventDoubleEncoding) {\r\n        str = decodeHtml(str);\r\n    }\r\n    return encodeMappings(EncodeTarget.Html).reduce(encodeMapping, str);\r\n}\r\nexports.encodeHtml = encodeHtml;\r\nfunction encodeLink(str) {\r\n    var linkMaps = encodeMappings(EncodeTarget.Url);\r\n    var decoded = linkMaps.reduce(decodeMapping, str);\r\n    return linkMaps.reduce(encodeMapping, decoded);\r\n}\r\nexports.encodeLink = encodeLink;\r\nfunction encodeMappings(mtype) {\r\n    var maps = [\r\n        ['&', '&amp;'],\r\n        ['<', '&lt;'],\r\n        ['>', '&gt;'],\r\n        ['\"', '&quot;'],\r\n        [\"'\", '&#x27;'],\r\n        ['\\\\/', '&#x2F;'],\r\n        ['\\\\(', '&#40;'],\r\n        ['\\\\)', '&#41;'],\r\n    ];\r\n    if (mtype === EncodeTarget.Html) {\r\n        return maps.filter(function (_a) {\r\n            var v = _a[0], _ = _a[1];\r\n            return v.indexOf('(') === -1 && v.indexOf(')') === -1;\r\n        });\r\n    }\r\n    else {\r\n        return maps.filter(function (_a) {\r\n            var v = _a[0], _ = _a[1];\r\n            return v.indexOf('/') === -1;\r\n        });\r\n    }\r\n}\r\nfunction encodeMapping(str, mapping) {\r\n    return str.replace(new RegExp(mapping[0], 'g'), mapping[1]);\r\n}\r\nfunction decodeMapping(str, mapping) {\r\n    return str.replace(new RegExp(mapping[1], 'g'), mapping[0].replace('\\\\', ''));\r\n}\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nfunction preferSecond(arr) {\r\n    if (arr.length === 0) {\r\n        return null;\r\n    }\r\n    return arr.length >= 2 ? arr[1] : arr[0];\r\n}\r\nexports.preferSecond = preferSecond;\r\nfunction flatten(arr) {\r\n    return arr.reduce(function (pv, v) {\r\n        return pv.concat(Array.isArray(v) ? flatten(v) : v);\r\n    }, []);\r\n}\r\nexports.flatten = flatten;\r\nfunction find(arr, predicate) {\r\n    if (Array.prototype.find) {\r\n        return Array.prototype.find.call(arr, predicate);\r\n    }\r\n    for (var i = 0; i < arr.length; i++) {\r\n        if (predicate(arr[i]))\r\n            return arr[i];\r\n    }\r\n    return undefined;\r\n}\r\nexports.find = find;\r\nfunction groupConsecutiveElementsWhile(arr, predicate) {\r\n    var groups = [];\r\n    var currElm, currGroup;\r\n    for (var i = 0; i < arr.length; i++) {\r\n        currElm = arr[i];\r\n        if (i > 0 && predicate(currElm, arr[i - 1])) {\r\n            currGroup = groups[groups.length - 1];\r\n            currGroup.push(currElm);\r\n        }\r\n        else {\r\n            groups.push([currElm]);\r\n        }\r\n    }\r\n    return groups.map(function (g) { return (g.length === 1 ? g[0] : g); });\r\n}\r\nexports.groupConsecutiveElementsWhile = groupConsecutiveElementsWhile;\r\nfunction sliceFromReverseWhile(arr, startIndex, predicate) {\r\n    var result = {\r\n        elements: [],\r\n        sliceStartsAt: -1,\r\n    };\r\n    for (var i = startIndex; i >= 0; i--) {\r\n        if (!predicate(arr[i])) {\r\n            break;\r\n        }\r\n        result.sliceStartsAt = i;\r\n        result.elements.unshift(arr[i]);\r\n    }\r\n    return result;\r\n}\r\nexports.sliceFromReverseWhile = sliceFromReverseWhile;\r\nfunction intersperse(arr, item) {\r\n    return arr.reduce(function (pv, v, index) {\r\n        pv.push(v);\r\n        if (index < arr.length - 1) {\r\n            pv.push(item);\r\n        }\r\n        return pv;\r\n    }, []);\r\n}\r\nexports.intersperse = intersperse;\r\n", "\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result[\"default\"] = mod;\r\n    return result;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar value_types_1 = require(\"./value-types\");\r\nvar str = __importStar(require(\"./helpers/string\"));\r\nvar obj = __importStar(require(\"./helpers/object\"));\r\nvar InsertOpDenormalizer = (function () {\r\n    function InsertOpDenormalizer() {\r\n    }\r\n    InsertOpDenormalizer.denormalize = function (op) {\r\n        if (!op || typeof op !== 'object') {\r\n            return [];\r\n        }\r\n        if (typeof op.insert === 'object' || op.insert === value_types_1.NewLine) {\r\n            return [op];\r\n        }\r\n        var newlinedArray = str.tokenizeWithNewLines(op.insert + '');\r\n        if (newlinedArray.length === 1) {\r\n            return [op];\r\n        }\r\n        var nlObj = obj.assign({}, op, { insert: value_types_1.NewLine });\r\n        return newlinedArray.map(function (line) {\r\n            if (line === value_types_1.NewLine) {\r\n                return nlObj;\r\n            }\r\n            return obj.assign({}, op, {\r\n                insert: line,\r\n            });\r\n        });\r\n    };\r\n    return InsertOpDenormalizer;\r\n}());\r\nexports.InsertOpDenormalizer = InsertOpDenormalizer;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nfunction tokenizeWithNewLines(str) {\r\n    var NewLine = '\\n';\r\n    if (str === NewLine) {\r\n        return [str];\r\n    }\r\n    var lines = str.split(NewLine);\r\n    if (lines.length === 1) {\r\n        return lines;\r\n    }\r\n    var lastIndex = lines.length - 1;\r\n    return lines.reduce(function (pv, line, ind) {\r\n        if (ind !== lastIndex) {\r\n            if (line !== '') {\r\n                pv = pv.concat(line, NewLine);\r\n            }\r\n            else {\r\n                pv.push(NewLine);\r\n            }\r\n        }\r\n        else if (line !== '') {\r\n            pv.push(line);\r\n        }\r\n        return pv;\r\n    }, []);\r\n}\r\nexports.tokenizeWithNewLines = tokenizeWithNewLines;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nfunction assign(target) {\r\n    var sources = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        sources[_i - 1] = arguments[_i];\r\n    }\r\n    if (target == null) {\r\n        throw new TypeError('Cannot convert undefined or null to object');\r\n    }\r\n    var to = Object(target);\r\n    for (var index = 0; index < sources.length; index++) {\r\n        var nextSource = sources[index];\r\n        if (nextSource != null) {\r\n            for (var nextKey in nextSource) {\r\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\r\n                    to[nextKey] = nextSource[nextKey];\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return to;\r\n}\r\nexports.assign = assign;\r\n", "\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result[\"default\"] = mod;\r\n    return result;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar funcs_html_1 = require(\"./funcs-html\");\r\nvar value_types_1 = require(\"./value-types\");\r\nvar obj = __importStar(require(\"./helpers/object\"));\r\nvar arr = __importStar(require(\"./helpers/array\"));\r\nvar OpAttributeSanitizer_1 = require(\"./OpAttributeSanitizer\");\r\nvar DEFAULT_INLINE_FONTS = {\r\n    serif: 'font-family: Georgia, Times New Roman, serif',\r\n    monospace: 'font-family: Monaco, Courier New, monospace',\r\n};\r\nexports.DEFAULT_INLINE_STYLES = {\r\n    font: function (value) { return DEFAULT_INLINE_FONTS[value] || 'font-family:' + value; },\r\n    size: {\r\n        small: 'font-size: 0.75em',\r\n        large: 'font-size: 1.5em',\r\n        huge: 'font-size: 2.5em',\r\n    },\r\n    indent: function (value, op) {\r\n        var indentSize = parseInt(value, 10) * 3;\r\n        var side = op.attributes['direction'] === 'rtl' ? 'right' : 'left';\r\n        return 'padding-' + side + ':' + indentSize + 'em';\r\n    },\r\n    direction: function (value, op) {\r\n        if (value === 'rtl') {\r\n            return ('direction:rtl' + (op.attributes['align'] ? '' : '; text-align:inherit'));\r\n        }\r\n        else {\r\n            return undefined;\r\n        }\r\n    },\r\n};\r\nvar OpToHtmlConverter = (function () {\r\n    function OpToHtmlConverter(op, options) {\r\n        this.op = op;\r\n        this.options = obj.assign({}, {\r\n            classPrefix: 'ql',\r\n            inlineStyles: undefined,\r\n            encodeHtml: true,\r\n            listItemTag: 'li',\r\n            paragraphTag: 'p',\r\n        }, options);\r\n    }\r\n    OpToHtmlConverter.prototype.prefixClass = function (className) {\r\n        if (!this.options.classPrefix) {\r\n            return className + '';\r\n        }\r\n        return this.options.classPrefix + '-' + className;\r\n    };\r\n    OpToHtmlConverter.prototype.getHtml = function () {\r\n        var parts = this.getHtmlParts();\r\n        return parts.openingTag + parts.content + parts.closingTag;\r\n    };\r\n    OpToHtmlConverter.prototype.getHtmlParts = function () {\r\n        var _this = this;\r\n        if (this.op.isJustNewline() && !this.op.isContainerBlock()) {\r\n            return { openingTag: '', closingTag: '', content: value_types_1.NewLine };\r\n        }\r\n        var tags = this.getTags(), attrs = this.getTagAttributes();\r\n        if (!tags.length && attrs.length) {\r\n            tags.push('span');\r\n        }\r\n        var beginTags = [], endTags = [];\r\n        var imgTag = 'img';\r\n        var isImageLink = function (tag) {\r\n            return tag === imgTag && !!_this.op.attributes.link;\r\n        };\r\n        for (var _i = 0, tags_1 = tags; _i < tags_1.length; _i++) {\r\n            var tag = tags_1[_i];\r\n            if (isImageLink(tag)) {\r\n                beginTags.push(funcs_html_1.makeStartTag('a', this.getLinkAttrs()));\r\n            }\r\n            beginTags.push(funcs_html_1.makeStartTag(tag, attrs));\r\n            endTags.push(tag === 'img' ? '' : funcs_html_1.makeEndTag(tag));\r\n            if (isImageLink(tag)) {\r\n                endTags.push(funcs_html_1.makeEndTag('a'));\r\n            }\r\n            attrs = [];\r\n        }\r\n        endTags.reverse();\r\n        return {\r\n            openingTag: beginTags.join(''),\r\n            content: this.getContent(),\r\n            closingTag: endTags.join(''),\r\n        };\r\n    };\r\n    OpToHtmlConverter.prototype.getContent = function () {\r\n        if (this.op.isContainerBlock()) {\r\n            return '';\r\n        }\r\n        if (this.op.isMentions()) {\r\n            return this.op.insert.value;\r\n        }\r\n        var content = this.op.isFormula() || this.op.isText() ? this.op.insert.value : '';\r\n        return (this.options.encodeHtml && funcs_html_1.encodeHtml(content)) || content;\r\n    };\r\n    OpToHtmlConverter.prototype.getCssClasses = function () {\r\n        var attrs = this.op.attributes;\r\n        if (this.options.inlineStyles) {\r\n            return [];\r\n        }\r\n        var propsArr = ['indent', 'align', 'direction', 'font', 'size'];\r\n        if (this.options.allowBackgroundClasses) {\r\n            propsArr.push('background');\r\n        }\r\n        return (this.getCustomCssClasses() || []).concat(propsArr\r\n            .filter(function (prop) { return !!attrs[prop]; })\r\n            .filter(function (prop) {\r\n            return prop === 'background'\r\n                ? OpAttributeSanitizer_1.OpAttributeSanitizer.IsValidColorLiteral(attrs[prop])\r\n                : true;\r\n        })\r\n            .map(function (prop) { return prop + '-' + attrs[prop]; })\r\n            .concat(this.op.isFormula() ? 'formula' : [])\r\n            .concat(this.op.isVideo() ? 'video' : [])\r\n            .concat(this.op.isImage() ? 'image' : [])\r\n            .map(this.prefixClass.bind(this)));\r\n    };\r\n    OpToHtmlConverter.prototype.getCssStyles = function () {\r\n        var _this = this;\r\n        var attrs = this.op.attributes;\r\n        var propsArr = [['color']];\r\n        if (!!this.options.inlineStyles || !this.options.allowBackgroundClasses) {\r\n            propsArr.push(['background', 'background-color']);\r\n        }\r\n        if (this.options.inlineStyles) {\r\n            propsArr = propsArr.concat([\r\n                ['indent'],\r\n                ['align', 'text-align'],\r\n                ['direction'],\r\n                ['font', 'font-family'],\r\n                ['size'],\r\n            ]);\r\n        }\r\n        return (this.getCustomCssStyles() || [])\r\n            .concat(propsArr\r\n            .filter(function (item) { return !!attrs[item[0]]; })\r\n            .map(function (item) {\r\n            var attribute = item[0];\r\n            var attrValue = attrs[attribute];\r\n            var attributeConverter = (_this.options.inlineStyles &&\r\n                _this.options.inlineStyles[attribute]) ||\r\n                exports.DEFAULT_INLINE_STYLES[attribute];\r\n            if (typeof attributeConverter === 'object') {\r\n                return attributeConverter[attrValue];\r\n            }\r\n            else if (typeof attributeConverter === 'function') {\r\n                var converterFn = attributeConverter;\r\n                return converterFn(attrValue, _this.op);\r\n            }\r\n            else {\r\n                return arr.preferSecond(item) + ':' + attrValue;\r\n            }\r\n        }))\r\n            .filter(function (item) { return item !== undefined; });\r\n    };\r\n    OpToHtmlConverter.prototype.getTagAttributes = function () {\r\n        if (this.op.attributes.code && !this.op.isLink()) {\r\n            return [];\r\n        }\r\n        var makeAttr = this.makeAttr.bind(this);\r\n        var customTagAttributes = this.getCustomTagAttributes();\r\n        var customAttr = customTagAttributes\r\n            ? Object.keys(this.getCustomTagAttributes()).map(function (k) {\r\n                return makeAttr(k, customTagAttributes[k]);\r\n            })\r\n            : [];\r\n        var classes = this.getCssClasses();\r\n        var tagAttrs = classes.length\r\n            ? customAttr.concat([makeAttr('class', classes.join(' '))])\r\n            : customAttr;\r\n        if (this.op.isImage()) {\r\n            this.op.attributes.width &&\r\n                (tagAttrs = tagAttrs.concat(makeAttr('width', this.op.attributes.width)));\r\n            return tagAttrs.concat(makeAttr('src', this.op.insert.value));\r\n        }\r\n        if (this.op.isACheckList()) {\r\n            return tagAttrs.concat(makeAttr('data-checked', this.op.isCheckedList() ? 'true' : 'false'));\r\n        }\r\n        if (this.op.isFormula()) {\r\n            return tagAttrs;\r\n        }\r\n        if (this.op.isVideo()) {\r\n            return tagAttrs.concat(makeAttr('frameborder', '0'), makeAttr('allowfullscreen', 'true'), makeAttr('src', this.op.insert.value));\r\n        }\r\n        if (this.op.isMentions()) {\r\n            var mention = this.op.attributes.mention;\r\n            if (mention.class) {\r\n                tagAttrs = tagAttrs.concat(makeAttr('class', mention.class));\r\n            }\r\n            if (mention['end-point'] && mention.slug) {\r\n                tagAttrs = tagAttrs.concat(makeAttr('href', mention['end-point'] + '/' + mention.slug));\r\n            }\r\n            else {\r\n                tagAttrs = tagAttrs.concat(makeAttr('href', 'about:blank'));\r\n            }\r\n            if (mention.target) {\r\n                tagAttrs = tagAttrs.concat(makeAttr('target', mention.target));\r\n            }\r\n            return tagAttrs;\r\n        }\r\n        var styles = this.getCssStyles();\r\n        if (styles.length) {\r\n            tagAttrs.push(makeAttr('style', styles.join(';')));\r\n        }\r\n        if (this.op.isCodeBlock() &&\r\n            typeof this.op.attributes['code-block'] === 'string') {\r\n            return tagAttrs.concat(makeAttr('data-language', this.op.attributes['code-block']));\r\n        }\r\n        if (this.op.isContainerBlock()) {\r\n            return tagAttrs;\r\n        }\r\n        if (this.op.isLink()) {\r\n            tagAttrs = tagAttrs.concat(this.getLinkAttrs());\r\n        }\r\n        return tagAttrs;\r\n    };\r\n    OpToHtmlConverter.prototype.makeAttr = function (k, v) {\r\n        return { key: k, value: v };\r\n    };\r\n    OpToHtmlConverter.prototype.getLinkAttrs = function () {\r\n        var tagAttrs = [];\r\n        var targetForAll = OpAttributeSanitizer_1.OpAttributeSanitizer.isValidTarget(this.options.linkTarget || '')\r\n            ? this.options.linkTarget\r\n            : undefined;\r\n        var relForAll = OpAttributeSanitizer_1.OpAttributeSanitizer.IsValidRel(this.options.linkRel || '')\r\n            ? this.options.linkRel\r\n            : undefined;\r\n        var target = this.op.attributes.target || targetForAll;\r\n        var rel = this.op.attributes.rel || relForAll;\r\n        return tagAttrs\r\n            .concat(this.makeAttr('href', this.op.attributes.link))\r\n            .concat(target ? this.makeAttr('target', target) : [])\r\n            .concat(rel ? this.makeAttr('rel', rel) : []);\r\n    };\r\n    OpToHtmlConverter.prototype.getCustomTag = function (format) {\r\n        if (this.options.customTag &&\r\n            typeof this.options.customTag === 'function') {\r\n            return this.options.customTag.apply(null, [format, this.op]);\r\n        }\r\n    };\r\n    OpToHtmlConverter.prototype.getCustomTagAttributes = function () {\r\n        if (this.options.customTagAttributes &&\r\n            typeof this.options.customTagAttributes === 'function') {\r\n            return this.options.customTagAttributes.apply(null, [this.op]);\r\n        }\r\n    };\r\n    OpToHtmlConverter.prototype.getCustomCssClasses = function () {\r\n        if (this.options.customCssClasses &&\r\n            typeof this.options.customCssClasses === 'function') {\r\n            var res = this.options.customCssClasses.apply(null, [this.op]);\r\n            if (res) {\r\n                return Array.isArray(res) ? res : [res];\r\n            }\r\n        }\r\n    };\r\n    OpToHtmlConverter.prototype.getCustomCssStyles = function () {\r\n        if (this.options.customCssStyles &&\r\n            typeof this.options.customCssStyles === 'function') {\r\n            var res = this.options.customCssStyles.apply(null, [this.op]);\r\n            if (res) {\r\n                return Array.isArray(res) ? res : [res];\r\n            }\r\n        }\r\n    };\r\n    OpToHtmlConverter.prototype.getTags = function () {\r\n        var _this = this;\r\n        var attrs = this.op.attributes;\r\n        if (!this.op.isText()) {\r\n            return [\r\n                this.op.isVideo() ? 'iframe' : this.op.isImage() ? 'img' : 'span',\r\n            ];\r\n        }\r\n        var positionTag = this.options.paragraphTag || 'p';\r\n        var blocks = [\r\n            ['blockquote'],\r\n            ['code-block', 'pre'],\r\n            ['list', this.options.listItemTag],\r\n            ['header'],\r\n            ['align', positionTag],\r\n            ['direction', positionTag],\r\n            ['indent', positionTag],\r\n        ];\r\n        for (var _i = 0, blocks_1 = blocks; _i < blocks_1.length; _i++) {\r\n            var item = blocks_1[_i];\r\n            var firstItem = item[0];\r\n            if (attrs[firstItem]) {\r\n                var customTag = this.getCustomTag(firstItem);\r\n                return customTag\r\n                    ? [customTag]\r\n                    : firstItem === 'header'\r\n                        ? ['h' + attrs[firstItem]]\r\n                        : [arr.preferSecond(item)];\r\n            }\r\n        }\r\n        if (this.op.isCustomTextBlock()) {\r\n            var customTag = this.getCustomTag('renderAsBlock');\r\n            return customTag ? [customTag] : [positionTag];\r\n        }\r\n        var customTagsMap = Object.keys(attrs).reduce(function (res, it) {\r\n            var customTag = _this.getCustomTag(it);\r\n            if (customTag) {\r\n                res[it] = customTag;\r\n            }\r\n            return res;\r\n        }, {});\r\n        var inlineTags = [\r\n            ['link', 'a'],\r\n            ['mentions', 'a'],\r\n            ['script'],\r\n            ['bold', 'strong'],\r\n            ['italic', 'em'],\r\n            ['strike', 's'],\r\n            ['underline', 'u'],\r\n            ['code'],\r\n        ];\r\n        return inlineTags.filter(function (item) { return !!attrs[item[0]]; }).concat(Object.keys(customTagsMap)\r\n            .filter(function (t) { return !inlineTags.some(function (it) { return it[0] == t; }); })\r\n            .map(function (t) { return [t, customTagsMap[t]]; })).map(function (item) {\r\n            return customTagsMap[item[0]]\r\n                ? customTagsMap[item[0]]\r\n                : item[0] === 'script'\r\n                    ? attrs[item[0]] === value_types_1.ScriptType.Sub\r\n                        ? 'sub'\r\n                        : 'sup'\r\n                    : arr.preferSecond(item);\r\n        });\r\n    };\r\n    return OpToHtmlConverter;\r\n}());\r\nexports.OpToHtmlConverter = OpToHtmlConverter;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar DeltaInsertOp_1 = require(\"./../DeltaInsertOp\");\r\nvar array_1 = require(\"./../helpers/array\");\r\nvar group_types_1 = require(\"./group-types\");\r\nvar Grouper = (function () {\r\n    function Grouper() {\r\n    }\r\n    Grouper.pairOpsWithTheirBlock = function (ops) {\r\n        var result = [];\r\n        var canBeInBlock = function (op) {\r\n            return !(op.isJustNewline() ||\r\n                op.isCustomEmbedBlock() ||\r\n                op.isVideo() ||\r\n                op.isContainerBlock());\r\n        };\r\n        var isInlineData = function (op) { return op.isInline(); };\r\n        var lastInd = ops.length - 1;\r\n        var opsSlice;\r\n        for (var i = lastInd; i >= 0; i--) {\r\n            var op = ops[i];\r\n            if (op.isVideo()) {\r\n                result.push(new group_types_1.VideoItem(op));\r\n            }\r\n            else if (op.isCustomEmbedBlock()) {\r\n                result.push(new group_types_1.BlotBlock(op));\r\n            }\r\n            else if (op.isContainerBlock()) {\r\n                opsSlice = array_1.sliceFromReverseWhile(ops, i - 1, canBeInBlock);\r\n                result.push(new group_types_1.BlockGroup(op, opsSlice.elements));\r\n                i = opsSlice.sliceStartsAt > -1 ? opsSlice.sliceStartsAt : i;\r\n            }\r\n            else {\r\n                opsSlice = array_1.sliceFromReverseWhile(ops, i - 1, isInlineData);\r\n                result.push(new group_types_1.InlineGroup(opsSlice.elements.concat(op)));\r\n                i = opsSlice.sliceStartsAt > -1 ? opsSlice.sliceStartsAt : i;\r\n            }\r\n        }\r\n        result.reverse();\r\n        return result;\r\n    };\r\n    Grouper.groupConsecutiveSameStyleBlocks = function (groups, blocksOf) {\r\n        if (blocksOf === void 0) { blocksOf = {\r\n            header: true,\r\n            codeBlocks: true,\r\n            blockquotes: true,\r\n            customBlocks: true,\r\n        }; }\r\n        return array_1.groupConsecutiveElementsWhile(groups, function (g, gPrev) {\r\n            if (!(g instanceof group_types_1.BlockGroup) || !(gPrev instanceof group_types_1.BlockGroup)) {\r\n                return false;\r\n            }\r\n            return ((blocksOf.codeBlocks &&\r\n                Grouper.areBothCodeblocksWithSameLang(g, gPrev)) ||\r\n                (blocksOf.blockquotes &&\r\n                    Grouper.areBothBlockquotesWithSameAdi(g, gPrev)) ||\r\n                (blocksOf.header &&\r\n                    Grouper.areBothSameHeadersWithSameAdi(g, gPrev)) ||\r\n                (blocksOf.customBlocks &&\r\n                    Grouper.areBothCustomBlockWithSameAttr(g, gPrev)));\r\n        });\r\n    };\r\n    Grouper.reduceConsecutiveSameStyleBlocksToOne = function (groups) {\r\n        var newLineOp = DeltaInsertOp_1.DeltaInsertOp.createNewLineOp();\r\n        return groups.map(function (elm) {\r\n            if (!Array.isArray(elm)) {\r\n                if (elm instanceof group_types_1.BlockGroup && !elm.ops.length) {\r\n                    elm.ops.push(newLineOp);\r\n                }\r\n                return elm;\r\n            }\r\n            var groupsLastInd = elm.length - 1;\r\n            elm[0].ops = array_1.flatten(elm.map(function (g, i) {\r\n                if (!g.ops.length) {\r\n                    return [newLineOp];\r\n                }\r\n                return g.ops.concat(i < groupsLastInd ? [newLineOp] : []);\r\n            }));\r\n            return elm[0];\r\n        });\r\n    };\r\n    Grouper.areBothCodeblocksWithSameLang = function (g1, gOther) {\r\n        return (g1.op.isCodeBlock() &&\r\n            gOther.op.isCodeBlock() &&\r\n            g1.op.hasSameLangAs(gOther.op));\r\n    };\r\n    Grouper.areBothSameHeadersWithSameAdi = function (g1, gOther) {\r\n        return g1.op.isSameHeaderAs(gOther.op) && g1.op.hasSameAdiAs(gOther.op);\r\n    };\r\n    Grouper.areBothBlockquotesWithSameAdi = function (g, gOther) {\r\n        return (g.op.isBlockquote() &&\r\n            gOther.op.isBlockquote() &&\r\n            g.op.hasSameAdiAs(gOther.op));\r\n    };\r\n    Grouper.areBothCustomBlockWithSameAttr = function (g, gOther) {\r\n        return (g.op.isCustomTextBlock() &&\r\n            gOther.op.isCustomTextBlock() &&\r\n            g.op.hasSameAttr(gOther.op));\r\n    };\r\n    return Grouper;\r\n}());\r\nexports.Grouper = Grouper;\r\n", "\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar InlineGroup = (function () {\r\n    function InlineGroup(ops) {\r\n        this.ops = ops;\r\n    }\r\n    return InlineGroup;\r\n}());\r\nexports.InlineGroup = InlineGroup;\r\nvar SingleItem = (function () {\r\n    function SingleItem(op) {\r\n        this.op = op;\r\n    }\r\n    return SingleItem;\r\n}());\r\nvar VideoItem = (function (_super) {\r\n    __extends(VideoItem, _super);\r\n    function VideoItem() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    return VideoItem;\r\n}(SingleItem));\r\nexports.VideoItem = VideoItem;\r\nvar BlotBlock = (function (_super) {\r\n    __extends(BlotBlock, _super);\r\n    function BlotBlock() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    return BlotBlock;\r\n}(SingleItem));\r\nexports.BlotBlock = BlotBlock;\r\nvar BlockGroup = (function () {\r\n    function BlockGroup(op, ops) {\r\n        this.op = op;\r\n        this.ops = ops;\r\n    }\r\n    return BlockGroup;\r\n}());\r\nexports.BlockGroup = BlockGroup;\r\nvar ListGroup = (function () {\r\n    function ListGroup(items) {\r\n        this.items = items;\r\n    }\r\n    return ListGroup;\r\n}());\r\nexports.ListGroup = ListGroup;\r\nvar ListItem = (function () {\r\n    function ListItem(item, innerList) {\r\n        if (innerList === void 0) { innerList = null; }\r\n        this.item = item;\r\n        this.innerList = innerList;\r\n    }\r\n    return ListItem;\r\n}());\r\nexports.ListItem = ListItem;\r\nvar TableGroup = (function () {\r\n    function TableGroup(rows) {\r\n        this.rows = rows;\r\n    }\r\n    return TableGroup;\r\n}());\r\nexports.TableGroup = TableGroup;\r\nvar TableRow = (function () {\r\n    function TableRow(cells) {\r\n        this.cells = cells;\r\n    }\r\n    return TableRow;\r\n}());\r\nexports.TableRow = TableRow;\r\nvar TableCell = (function () {\r\n    function TableCell(item) {\r\n        this.item = item;\r\n    }\r\n    return TableCell;\r\n}());\r\nexports.TableCell = TableCell;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar group_types_1 = require(\"./group-types\");\r\nvar array_1 = require(\"./../helpers/array\");\r\nvar ListNester = (function () {\r\n    function ListNester() {\r\n    }\r\n    ListNester.prototype.nest = function (groups) {\r\n        var _this = this;\r\n        var listBlocked = this.convertListBlocksToListGroups(groups);\r\n        var groupedByListGroups = this.groupConsecutiveListGroups(listBlocked);\r\n        var nested = array_1.flatten(groupedByListGroups.map(function (group) {\r\n            if (!Array.isArray(group)) {\r\n                return group;\r\n            }\r\n            return _this.nestListSection(group);\r\n        }));\r\n        var groupRootLists = array_1.groupConsecutiveElementsWhile(nested, function (curr, prev) {\r\n            if (!(curr instanceof group_types_1.ListGroup && prev instanceof group_types_1.ListGroup)) {\r\n                return false;\r\n            }\r\n            return curr.items[0].item.op.isSameListAs(prev.items[0].item.op);\r\n        });\r\n        return groupRootLists.map(function (v) {\r\n            if (!Array.isArray(v)) {\r\n                return v;\r\n            }\r\n            var litems = v.map(function (g) { return g.items; });\r\n            return new group_types_1.ListGroup(array_1.flatten(litems));\r\n        });\r\n    };\r\n    ListNester.prototype.convertListBlocksToListGroups = function (items) {\r\n        var grouped = array_1.groupConsecutiveElementsWhile(items, function (g, gPrev) {\r\n            return (g instanceof group_types_1.BlockGroup &&\r\n                gPrev instanceof group_types_1.BlockGroup &&\r\n                g.op.isList() &&\r\n                gPrev.op.isList() &&\r\n                g.op.isSameListAs(gPrev.op) &&\r\n                g.op.hasSameIndentationAs(gPrev.op));\r\n        });\r\n        return grouped.map(function (item) {\r\n            if (!Array.isArray(item)) {\r\n                if (item instanceof group_types_1.BlockGroup && item.op.isList()) {\r\n                    return new group_types_1.ListGroup([new group_types_1.ListItem(item)]);\r\n                }\r\n                return item;\r\n            }\r\n            return new group_types_1.ListGroup(item.map(function (g) { return new group_types_1.ListItem(g); }));\r\n        });\r\n    };\r\n    ListNester.prototype.groupConsecutiveListGroups = function (items) {\r\n        return array_1.groupConsecutiveElementsWhile(items, function (curr, prev) {\r\n            return curr instanceof group_types_1.ListGroup && prev instanceof group_types_1.ListGroup;\r\n        });\r\n    };\r\n    ListNester.prototype.nestListSection = function (sectionItems) {\r\n        var _this = this;\r\n        var indentGroups = this.groupByIndent(sectionItems);\r\n        Object.keys(indentGroups)\r\n            .map(Number)\r\n            .sort()\r\n            .reverse()\r\n            .forEach(function (indent) {\r\n            indentGroups[indent].forEach(function (lg) {\r\n                var idx = sectionItems.indexOf(lg);\r\n                if (_this.placeUnderParent(lg, sectionItems.slice(0, idx))) {\r\n                    sectionItems.splice(idx, 1);\r\n                }\r\n            });\r\n        });\r\n        return sectionItems;\r\n    };\r\n    ListNester.prototype.groupByIndent = function (items) {\r\n        return items.reduce(function (pv, cv) {\r\n            var indent = cv.items[0].item.op.attributes.indent;\r\n            if (indent) {\r\n                pv[indent] = pv[indent] || [];\r\n                pv[indent].push(cv);\r\n            }\r\n            return pv;\r\n        }, {});\r\n    };\r\n    ListNester.prototype.placeUnderParent = function (target, items) {\r\n        for (var i = items.length - 1; i >= 0; i--) {\r\n            var elm = items[i];\r\n            if (target.items[0].item.op.hasHigherIndentThan(elm.items[0].item.op)) {\r\n                var parent = elm.items[elm.items.length - 1];\r\n                if (parent.innerList) {\r\n                    parent.innerList.items = parent.innerList.items.concat(target.items);\r\n                }\r\n                else {\r\n                    parent.innerList = target;\r\n                }\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    };\r\n    return ListNester;\r\n}());\r\nexports.ListNester = ListNester;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar group_types_1 = require(\"./group-types\");\r\nvar array_1 = require(\"../helpers/array\");\r\nvar TableGrouper = (function () {\r\n    function TableGrouper() {\r\n    }\r\n    TableGrouper.prototype.group = function (groups) {\r\n        var tableBlocked = this.convertTableBlocksToTableGroups(groups);\r\n        return tableBlocked;\r\n    };\r\n    TableGrouper.prototype.convertTableBlocksToTableGroups = function (items) {\r\n        var _this = this;\r\n        var grouped = array_1.groupConsecutiveElementsWhile(items, function (g, gPrev) {\r\n            return (g instanceof group_types_1.BlockGroup &&\r\n                gPrev instanceof group_types_1.BlockGroup &&\r\n                g.op.isTable() &&\r\n                gPrev.op.isTable());\r\n        });\r\n        return grouped.map(function (item) {\r\n            if (!Array.isArray(item)) {\r\n                if (item instanceof group_types_1.BlockGroup && item.op.isTable()) {\r\n                    return new group_types_1.TableGroup([new group_types_1.TableRow([new group_types_1.TableCell(item)])]);\r\n                }\r\n                return item;\r\n            }\r\n            return new group_types_1.TableGroup(_this.convertTableBlocksToTableRows(item));\r\n        });\r\n    };\r\n    TableGrouper.prototype.convertTableBlocksToTableRows = function (items) {\r\n        var grouped = array_1.groupConsecutiveElementsWhile(items, function (g, gPrev) {\r\n            return (g instanceof group_types_1.BlockGroup &&\r\n                gPrev instanceof group_types_1.BlockGroup &&\r\n                g.op.isTable() &&\r\n                gPrev.op.isTable() &&\r\n                g.op.isSameTableRowAs(gPrev.op));\r\n        });\r\n        return grouped.map(function (item) {\r\n            return new group_types_1.TableRow(Array.isArray(item)\r\n                ? item.map(function (it) { return new group_types_1.TableCell(it); })\r\n                : [new group_types_1.TableCell(item)]);\r\n        });\r\n    };\r\n    return TableGrouper;\r\n}());\r\nexports.TableGrouper = TableGrouper;\r\n"]}