const APP = getApp()
const ROUTER = require("@/services/routerManager")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  data: {
    show_white: false,
    courseList: [],
    page: 1,
    isRequest: true,
    isComplete: false,
    isPageLoadComplete: false, // 页面是否加载完成
    resData: null,
    hideenShow: false,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    this.getCourseList(1)
    APP.setCopyCurrentPageUrl(options)
  },
  onShow() {
    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }
    this.setData({
      page: 1,
    })
    this.getCourseList(1)
  },
  getCourseList(type) {
    return UTIL.request(API.getRecommendList, {
      page: this.data.page,
    }).then((res) => {
      const resData = res.data
      let isRequest = this.data.isRequest
      isRequest = resData?.list?.length > 0 ? true : false
      let arr = res.data.list
      let courseList = []
      courseList = type == 1 ? arr : this.data.courseList.concat(arr)
      this.setData({
        resData,
        courseList,
        isRequest,
        isComplete: true,
        isPageLoadComplete: true,
      })
    })
  },
  // 面授课程
  goDetail(e) {
    console.log(e)
    const data = e.currentTarget.dataset.item
    const cmd = this.parseCmdJson(data.cmd_json)
    APP.cmdManage(cmd)
  },
  parseCmdJson(cmdJsonStr) {
    try {
      // 尝试将字符串转换为对象
      const cmdJson = JSON.parse(cmdJsonStr)
      return cmdJson
    } catch (error) {
      console.error("JSON 解析错误:", error)
      // 如果解析失败，返回 null 或者其他默认值
      return null
    }
  },
  // 触底加载
  onReachBottom() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getCourseList(2)
  },
  closeHidden() {
    this.setData({
      hideenShow: false,
    })
  },
  openHideen() {
    console.log("进来么")
    this.setData({
      hideenShow: true,
    })
  },
  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  // 获取分享参数
  getPageShareParams() {
    return APP.createShareParams({
      title: this.data?.resData?.share_info?.title || "",
      imageUrl: this.data?.resData?.share_info?.image || "",
      path: "/pages/improve/list/index",
      query: {},
    })
  },
  // 分享好友
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  // 分享朋友圈
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
