.remain-time {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 142rpx;
  background: #fff;
  box-sizing: border-box;

  .remain-time-box {
    background-color: rgba(57, 160, 237, 0.1);
    padding: 20rpx 32rpx;
    display: flex;
    align-items: center;
    // border-bottom: 1rpx solid #ebecf0;
    &.error {
      background: rgba(255, 247, 245, 1);
      .text {
        color: rgba(230, 0, 0, 1);
      }
      .right-btn {
        color: rgba(230, 0, 0, 1);
      }
    }
    .text {
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      .text {
        color: #39a0ed;
      }
      // flex: 1;
      // min-width: 0;
    }
    .right-btn {
      font-size: 24rpx;
      color: #39a0ed;
      display: flex;
      align-items: center;
      image {
        width: 24rpx;
        height: 24rpx;
        margin-left: 4rpx;
      }
    }
    .right-arrow {
      width: 24rpx;
      height: 24rpx;
    }
  }
}
.subject-list-remain {
  bottom: auto;
}
