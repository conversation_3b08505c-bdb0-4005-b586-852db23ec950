const ROUTER = require("@/services/routerManager")
const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const BASE_CACHE = require("@/utils/cache/baseCache")
let audioService = null
Page({
  data: {
    show_white: false,
    stickyHeight: null,
    listHeight: null,
    accumulateTab: [],
    activeId: null,
    scrollViewWidth: 0,
    tabWidth: 0,
    currentScrollLeft: 0,
    page: 1,
    isRequest: false,
    id: "", // 当前播放音频的id
    isPlaying: false, // 是否播放
    tagId: "",
    isComplete: false, //是否加载完成
    hideenShow: false,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    const obj = BASE_CACHE.getBaseCache() || {}
    this.pageOptions = options
    this.pageConfig = obj
    await this.getTabList(1)
    this.getHeight()
    audioService = APP.globalData.audioService
    // 注册状态变化监听器
    this.unwatchPlayer = audioService?.watchPlayer((state) => {
      // console.log("播放器状态更新1:", state.playStateMap)
      // 直接从state中获取当前播放项的状态信息
      // 直接从 state 中获取当前播放项的状态信息
      const playStateMap = state?.playStateMap
      let accumulateTabArr = this.data.accumulateTab
      let activeIndex = accumulateTabArr.findIndex(
        (item) => item.id == this.data.activeId
      )

      // 遍历 playStateMap 并更新 dataList 中对应项的 currentTime 和 progress
      let updatedDataList = accumulateTabArr[activeIndex].dataList.map(
        (item) => {
          if (playStateMap[item.id]) {
            return {
              ...item,
              currentTime: playStateMap[item.id].currentTime,
              progress: playStateMap[item.id].progress,
            }
          }
          return item
        }
      )
      accumulateTabArr[activeIndex].dataList = updatedDataList
      // 更新组件数据
      this.setData({
        tagId: state.tagId,
        isPlaying: state.isPlaying,
        id: state.id,
        accumulateTab: accumulateTabArr, // 更新后的 dataList
      })
    })
    APP.setCopyCurrentPageUrl(options)
  },
  onShow() {
    if (this.data.isComplete) {
      const newObj = BASE_CACHE.getBaseCache() || {}
      if (
        newObj.province?.key != this.pageConfig?.province.key ||
        newObj.exam?.key != this.pageConfig.exam.key
      ) {
        this.pageConfig = newObj
        this.getTabList(2)
      }
    }
  },
  closeHidden() {
    this.setData({
      hideenShow: false,
    })
  },
  openHideen() {
    this.setData({
      hideenShow: true,
    })
  },
  getHeight() {
    const query = wx.createSelectorQuery()
    query
      .select(".accumulate-tab") // 替换为你的类名
      .boundingClientRect((rect) => {
        if (rect) {
          let heightText = "top:" + rect.top + "px"
          this.setData({
            stickyHeight: heightText,
          })
          console.log("距离顶部高度: ", rect.top) // 输出距离顶部的高度
        }
      })
      .exec()
  },
  getListHeight() {
    // 如果已经计算过高度，直接返回（避免重复计算）
    if (this.data.listHeight) {
      return
    }
    setTimeout(() => {
      const query = wx.createSelectorQuery()

      query.select(".main-content").boundingClientRect()
      query.select(".home-tabbar").boundingClientRect()

      query.exec((res) => {
        const mainContentRect = res[0]
        const homeTabbarRect = res[1]

        if (!mainContentRect || !homeTabbarRect) {
          console.error("无法获取元素的高度，请检查类名是否正确")
          return
        }

        const top = mainContentRect.top + homeTabbarRect.height
        const heightText = `height: calc(100vh - ${top}px)`

        this.setData({
          listHeight: heightText,
        })
      })
    }, 500) // 根据实际情况调整延迟时间
  },
  onScroll(e) {
    console.log(e.detail.scrollTop)
    let accumulateTabArr = this.data.accumulateTab
    let activeIndex = accumulateTabArr.findIndex(
      (item) => item.id == this.data.activeId
    )
    if (e.detail.scrollTop > 80) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!accumulateTabArr[activeIndex].show_white) {
        accumulateTabArr[activeIndex].show_white = true
        this.setData({
          accumulateTab: accumulateTabArr,
        })
      }
    } else {
      if (accumulateTabArr[activeIndex].show_white) {
        accumulateTabArr[activeIndex].show_white = false
        this.setData({
          accumulateTab: accumulateTabArr,
        })
      }
    }
  },
  onScrolltoupper(e) {
    console.log("触发到顶部", e)
    let accumulateTabArr = this.data.accumulateTab
    let activeIndex = accumulateTabArr.findIndex(
      (item) => item.id == this.data.activeId
    )
    if (accumulateTabArr[activeIndex].show_white) {
      accumulateTabArr[activeIndex].show_white = false
      this.setData({
        accumulateTab: accumulateTabArr,
      })
    }
  },

  goDetail(e) {
    const { id } = e.currentTarget.dataset
    ROUTER.navigateTo({
      path: "/pages/accumulate/detail/index",
      query: {
        id,
      },
    })
  },
  scrollClick(e) {
    this.setData({
      intoIndex: "item" + e.currentTarget.dataset.id,

      indexId: e.currentTarget.dataset.id,
    })
  },
  // 获取顶部tab
  async getTabList(type) {
    return UTIL.request(API.getCcumulationListTag).then((res) => {
      if (res?.error?.code === 0) {
        let arr = res.data.list || []
        if (arr.length) {
          arr.forEach((item) => {
            item.icon = false
            item.dataList = []
            item.page = 1
            item.isRequest = true
            item.show_white = false
          })
        }
        this.setData({
          accumulateTab: arr,
          activeId:
            this.pageOptions.tag_id && type == 1
              ? this.pageOptions.tag_id
              : arr[0].id,
        })
        wx.setStorageSync("accumulateActiveId", this.data.activeId)
      }
      this.getList(1)
    })
  },
  // 获取列表
  async getList(type) {
    let accumulateTabArr = this.data.accumulateTab
    let activeIndex = accumulateTabArr.findIndex(
      (item) => item.id == this.data.activeId
    )
    let activeData = accumulateTabArr[activeIndex]
    const param = {
      tag_id: this.data.activeId,
      only_video: 0,
      page: activeData.page,
    }
    const res = await UTIL.request(API.getCcumulationList, param)
    if (res?.error?.code === 0) {
      let isRequest = activeData.isRequest
      isRequest = res.data.list?.length > 0 ? true : false
      let arr = []
      let newArr = res.data.list || []
      // // 对 newArr 中的每一项进行处理
      newArr = newArr.map((item) => {
        if (item.progress_record && item.progress_record.time !== undefined) {
          item.progress = Math.round(
            (item.progress_record.time / Number(item.video_time)) * 100
          )
          item.currentTime = item.progress_record.time
        }
        return item
      })
      arr = type == 1 ? newArr : activeData.dataList.concat(newArr)
      accumulateTabArr[activeIndex].dataList = arr
      accumulateTabArr[activeIndex].isRequest = isRequest
      this.setData({
        accumulateTab: accumulateTabArr,
        isComplete: true,
      })
      this.getListHeight()
    }
  },
  changeIndex(e) {
    const { id } = e.currentTarget.dataset
    const oldId = this.data.activeId
    if (oldId === id) {
      return
    }
    this.setData({
      activeId: id,
    })
    wx.setStorageSync("accumulateActiveId", this.data.activeId)
    this.getList(1)
  },
  changePlay(e) {
    const item = e.currentTarget.dataset.item
    // const isPlaying = audioService.getState().isPlaying
    const isPlaying = this.data.isPlaying
    if (this.data.id == item.id && isPlaying) {
      this.setData({
        isPlaying: false,
      })
      audioService.pause()
    } else {
      this.setData({
        defaulId: item.id,
        isPlaying: true,
        id: item.id,
      })
      audioService.playItemById(item.id, this.data.activeId)
    }
  },
  getPageShareParams() {
    let query = this.pageOptions
    let share_info = this.data.accumulateTab.find(
      (item) => item.id == this.data.activeId
    ).share_info
    query.tag_id = this.data.activeId
    return APP.createShareParams({
      title: share_info.title || "",
      imageUrl: share_info.image || "",
      path: "/pages/accumulate/list/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
