const { parseQuestionHtml } = require("@/utils/QuestionParseHtml")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    strText: String,

    customClass: false,
  },

  lifetimes: {
    attached() {
      if (this.data.strText) {
        let imgSrcArray = []
        let index = 0

        // 正则表达式匹配所有 <img> 标签
        const resultStr = parseQuestionHtml(this.data.strText).replace(
          /<img\s+[^>]*class=['"]ql-image['"][^>]*>/gi,
          (match) => {
            // 提取 src 属性
            const srcMatch = match.match(/src=(["'])(.*?)\1/)

            if (srcMatch && srcMatch[2]) {
              const src = decodeURIComponent(srcMatch[2])
              // 将解码后的 src 添加到数组中
              imgSrcArray[index] = src

              // 在 img 标签中添加 catchtap 和 data-index 属性
              const modifiedImgTag = match.replace(
                ">",
                ` catchtap="handleTap" data-index="${index}" >`
              )

              index++
              return modifiedImgTag
            }
            return match
          }
        )

        this.setData({
          imageMap: imgSrcArray,
          textContent: resultStr,
        })
      }
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    textContent: null,
    imageMap: null,
    previewImageUrl: "",
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleTap(event) {
      // 检查点击的是否是 .ql-image 类的 img 标签
      const { map } = event.currentTarget.dataset
      const current = map[0]
      if (current) {
        this.previewImage(current)
      }
    },
    previewImage(current) {
      // const urls = [current]
      // this.setData({
      //   previewImageUrl: current,
      // })
      // console.log(current)
      // this.selectComponent("#preview").openModal()
      // wx.previewImage({
      //   current,
      //   urls,
      // })
      console.log("dddddd")
      this.triggerEvent("onImagePreview", { url: current })
    },
  },
})
