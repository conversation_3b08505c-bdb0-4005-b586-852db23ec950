const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    listHeight: null,
    dataList: [],
    listKey: "",
    examTabs: {},
    subjectTabs: {},
    isComplete: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    await this.getExamListRequest()
  },

  getListHeight() {
    const query = wx.createSelectorQuery()
    query
      .select(".main-content") // 替换为你的类名
      .boundingClientRect((rect) => {
        if (rect) {
          let heightText = "height: calc(100vh - " + rect.top + "px)"
          this.setData({
            listHeight: heightText,
          })
          console.log("距离顶部高度: ", rect.top) // 输出距离顶部的高度
        }
      })
      .exec()
  },

  // 获取顶部tab
  async getExamListRequest() {
    const res = await UTIL.request(API.getCollectTab)
    let arr = res.data.list || []
    if (arr.length) {
      arr.forEach((item) => {
        item.exam_key = item.no
        item.exam_name = item.name
        if (item.children && item.children.length) {
          item.children.forEach((citem) => {
            citem.subject_key = citem.no
            citem.subject_name = citem.name
          })
        }
      })
      this.setData({
        ["examTabs.list"]: arr,
        ["examTabs.active"]: arr[0].exam_key,
        ["subjectTabs.list"]:
          arr[0].children && arr[0].children.length ? arr[0].children : [],
        ["subjectTabs.active"]:
          arr[0].children && arr[0].children.length
            ? arr[0].children[0].subject_key
            : "",
      })
      this.getList(1)
    } else {
      this.setData({
        isComplete: true,
      })
    }
  },

  handleExamTabsItemEvent(e) {
    const data = e.detail.data
    this.setData({
      ["examTabs.active"]: data.exam_key,
      ["subjectTabs.list"]: data.children || [],
      ["subjectTabs.active"]:
        data.children && data.children.length
          ? data.children[0].subject_key
          : "",
    })
    let param = this.getParam()
    const listKey = `${param.type}_${param.no}`
    let list = []
    if (this.data.dataList.length) {
      list = this.data.dataList.find((item) => item.key == listKey)?.value || []
      if (list.length) {
        this.setData({
          listKey,
        })
        return
      }
      this.getList(1)
    } else {
      this.getList(1)
    }
  },
  handleSubjectTabsItemEvent(e) {
    const data = e.detail.data
    this.setData({
      ["subjectTabs.active"]: data.subject_key,
    })
    let param = this.getParam()
    const listKey = `${param.type}_${param.no}`
    let list = []
    if (this.data.dataList.length) {
      list = this.data.dataList.find((item) => item.key == listKey)?.value || []
      if (list.length) {
        this.setData({
          listKey,
        })
        return
      }
      this.getList(1)
    } else {
      this.getList(1)
    }
  },

  async getList(type) {
    let param = this.getParam()
    // 获取当前键
    const key = `${param.type}_${param.no}`
    let dataList = [...this.data.dataList] // 深拷贝当前数组
    let activeIndex = dataList.findIndex((item) => item.key == key)
    param.page = dataList[activeIndex]?.page || 1
    param.limit = dataList[activeIndex]?.limit || 20

    const res = await UTIL.request(API.getCollectList, param)
    let isRequest = dataList[activeIndex]?.isRequest || true
    isRequest = res.data.list?.length < param.limit ? false : true

    let newArr = res.data.list || []

    // 查找是否已存在对应的键
    const index = dataList.findIndex((item) => item.key === key)

    if (index !== -1) {
      // 如果键已存在，按逻辑替换或追加数据
      dataList[index].value =
        type === 1 ? newArr : dataList[index].value.concat(newArr)
      dataList[index].isRequest = isRequest
    } else {
      // 如果键不存在，新增键值对
      dataList.push({
        key,
        value: newArr,
        page: 1,
        limit: 20,
        isRequest,
      })
    }

    this.setData({
      dataList, // 更新 dataList
      isComplete: true,
      isRequest,
      listKey: key,
    })

    this.getListHeight()
  },
  getParam() {
    let obj = {
      type: "",
      no: "",
    }
    if (this.data.subjectTabs.active) {
      let data = this.data.subjectTabs.list.find(
        (item) => item.subject_key == this.data.subjectTabs.active
      )
      obj.type = data.type
      obj.no = data.no
    } else {
      let data = this.data.examTabs.list.find(
        (item) => item.exam_key == this.data.examTabs.active
      )
      obj.type = data.type
      obj.no = data.no
    }
    return obj
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (this.data.examTabs?.active && this.data.isComplete) {
      let arr = this.data.dataList
      let index = arr.findIndex((item) => item.key == this.data.listKey)
      arr[index].page = 1
      this.setData({
        //改变页码
        dataList: arr,
      })
      this.getList(1)
    }
  },

  goDetail(e) {
    const { data } = e.currentTarget.dataset
    let url = ""
    let query = {}
    if (this.data.examTabs.active === "single_all") {
      query.question_id = data.id
      query.type = "question"
      url = "/pages/question/single-detail/index"
    } else if (this.data.examTabs.active === "multiple") {
      query.id = data.id
      url = "/pages/situation/sheetDetail/index"
    } else {
      query.id = data.id
      query.tag_id = this.data.subjectTabs.active
      url = "/pages/accumulate/detail/index"
    }
    ROUTER.navigateTo({
      path: url,
      query,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onScrollToLower() {
    let arr = this.data.dataList
    let index = arr.findIndex((item) => item.key == this.data.listKey)
    if (!arr[index].isRequest) {
      return
    }
    arr[index].page = arr[index].page + 1
    this.setData({
      //改变页码
      dataList: arr,
    })
    this.getList(2)
  },
})
