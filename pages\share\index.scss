/* pages/share/index.wxss */
page {
  background: rgba(230, 0, 0, 1);
}

.share-btn {
  padding: 0 !important;
  border: none;
  position: absolute;
  bottom: 64rpx;
  left: 50%;
  transform: translateX(-50%);
}

.bg-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
}

button {
  background-color: transparent !important;

  border: none !important;
  width: 670rpx;
  padding: 0 !important;
}

button::after {
  border: none;
}

.share-img {
  width: 100%;
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  .img-wrap {
    width: 600rpx;
  }
  .close-yuan {
    width: 56rpx;
    height: 56rpx;
    margin-top: 60rpx;
  }
}

.main-content {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  .main-content-top {
    image {
      width: 100%;
      border-radius: 24rpx 24rpx 0 0;
    }
  }

  .content-box {
    padding: 80rpx 64rpx 48rpx 64rpx;
    position: relative;
    .top-bg {
      background: linear-gradient(180deg, #ffc7c7 0%, #ffffff 100%);
      width: 100%;
      height: 160rpx;
      position: absolute;
      left: 0;
      top: 0;
      border-radius: 24rpx 24rpx 0 0;
    }
    .title {
      font-size: 48rpx;
      color: rgba(60, 61, 66, 1);
      font-weight: bold;
      text-align: center;
      text {
        color: rgba(230, 0, 0, 1);
      }
    }
    .label-text {
      font-size: 24rpx;
      color: rgba(102, 102, 102, 1);
      text-align: center;
      margin-top: 16rpx;
      text {
        color: rgba(230, 0, 0, 1);
      }
    }

    .btn-box {
      background-color: rgba(230, 0, 0, 1);
      color: #fff;
      border-radius: 56rpx;
      font-size: 30rpx;
      height: 88rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 88rpx;
      text {
        color: rgba(255, 255, 0, 1);
      }
    }
    .clsoe-text {
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 32rpx;
      }
      font-size: 24rpx;
      color: rgba(102, 102, 102, 1);
      text-align: center;
      margin-top: 32rpx;
      line-height: 1em;
    }
  }
}
