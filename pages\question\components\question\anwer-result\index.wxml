<wxs module="utils">
  // formatTime.wxs
  function formatSeconds(seconds) {
    var secondsNumber = parseInt(seconds, 10);
    if (isNaN(secondsNumber)) {
      return '00:00';
    }
    secondsNumber = Math.max(0, secondsNumber); // 确保非负
    var minutes = Math.floor(secondsNumber / 60);
    var secondsRemaining = secondsNumber % 60;
    // 补零处理
    var minutesStr = minutes < 10 ? '0' + minutes : '' + minutes;
    var secondsStr = secondsRemaining < 10 ? '0' + secondsRemaining : '' + secondsRemaining;
    return minutesStr + ':' + secondsStr;
  }

  module.exports = {
    formatSeconds: formatSeconds
  };
</wxs>
<!-- 意外提交 -->
<view class="header-tips" wx:if="{{pageState.isUnexpectedSubmit}}">
  <text wx:if="{{pageState.isOpenCamera}}">因系统原因视频已停止录制，可重答或提交</text>
  <text wx:else>因系统原因音频已停止录制，可重答或提交</text>
</view>
<!-- <view class="header-tips">作答超时，已自动结束录制</view> -->
<view class="page-content">
  <view class="page-title">我的作答</view>
  <view class="media-content">
    <view class="video-content" wx:if="{{mediaInfo.video.tempPath}}">
      <video class="video" id="resultVideo" src="{{mediaInfo.video.tempPath}}" muted="{{!hasPlay}}" object-fit="cover" autoplay="{{false}}" bindtimeupdate="onBackgroundVideoTimeupdate" object-fit="fill"></video>
    </view>
    <view class="audio-content" wx:elif="{{mediaInfo.audio.tempPath}}">
      <my-audio audioUrl="{{mediaInfo.audio.tempPath}}" id="audio-answer"></my-audio>
    </view>
  </view>
  <view class="time-content">
    <view class="time-item">
      <view class="name">思考时长</view>
      <view class="text">{{utils.formatSeconds(pageState.useTime-mediaInfo.audio.time)}}</view>
    </view>
    <view class="time-item">
      <view class="name">作答时长</view>
      <view class="text">{{utils.formatSeconds(mediaInfo.audio.time)}}</view>
    </view>
    <view class="time-item">
      <view class="name">总时长</view>
      <view class="text">{{utils.formatSeconds(pageState.useTime)}}</view>
    </view>
  </view>
</view>
<view class="bottom-actionbar">
  <view class="submit-button" catch:tap="submit">提交作答</view>
  <view class="replay-button" catch:tap="replay">
    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_result_replay.png" mode="widthFix" class="icon" />
    重新作答
  </view>
</view>