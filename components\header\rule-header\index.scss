.banner-bg {
  width: 100%;
  position: absolute;
  z-index: -1;
  display: block;
}

.right-boxs {
  padding-right: 90px;
  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 24rpx;
  }
}

.back-img {
  width: 40rpx;
  height: 40rpx;
}

.top-header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64rpx;
  margin-bottom: 12rpx;

  .exam-name {
    font-size: 30rpx;
    color: #18191a;

    .icon {
      display: inline-block;
      width: 0;
      height: 0;
      border-right: 8rpx solid transparent;
      border-left: 8rpx solid transparent;
      border-top: 10rpx solid #93969b;
      border-bottom: none;
      transform: translateY(-4rpx);
    }
  }
}

.title {
  font-size: 36rpx;
  color: rgba(34, 36, 46, 1);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
}

.status-box {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: transparent;
  padding-left: 32rpx;
  &.bgf {
    background-color: #fff;
  }
}
