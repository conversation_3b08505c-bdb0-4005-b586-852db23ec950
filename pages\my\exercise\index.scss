page {
  background: #f2f4f7;
}
.pd-style {
  padding: 0 80rpx;
}
.tab-list {
  background: #fff;
  padding-bottom: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  left: 0;
  box-sizing: border-box;
  &-item {
    flex: 1;
    font-size: 30rpx;
    padding: 32rpx 0;
    color: #666666;
    font-weight: 400;
    text-align: center;
    &.active {
      font-weight: bold;
      font-size: 36rpx;
      color: #22242e;
      .text {
        position: relative;
        &::after {
          content: "";
          position: absolute;
          bottom: 2rpx;
          left: -6rpx;
          width: 80rpx;
          height: 12rpx;
          background: linear-gradient(
            131deg,
            rgba(230, 0, 0, 0.3) 0%,
            rgba(230, 0, 0, 0) 100%
          );
          border-radius: 8rpx;
        }
      }
    }
  }
}
.exercise-list {
  box-sizing: border-box;
  padding: 32rpx 32rpx 64rpx 32rpx;
  &-item {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 16rpx 2rpx rgba(76, 96, 133, 0.05);
    border-radius: 16rpx;
    box-sizing: border-box;
    padding: 32rpx;
    margin-bottom: 24rpx;
    .top-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;
      .top-text {
        font-size: 28rpx;
        color: #919499;
        font-weight: 400;
      }
      .red-text {
        color: #e60000;
      }
      .right {
        display: flex;
        align-items: center;
        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
          margin-left: 8rpx;
        }
      }
    }
    .content-area {
      font-size: 30rpx;
      font-weight: 400;
      color: #3c3d42;
      line-height: 48rpx;
    }
  }
}
.no-data-box {
  overflow: hidden;
}
.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}
.no-data-btn {
  margin: 48rpx auto;
  width: 212rpx;
  height: 72rpx;
  background: #e60000;
  border-radius: 44rpx;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
