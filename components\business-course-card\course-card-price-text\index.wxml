<wxs src="/utils/wxs/utils.wxs" module="utils" />
<view class="price-text   {{isInHome?'price-home-text':''}}">

  <!-- 首页展示 -->
  <block wx:if="{{isInHome}}">
    <!-- 已购买 -->
    <block wx:if="{{isQualification}}">
      <view class="start-study buy-text" wx:if="{{usability=='free'}}">已添加</view>
      <view class="start-study buy-text" wx:else>已购买</view>

    </block>

    <!-- 是否参与拼团 -->
    <block wx:elif="{{isJoinGroupon}}">
      <view class="start-study">参与拼团中</view>
    </block>

    <!-- 未购买 -->
    <block wx:else>
      <!-- 免费课程 -->
      <view class="money" wx:if="{{usability=='free'}}">
        <view class="free-text">免费</view>
      </view>

      <!-- 商品 -->
      <view class="money text-left" wx:else>
        <block wx:if="{{isHasGroupon}}">
          <view class="groupon-label">拼</view>
          <text class="symbol">￥</text>
          <text class="num">{{ utils.clearPriceZero(price)}}</text>
        </block>
        <block wx:else>
          <text class="symbol">{{couponPrice?'券后':''}}￥</text>
          <text class="num">{{ couponPrice?couponPrice:utils.clearPriceZero(price)}}</text>
        </block>
        <text class="old-num" wx:if="{{ utils.clearPriceZero(oldPrice)>0&&!isHasGroupon}}">￥{{utils.clearPriceZero(oldPrice)}}</text>
        <!-- <view class="groupon-label" wx:if="{{isHasGroupon}}">拼团</view> -->
      </view>

      <view class="study-num text-left" wx:if="{{desc&&!isHideDescText}}">{{desc}}人已学</view>
    </block>
  </block>


  <!-- 列表展示 -->
  <block wx:else>
    <!-- 已购买 -->
    <block wx:if="{{isQualification}}">
      <view class="start-study">开始学习</view>
    </block>

    <!-- 是否参与拼团 -->
    <block wx:elif="{{isJoinGroupon}}">
      <view class="start-study">参与拼团中</view>
    </block>
    <!-- 未购买 -->
    <block wx:else>
      <!-- 免费课程 -->
      <view class="money" wx:if="{{usability=='free'}}">
        <view class="free-text">免费</view>
      </view>
      <!-- 商品 -->
      <view class="money" wx:else>
        <text class="old-num" wx:if="{{ utils.clearPriceZero(oldPrice)>0&&!isHasGroupon}}">￥{{ utils.clearPriceZero(oldPrice)}}</text>
        <block wx:if="{{isHasGroupon}}">
          <view class="groupon-label">拼团</view>
          <text class="symbol">￥</text><text class="num">{{utils.clearPriceZero(price)}}</text>
        </block>
        <block wx:else>
          <text class="symbol">{{couponPrice?'券后':''}}￥</text><text class="num">{{couponPrice?couponPrice:utils.clearPriceZero(price)}}</text>
        </block>


      </view>
      <view class="study-num" wx:if="{{desc&&!isHideDescText}}">{{desc}}人已学</view>
    </block>
  </block>
</view>