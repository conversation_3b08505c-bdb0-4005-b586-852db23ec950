const APP = getApp()
const ROUTER = require("@/services/routerManager")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const BASE_CACHE = require("@/utils/cache/baseCache")
Page({
  data: {
    showPrivacy: false, //是否显示隐私协议,
  },
  //事件处理函数
  onLoad: async function (options) {
    console.log(options, "进来")
    let code = null
    if (options.scene) {
      let query = this.getQueryParams(options.scene)
      if (query.zcode) {
        code = query.zcode
      }
    }

    if (code) {
      const res = await UTIL.request(API.getIndexCodePath, { code })
      if (res?.data?.jump_path) {
        try {
          const dataQuery = this.getQueryParams(res.data.query_params) || {}
          if (dataQuery.campus_id) {
            dataQuery.campus_id = Number(dataQuery.campus_id)
          }
          if (options.scene) {
            dataQuery.scene = options.scene
          }
          APP.globalData.launchOptions.query = dataQuery
        } catch (error) {}

        try {
          await APP.checkLoadRequest()

          APP.initCommonConfiguration(
            APP.globalData.serverConfig.exam_province_list,
            APP.globalData.launchOptions.query
          )
        } catch (error) {
          console.log(error)
        }

        wx.reLaunch({
          url: res.data.jump_path + "?" + res.data.query_params,
          fail: (res) => {
            console.log("入口跳转失败", res)
            this.toNext()
          },
        })
        return false
      }
    }
    this.onPrivacyConfirm()
  },
  // 进入下个页面
  toNext() {
    const settingCache = BASE_CACHE.getBaseCache() || {}
    if (!settingCache.province?.key || !settingCache.exam?.key) {
      ROUTER.redirectTo({
        path: "/pages/entry/list/index",
        query: { isEntryFirst: true },
      })
      return
    }
    ROUTER.reLaunch({
      path: "/pages/practice/home/<USER>",
    })
  },
  getQueryParams(encodedParams) {
    // 解码字符串
    const decodedParams = decodeURIComponent(encodedParams)

    // 解析参数
    const params = {}
    decodedParams.split("&").forEach((pair) => {
      const [key, value] = pair.split("=")
      params[key] = value
    })

    return params
  },
  // 监听隐私协议弹窗确认
  onPrivacyConfirm() {
    this.toNext()
  },
})
