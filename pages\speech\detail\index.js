const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
let PAGE_OPTIONS = {}
Page({
  data: {
    isLogin: false, // 用户是否登录
    id: "",
    speechData: null,
    weichatShow: false, // 微信客服弹窗
    weichatCustomerService: false, // 微信客服信息
  },
  async onLoad(options) {
    PAGE_OPTIONS = options
    if (options.id) {
      this.setData({
        id: options.id,
      })
    }
    await APP.checkLoadRequest()
    this.getDrillDetail()
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
  },
  onShow() {
    if (this.data.speechData) {
      this.getDrillDetail()
    }
  },
  gokefu() {},
  // 处理关闭校区微信客服
  closeWeichatCustomerService() {
    this.setData({
      weichatShow: false,
    })
  },
  // 处理打开校区微信客服
  openWeichatCustomerService() {
    let configList = APP?.globalData?.serverConfig?.customer_list
    const obj = BASE_CACHE.getBaseCache() || {}
    let province = obj.province?.key
    let customerService = configList.find(
      (item) => item.province_key == province
    ).customer_config
    if (customerService.type == "popup") {
      this.setData({
        weichatCustomerService: customerService,
        weichatShow: true,
      })
      return
    }
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: {
        url: customerService.url,
      },
    })
  },
  async getDrillDetail() {
    const res = await UTIL.request(API.getDrillDetail, {
      id: this.data.id,
    })
    let resData = res.data
    const startTime = new Date(resData.start_time.replace(" ", "T"))
    resData.formatStartTime = this.formatStartTime(startTime)
    if (resData) {
      this.setData({
        speechData: resData,
      })
    }
    console.log(res, "12312321321")
  },
  formatStartTime(date) {
    const monthNames = [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月",
    ]

    const month = monthNames[date.getMonth()]
    const day = date.getDate()
    const hours = String(date.getHours()).padStart(2, "0")
    const minutes = String(date.getMinutes()).padStart(2, "0")

    return `${month}${day}日 ${hours}:${minutes}`
  },
  // 查看完整报告
  goResult() {
    ROUTER.navigateTo({
      path: "/pages/speech/report/index",
      query: {
        record_id: this.data.speechData.join_record.id,
        item_no: this.data.speechData.id,
      },
    })
  },
  // 他人报告页
  goOther(e) {
    const data = e.currentTarget.dataset.item
    ROUTER.navigateTo({
      path: "/pages/speech/report/index",
      query: {
        record_id: data.id,
        item_no: this.data.speechData.id,
      },
    })
  },
  // 参加演练
  joinSpeech() {
    // 获取当前时间
    const now = new Date()

    // 假设 start_time 是 "2025-03-19 18:43:48"
    const startTimeStr = this.data.speechData.start_time

    // 解析开始时间字符串为 Date 对象
    const startTime = new Date(startTimeStr.replace(" ", "T")) // 将空格替换为 'T' 以符合 ISO 8601 格式

    // 比较当前时间和开始时间
    if (now > startTime) {
      // 如果当前时间大于开始时间，则进行跳转
      ROUTER.navigateTo({
        path: "/pages/question/many-preparation/index",
        query: {
          type: "drill",
          item_no: this.data.speechData.id,
        },
      })
    } else {
      // 否则，提示用户活动尚未开始
      wx.showToast({
        title: this.data.speechData.is_online
          ? "面试PK赛暂未开启"
          : "演练尚未开始",
        icon: "none",
        duration: 2000,
      })
    }
  },
  goMore() {
    ROUTER.navigateTo({
      path: "/pages/speech/rank/index",
      query: { id: this.data.id },
    })
  },

  getPageShareParams() {
    let query = { ...PAGE_OPTIONS }
    return APP.createShareParams({
      title:
        this.data?.speechData?.share_info?.title ||
        this.data?.speechData?.title,
      imageUrl: this.data?.speechData?.share_info?.image || "",
      path: "/pages/speech/detail/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
