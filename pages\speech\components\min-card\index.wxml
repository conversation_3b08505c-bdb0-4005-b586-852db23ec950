<wxs module="utils2">
  function formatTime(input) {
    // 尝试将输入转换为整数
    var seconds = parseInt(input, 10);

    // 如果秒数为0，直接返回"00:00"
    if (seconds == 0) {
      return "00:00";
    }

    var hours = Math.floor(seconds / 3600);
    var minutes = Math.floor((seconds % 3600) / 60);
    seconds = Math.floor(seconds % 60);

    // 使用padZero确保每部分都是两位数
    var formattedHours = padZero(hours);
    var formattedMinutes = padZero(minutes);
    var formattedSeconds = padZero(seconds);

    // 根据是否有小时来决定返回的格式
    if (hours > 0) {
      // 如果有时，则格式化为 "XX:XX:XX"
      return formattedHours + ":" + formattedMinutes + ":" + formattedSeconds;
    } else {
      // 如果没有小时，则仅格式化为 "XX:XX"
      return formattedMinutes + ":" + formattedSeconds;
    }
  }

  function padZero(number) {
    return number < 10 ? "0" + number : "" + number;
  }

  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };
  module.exports = {
    formatTime: formatTime,
    getNum: getNum
  };
</wxs>
<view class="list-item" wx:for="{{list }}" wx:key="index" bindtap="goResult" data-item="{{item}}">
  <view class="top-area">
    <view class="top-left">
      <view class="list-title-text text-ellipsis-2"><text wx:if="{{item.label}}" class="label" style="background: {{item.label.color}};">{{item.label.txt}}</text>{{item.title}}</view>
      <view class="label-box" wx:if="{{!item.join_record }}">
        <block wx:if="{{item.qv_data}}">
          <view class="label-text">题量：<text>{{item.qv_data.question_number}}题</text></view>
          <view class="lines"></view>
          <view class="label-text">作答时限：<text>{{item.qv_data.answer_time}}分钟</text></view>
        </block>
      </view>
      <view class="label-box" wx:else>
        <block wx:if="{{item.join_record.correct_status == 0}}">
          <text style="color: #E60000;font-size: 24rpx;">报告生成中</text>
        </block>
        <block wx:if="{{item.join_record.correct_status == -1}}">
          <text style="color: #E60000;font-size: 24rpx;">报告生成失败</text>
        </block>
        <block wx:if="{{item.join_record.correct_status == 1}}">
          <view class="label-text">作答用时：<text>{{utils2.formatTime(item.join_record.answer_time)}}</text></view>
          <view class="lines"></view>
          <view class="label-text red">得分：<text>{{utils2.getNum(item.join_record.score)}}</text></view>
        </block>
      </view>

    </view>
    <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
  </view>
  <view class="line"></view>
  <view class="bottom-area">
    <!-- 已开始 -->
    <block wx:if="{{item.status == 1}}">
      <block wx:if="{{item.participants>0}}">
        <view class="left">
          <block wx:if="{{item.top3_users.length>0}}">
            <view class="photo" wx:for="{{item.top3_users}}" wx:key="index">
              <view class="index-label {{index==1?'hui':''}} {{index==2?'ju':''}}">{{index+1}}
              </view>
              <image src="{{item.portrait}}"></image>
            </view>
          </block>
        </view>
        <!-- <view class="right">{{item.participants}}人参与</view> -->
      </block>
      <view class="left" wx:else>
        <view class="fire-box">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_fire.png"></image>
          演练进行中
        </view>
      </view>
    </block>
    <!-- 未开始 -->
    <block wx:if="{{item.status == 2}}">
      <view class="left">
        <view class="start-time">
          {{item.formattedStartTime}}开始
        </view>
      </view>
    </block>
  </view>
</view>