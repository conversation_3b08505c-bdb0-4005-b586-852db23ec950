const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
Page({
  /**
   * 题目列表！！！！！！！！！！！！！！！！！！！
   */
  data: {
    id: null,
    questionInfo: {},
    isLogin: false, // 用户是否登录
    pageTitle: "",
    page: 1,
    limit: 20,
    isRequest: false,
    isComplete: false,
    share_info: {},
    isNeedFreshData: {}, // 是否需要刷新数据的页码和题目
    elementTop: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    this.pageOptions = options
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
    if (this.pageOptions.id) {
      this.setData({
        id: this.pageOptions.id,
      })
    }
    this.getList(1)
  },
  getElementTop() {
    if (!this.data.elementTop) {
      const query = wx.createSelectorQuery() // 创建选择器查询实例
      query
        .select(".action-bar-box") // 选择目标元素（通过 id 或 class）
        .boundingClientRect((rect) => {
          if (rect) {
            this.setData({
              elementTop: rect.top, // 获取元素到顶部的距离
            })
            console.log("元素到顶部的距离:", rect.top)
          }
        })
        .exec() // 执行查询
    }
  },
  onShow() {
    if (this.data.isComplete && this.data.isNeedFreshData.page) {
      this.setData({
        isLogin: APP.getIsUserLogin(),
      })
      this.getNewList()
    }
  },
  async getNewList() {
    let arr = []
    const { id, menu_id } = this.pageOptions
    let param = {
      id,
      menu_id,
      limit: this.data.limit,
      page: this.data.isNeedFreshData.page,
    }
    const res = await UTIL.request(API.getSubjectList, param)
    arr = res.data.questions || []
    let data = {}
    if (arr.length) {
      data = arr.find((item) => item.id == this.data.isNeedFreshData.id)
      data.page = this.data.isNeedFreshData.page
    }
    let oldArr = this.data.questionInfo.questions
    for (let i = 0; i < oldArr.length; i++) {
      if (oldArr[i].id == this.data.isNeedFreshData.id) {
        oldArr[i] = data
      }
    }
    console.log(oldArr, "33333333333333333333")
    this.setData({
      ["questionInfo.questions"]: oldArr,
      ["questionInfo.verify_record"]: res.data?.verify_record,
    })
  },
  async getList(type) {
    const { id, menu_id } = this.pageOptions
    const param = {
      id,
      menu_id,
      page: this.data.page,
      limit: this.data.limit,
    }
    const res = await UTIL.request(API.getSubjectList, param)
    if (res?.error?.code === 0) {
      let obj = {}
      let isRequest = this.data.isRequest
      isRequest = res.data.questions.length < this.data.limit ? false : true
      let arr = []
      let newArr = res.data.questions || []
      if (newArr.length) {
        newArr.forEach((item) => {
          item.page = this.data.page
        })
      }
      arr = type == 1 ? newArr : this.data.questionInfo.questions.concat(newArr)
      obj.questions = arr
      obj.title = res.data.title
      obj.verify_record = res.data?.verify_record
      this.setData({
        questionInfo: obj,
        isRequest,
        isComplete: true,
        pageTitle: res.data.title,
        share_info: res.data.share_info,
      })
      this.getElementTop()
    }
  },
  goLinkUrl(e) {
    const { item } = e.currentTarget.dataset
    let obj = {
      page: item.page,
      id: item.id,
    }
    this.setData({
      isNeedFreshData: obj,
    })
    let linkId = item.link?.id
    if (!linkId) {
      return
    }
    let path = ""
    if (item.link.type == "question_list") {
      if (item.link.id == this.data.id) {
        return
      }
      path = "/pages/situation/subjectList/index"
    } else {
      path = "/pages/situation/sheetDetail/index"
    }
    if (getCurrentPages().length > 6) {
      ROUTER.reLaunch({
        path: path,
        query: {
          id: item.link.id,
        },
      })
    } else {
      ROUTER.navigateTo({
        path: path,
        query: {
          id: item.link.id,
        },
      })
    }
  },

  toSingeQuestion(e) {
    const item = e.detail.item
    let param = this.pageOptions
    param.question_id = item.id
    param.type = "question"
    let obj = {
      page: item.page,
      id: item.id,
    }
    this.setData({
      isNeedFreshData: obj,
    })
    if (item.record_data.is_involved == 0) {
      ROUTER.navigateTo({
        path: "/pages/question/answer-vertical/index",
        query: param,
      })
    } else {
      param.item_no = this.pageOptions.id
      ROUTER.navigateTo({
        path: "/pages/question/single-detail/index",
        query: param,
      })
    }
  },
  getPageShareParams() {
    let query = this.pageOptions
    return APP.createShareParams({
      title: this.data.share_info.title || "",
      imageUrl: this.data.share_info.image || "",
      path: "/pages/situation/subjectList/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
  onReachBottom() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getList(2)
  },
  bottomNew() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getList(2)
  },
})
