const APP = getApp()
const ROUTER = require("@/services/routerManager")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: null,
    },
    // 当前是否是在首页展示
    isInHome: {
      type: Boolean,
      value: false,
    },
    attachParams: { type: Object, value: {} },
  },
  options: {},

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    goDetail(e) {
      const data = e.currentTarget.dataset.item
      const cmd = this.parseCmdJson(data.cmd_json)
      APP.cmdManage(cmd)
    },
    parseCmdJson(cmdJsonStr) {
      try {
        // 尝试将字符串转换为对象
        const cmdJson = JSON.parse(cmdJsonStr)
        return cmdJson
      } catch (error) {
        console.error("JSON 解析错误:", error)
        // 如果解析失败，返回 null 或者其他默认值
        return null
      }
    },
  },
})
