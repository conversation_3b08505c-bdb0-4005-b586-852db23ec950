<wxs module="utils">
  // 格式化时间戳
  function formatTimeStamp(time) {
    // 辅助函数需要定义在外部并在此处引用或直接定义在这里
    var formatSingleNumber = function (number) {
      return number <= 9 ? "0" + number : number;
    };

    time = time >>> 0; // 确保time是整数
    if (time < 0) {
      time = 0;
    }

    var minute = Math.floor((time % 3600) / 60),
      second = Math.floor(time % 60);

    // 返回格式化的分钟和秒
    var ms = formatSingleNumber(minute) + ":" + formatSingleNumber(second);

    return ms;
  }
  module.exports = {
    formatTimeStamp: formatTimeStamp,
  }
</wxs>
<rule-header pageTitle="套卷练习" show_white="{{show_white}}" bindbackPage="backPage"></rule-header>
<view wx:if="{{isComplete}}" class="main-content">
  <view class="rule-box">
    <image class="rule-comma" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_comma.png"></image>
    <view class="title" style="font-weight: bold;">{{questionData.question_volume_info.name}}</view>
    <view class="face-time">
      <view class="text">面试时长</view>
      <view class="time"><text>{{questionData.question_volume_info.answer_time}}</text>分钟</view>
    </view>
    <view class="question-setting">
      <view class="titles">选择出题模式</view>
      <view class="question-setting-box">
        <view class="question-setting-item {{activeIndex==0?'active':''}}" bindtap="changeSetting" data-item="{{0}}">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_listen.png" class="icon"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_listen_select.png" class="icon-select"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_select.png" class="select"></image>

          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_listen_bg.png" class="select-bg"></image>
          <text>听题模式</text>
        </view>
        <view class="question-setting-item  {{activeIndex==1?'active':''}}" bindtap="changeSetting" data-item="{{1}}">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_see.png" class="icon"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_see_select.png" class="icon-select"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_select.png" class="select"></image>

          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_select_bg.png" class="select-bg" style="top: 15rpx;"></image>
          <text>看题模式</text>
        </view>
      </view>
    </view>
  </view>
  <view class="tab-box">
    <view class="tab-list-area">
      <view class="list-item {{tabListIndex === 1?'active':''}}" bind:tap="changeIndex" data-index="{{1}}">作答流程</view>
      <view class="list-item {{tabListIndex === 2?'active':''}}" bind:tap="changeIndex" data-index="{{2}}">题目列表</view>
      <view class="list-item {{tabListIndex === 3?'active':''}}" bind:tap="changeIndex" data-index="{{3}}">历史作答</view>
    </view>
    <view class="zuoda-answer" wx:if="{{tabListIndex === 1}}">
      <!-- <view class="answering-title">考试说明</view> -->
      <block wx:if="{{activeIndex==0}}">
        <view class="progress-list" wx:for="{{questionData.process_list_listen}}" wx:key="index">
          <image class="num-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/progress_list_{{index + 1}}.png" mode="" />
          <view class="progress-list-item">
            <view class="title">{{item.title}}</view>
            <view class="desc">{{item.desc}}</view>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="progress-list" wx:for="{{questionData.process_list}}">
          <image class="num-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/progress_list_{{index + 1}}.png" mode="" />
          <view class="progress-list-item">
            <view class="title">{{item.title}}</view>
            <view class="desc">{{item.desc}}</view>
          </view>
        </view>
      </block>
      <view class="text-box" wx:if="{{questionData.question_volume_info.explanation}}">
        <view class="text-title">说明</view>
        <text>{{questionData.question_volume_info.explanation}} </text>
      </view>
    </view>
    <view class="question-list" wx:if="{{tabListIndex === 2}}">
      <authorize-phone wx:for="{{questionInfo.questions}}" wx:key="index" isBindPhone="{{isLogin}}" item="{{item}}" bind:onAuthorize="toSingeQuestion">
        <view class="list-card-item">
          <view class="left text-ellipsis-2">
            <text class="text-name">题目{{index + 1}}</text>
            <text class="title-span">{{item.content}}</text>
          </view>
          <image class="right-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
        </view>
      </authorize-phone>
      <view class="bottom-text">点击题目可进行单题练习</view>
    </view>
    <block wx:if="{{tabListIndex === 3}}">
      <view style="min-height: 798rpx;" class="answering-process" wx:if="{{historyList.length>0}}">
        <view class="answering-process-box">
          <view class="process-item" catchtap="goRecordId" data-id="{{item.record_id}}" wx:for="{{historyList}}" wx:key="index">
            <view class="yuan"></view>
            <view class="time">{{item.record_time}}</view>
            <view class="right-box">
              <view class="left-font">
                <view class="titles text-ellipsis-2">第{{item.record_num}}次作答</view>
                <view class="font-bottom">
                  <view class="waiting" wx:if="{{item.correct_status ==0}}">
                    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_waiting.png"></image>
                    报告生成中…
                  </view>
                  <view class="time-all" wx:if="{{item.correct_status ==-1}}">
                    报告生成失败
                  </view>
                  <block wx:if="{{item.correct_status ==1}}">
                    <view class="time-all">时长：<text>{{utils.formatTimeStamp(item.answer_time)}}</text></view>
                    <view class="line"></view>
                    <view class="score-all">得分：<text>{{item.score}}</text></view>
                  </block>
                </view>
              </view>
              <image class="right-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
            </view>
          </view>
        </view>
      </view>
      <view wx:else style="min-height: 632rpx;">
        <tips-default text="暂无历史作答记录" width="280" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_history_no_data.png"></tips-default>
      </view>
    </block>
  </view>
  <view class="action-bar-box">
    <remain-time isMultiple="{{ true }}" wx:if="{{questionInfo.verify_record&&isLogin}}" verify-record="{{questionInfo.verify_record}}"></remain-time>
    <view class="action-bar container flex-justify_between">
      <authorize-phone isBindPhone="{{isLogin}}" bind:onAuthorize="collectTap">
        <view class="left-collect">
          <image wx:if="{{questionInfo.is_collect == 1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_star_select.png"></image>
          <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_star.png"></image>
          <text>{{questionInfo.is_collect==1?'取消':'收藏'}}</text>
        </view>
      </authorize-phone>
      <view class="button opc-05" wx:if="{{(questionInfo.verify_record.error_code == '7009'|| questionInfo.verify_record.error_code == '7010' || questionInfo.verify_record.error_code == '7011')&&isLogin}}">开始套卷练习</view>
      <authorize-phone wx:else class="button" customClass="button" isBindPhone="{{isLogin}}" bind:onAuthorize="goQuestion">
        开始套卷练习
      </authorize-phone>
    </view>
  </view>
</view>

<!--  -->