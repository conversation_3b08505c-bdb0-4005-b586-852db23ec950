const ROUTER = require("@/services/routerManager")
const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const BASE_CACHE = require("@/utils/cache/baseCache")

let timer = null
let pollCount = 0
let audioService = null
Page({
  /**
   * 页面的初始数据
   */
  data: {
    stickyHeightText: "",
    stickyHeight: null,
    resultTitle: "",
    question_type: "", // single-单题  multiple-多题
    type: "", // accumulation-素材、drill-演练、booklet-套卷、exercise-专项练习、question-题目列表
    tabList: [],
    activeTabIndex: 0,
    show_white: false,
    is_self_record: null, // 是否自己的作答：0-不是 1-是
    questionInfo: {}, // 试卷整体信息
    questionData: [], // 题目集合
    adv_info: {}, // 广告
    isComplete: 0, // 是否加载完成 0-加载中 1-加载完成 -1加载失败
    mediaStatus: {}, // 用于存储所有媒体状态
    aiAudioInfo: {}, // ai点评语音
    share_info: {},
    weichatShow: false, // 微信客服弹窗
    weichatCustomerService: false, // 微信客服信息
    waitMessage: "",
    record_data_map: {},
    scrollIntoViewId: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    timer = null
    pollCount = 0
    this.scrollMap = {
      list: {},
      top: 0,
    }
    this.pageOptions = options
    await APP.checkLoadRequest()
    wx.hideShareMenu()
    audioService = APP.globalData.audioService
    audioService.stop()
    await this.getDetailInfo()
    this.startPollRequest()
  },
  async getDetailInfo() {
    // 1452879d302743cf8605ce7e66be40e5
    const param = {
      record_id: this.pageOptions.record_id || "",
      uuid: this.pageOptions.uuid || "",
      question_id: this.pageOptions.question_id || "",
      type: this.pageOptions.type || "",
      item_no: this.pageOptions.item_no || "",
    }
    // const param = {
    //   record_id: 1,
    //   // uuid: "6e1b125aaa3848aa83828c30767b127f", // 单题
    //   question_id: "11448022",
    //   type: "accumulation",
    //   item_no: 1,
    // }
    // const param = {
    //   record_id: 2,
    //   uuid: "e3fde66377d9426aad2c82b9c2314ca5", // 多题
    //   question_id: "",
    //   type: "booklet",
    //   item_no: 1,
    // }
    const res = await UTIL.request(API.getRecordData, param)
    let data = {}
    let activeTabIndex = this.data.activeTabIndex
    if (res?.error?.code === 0) {
      data = res.data
      let arr = []
      let obj = {}
      if (data.record_list.length) {
        data.record_list.forEach((item, index) => {
          // item.aiData = this.splitRichTextByKeywords(item.correct.text)
          // if (item.reference_answer?.dalta) {
          //   item.reference_answer = parseQuestionHtml(
          //     item.reference_answer.dalta
          //   )
          // }
          // if (item.question_content_delta) {
          //   item.question_content = parseQuestionHtml(
          //     item.question_content_delta
          //   )
          // }
          if (item.answer.video) {
            obj[`video-${index}-answer`] = { isPlaying: false }
          } else if (item.answer.audio) {
            obj[`audio-${index}-answer`] = { isPlaying: false }
          }

          if (item.video_data.url) {
            obj[`video-${index}-jiexi`] = { isPlaying: false }
          }
        })
        if (
          data.record_data?.question_type === "single" &&
          data.record_list[0].correct.audio
        ) {
          // AI点评语音
          this.getAiaudioInfo(data.record_list[0].correct)
        }
      }
      if (data.record_data?.question_type === "multiple") {
        // data.record_data.aiData = this.splitRichTextByKeywords(
        //   data.record_data.correct_text
        // )
        data.record_list.forEach((item, index) => {
          arr.push({
            name: `第${index + 1}题`,
            key: index + 1,
            question_id: item.question_id,
          })
        })
        if (arr.length) {
          arr.unshift({
            name: "整体报告",
            key: 0,
          })
        }
        if (this.pageOptions.to_question_id) {
          activeTabIndex =
            arr.find(
              (item) => item.question_id == this.pageOptions.to_question_id
            )?.key || 0
        }
      }
      if (data.record_data.is_self_record == 0) {
        wx.hideShareMenu()
      } else {
        if (data.record_data.is_finished == 1) {
          wx.showShareMenu()
        }
      }
      this.scrollMap.list = arr.reduce((acc, item) => {
        acc[item.key] = {
          top: 0,
        }
        return acc
      }, {})
      this.setData({
        question_type: data.record_data?.question_type,
        is_self_record: data.record_data?.is_self_record,
        type: data.record_data?.type,
        questionInfo: data.record_data,
        questionData: data.record_list || [],
        tabList: arr,
        adv_info: data.adv_info,
        isComplete: data.record_data.is_finished == 1 ? 1 : 0,
        mediaStatus: obj,
        activeTabIndex,
        share_info: data.share_info,
        record_data_map: data.record_data_map,
      })
      this.getConfig()
    } else {
      if (res?.error?.code === 7007 || res?.error?.code === 7020) {
        this.setData({
          isComplete: res.error.code,
          waitMessage: res.error.message,
        })
      } else {
        try {
          wx.reportEvent("event_report_error", {
            request_params: JSON.stringify(param),
            request_result: JSON.stringify(res),
          })
        } catch (error) {}
        this.setData({
          isComplete: -1,
        })
        clearInterval(timer)
      }
    }
    if (pollCount > 60) {
      try {
        res.errorText = "请求次数超出限制"
        wx.reportEvent("event_report_error", {
          request_params: JSON.stringify(param),
          request_result: JSON.stringify(res),
        })
      } catch (error) {}
      this.setData({
        isComplete: 7020,
      })
    }
  },
  getAiaudioInfo(data) {
    this.aiAudioCtx = wx.createInnerAudioContext()
    this.aiAudioCtx.src = data.audio
    let obj = {}
    obj.text = data.text
    obj.currentTime = 0
    obj.duration = 0
    obj.isPlaying = false
    this.setData({
      aiAudioInfo: obj,
    })
    // 监听canplay事件
    this.aiAudioCtx.onCanplay(() => {
      if (
        this.data.aiAudioInfo.duration &&
        this.data.aiAudioInfo.duration > 0
      ) {
        return
      }
      this.checkDuration(this.aiAudioCtx, (duration) => {
        let newObj = this.data.aiAudioInfo
        newObj.duration = duration
        if (this.pageOptions.uuid && !this.pageOptions.record_id) {
          newObj.isPlaying = true
          this.aiAudioCtx.play()
        }
        this.setData({
          aiAudioInfo: newObj,
        })
      })
    })

    // 监听ended事件
    this.aiAudioCtx.onEnded(() => {
      let newObj = this.data.aiAudioInfo
      newObj.currentTime = 0
      newObj.isPlaying = false
      this.setData({
        aiAudioInfo: newObj,
      })
    })

    this.aiAudioCtx.onTimeUpdate(() => {
      let newObj1 = this.data.aiAudioInfo
      newObj1.currentTime = this.aiAudioCtx.currentTime
      this.setData({
        aiAudioInfo: newObj1,
      })
    })
  },
  // 定义一个函数用于检查音频时长
  checkDuration(aiAudioCtx, callback) {
    if (aiAudioCtx.duration > 0 && !isNaN(aiAudioCtx.duration)) {
      // 如果已经获取到有效的时长，则调用回调函数
      callback(aiAudioCtx.duration)
    } else {
      // 否则，稍候再试
      setTimeout(() => this.checkDuration(aiAudioCtx, callback), 500) // 每隔500毫秒重试一次
    }
  },

  playAiAudio() {
    if (this.aiAudioCtx) {
      if (this.data.aiAudioInfo.isPlaying) {
        this.aiAudioCtx.pause()
        this.setData({
          [`aiAudioInfo.isPlaying`]: false,
        })
      } else {
        this.aiAudioCtx.play()
        this.onAudioPlay()
        this.setData({
          [`aiAudioInfo.isPlaying`]: true,
        })
      }
    }
  },
  getConfig() {
    let title = ""
    if (this.data.question_type === "multiple") {
      this.getHeight()
      title = ""
    } else {
      title = "作答报告"
    }
    if (this.data.is_self_record === 0) {
      title = this.data.questionInfo.user_info.nickname
    }
    this.setData({
      resultTitle: title,
    })
  },
  getHeight() {
    if (this.data.isComplete != 1) {
      return
    }
    const query = wx.createSelectorQuery()
    const newQuery = wx.createSelectorQuery()

    // 同时查询两个节点的信息
    query
      .select(".report-area")
      .boundingClientRect((rect1) => {
        if (!rect1) {
          console.error("未能找到 .report-area 元素")
          return
        }
        let height = rect1.top - 1
        let heightText = "top:" + height + "px;"
        this.setData({
          stickyHeightText: heightText,
        })
        newQuery
          .select(".tab-area")
          .boundingClientRect((rect2) => {
            if (!rect2) {
              console.error("未能找到 .tab-area 元素")
              return
            }
            console.log(rect2.top, "333")
            const showHeight = rect2.top - rect1.top
            this.setData({
              stickyHeight: showHeight,
            })
          })
          .exec()
      })
      .exec()
  },

  // 轮询请求
  startPollRequest() {
    timer = setInterval(() => {
      pollCount++
      if (this.data.isComplete == 1) {
        clearInterval(timer)
        return
      }
      this.getDetailInfo()
    }, 3000)
  },

  // 切换题目
  changeQuestion(e) {
    console.log(e, "11")
    const { key } = e.currentTarget.dataset
    const scrollKey = this.data.tabList.find((item) => item.key == key).key
    if (key == this.data.activeTabIndex) {
      return
    }
    this.setData({
      activeTabIndex: key,
      scrollIntoViewId: `tab-${scrollKey}`,
    })
    let pageScrollTop = 0
    let height =
      this.data.question_type === "single"
        ? APP.globalData.navHeight
        : this.data.stickyHeight
    const goalTop = this.scrollMap.list[this.data.activeTabIndex]?.top || 0
    const currentTop = this.scrollMap.top
    if (currentTop < height) {
      pageScrollTop = currentTop
    } else {
      if (goalTop < height) {
        pageScrollTop = height + 1
      } else {
        pageScrollTop = goalTop
      }
    }
    this.scrollMap.list[this.data.activeTabIndex].top = pageScrollTop
    wx.pageScrollTo({
      scrollTop: pageScrollTop,
      duration: 0,
    })
  },
  onPageScroll(e) {
    let height =
      this.data.question_type === "single"
        ? APP.globalData.navHeight
        : this.data.stickyHeight
    let title = this.data.resultTitle
    if (e.scrollTop > height) {
      // 当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        if (
          this.data.question_type === "multiple" &&
          this.data.is_self_record === 1
        ) {
          title = this.data.type === "drill" ? "面试演练报告" : "模拟面试报告"
        }
        this.setData({
          show_white: true,
          resultTitle: title,
        })
      }
    } else {
      if (this.data.show_white) {
        if (
          this.data.question_type === "multiple" &&
          this.data.is_self_record === 1
        ) {
          title = ""
        }
        this.setData({
          show_white: false,
          resultTitle: title,
        })
      }
    }
    this.scrollMap.top = e.scrollTop
    this.scrollMap.list[this.data.activeTabIndex].top = e.scrollTop
    console.log(this.scrollMap, "00000000000000000")
  },
  // splitRichTextByKeywords(text) {
  //   // 初始化结果对象
  //   let result = {
  //     advantages: "", // 存储优点部分（不包括“优点：”）
  //     disadvantages: "", // 存储不足部分（不包括“不足：”）
  //   }

  //   // 查找“优点：”和“不足：”的位置
  //   let advantagesStartIndex = text.indexOf("优点：")
  //   let disadvantagesStartIndex = text.indexOf("问题：")

  //   if (advantagesStartIndex !== -1 && disadvantagesStartIndex !== -1) {
  //     // 提取优点部分，并去除“优点：”关键字及后面的换行符
  //     let advantagesPart = text.substring(
  //       advantagesStartIndex + 3,
  //       disadvantagesStartIndex
  //     )
  //     // 去除开头的空白字符
  //     advantagesPart = advantagesPart.replace(/^\s*/g, "")
  //     result.advantages = advantagesPart

  //     // 提取不足部分，并去除“不足：”关键字及后面的换行符
  //     let disadvantagesPart = text.substring(disadvantagesStartIndex + 3)
  //     // 去除开头的空白字符
  //     disadvantagesPart = disadvantagesPart.replace(/^\s*/g, "")
  //     result.disadvantages = disadvantagesPart
  //   } else {
  //     console.log("未找到分隔关键字")
  //   }

  //   return result
  // },
  onAudioPlay(event) {
    console.log(event, "33333")
    const mediaId = event?.detail?.mediaId || null
    if (mediaId) {
      try {
        this.aiAudioCtx.pause()
        this.setData({
          [`aiAudioInfo.isPlaying`]: false,
        })
      } catch (error) {}
    }
    console.log(mediaId, this.data.mediaStatus, "11111111")
    Object.keys(this.data.mediaStatus).forEach((key) => {
      if (key !== mediaId) {
        const audioComponent = this.selectComponent(`#${key}`)
        console.log(audioComponent, "222222222")
        if (audioComponent) {
          audioComponent.pause()
        }
      }
      this.setData({
        [`mediaStatus.${key}.isPlaying`]: key == mediaId ? true : false,
      })
    })
  },
  onAudioPause(event) {
    const mediaId = event.detail.mediaId
    this.setData({
      [`mediaStatus.${mediaId}.isPlaying`]: false,
    })
  },
  singlePlayVideo() {
    this.pauseVideo()
  },
  goBack() {
    if (getCurrentPages().length === 1) {
      wx.reLaunch({
        url: "/pages/practice/home/<USER>",
      })
      return false
    }
    wx.navigateBack({
      delta: 1,
    })
  },
  onUnload() {
    clearInterval(timer)
    if (this.aiAudioCtx) {
      this.aiAudioCtx.stop()
      this.aiAudioCtx.destroy()
    }
  },
  goAdvDetail(e) {
    const data = e.currentTarget.dataset.item
    const cmd = JSON.parse(data.cmd_json)
    // 执行指令
    APP.cmdManage(cmd)
  },
  // 预览图片
  checkImg(e) {
    const { url } = e.currentTarget.dataset
    wx.previewImage({
      current: url, // 当前显示图片的链接
      urls: [url], // 需要预览的图片链接列表（即使只有一张图片，也需要放在数组中）
    })
  },
  goOther(e) {
    const { type } = e.currentTarget.dataset
    if (type == "-1") {
      // 跳客服
      this.openWeichatCustomerService()
    } else {
      // 跳演练详情
      ROUTER.navigateTo({
        path: "/pages/question/many-preparation/index",
        query: {
          type: this.data.questionInfo.type,
          item_no: this.data.questionInfo.item_no,
        },
      })
    }
  },
  goRank() {
    ROUTER.navigateTo({
      path: "/pages/speech/rank/index",
      query: {
        id: this.data.questionInfo.item_no,
      },
    })
  },
  goReport(e) {
    const { data } = e.currentTarget.dataset
    if (data.is_my) {
      return
    }
    ROUTER.navigateTo({
      path: "/pages/speech/report/index",
      query: {
        record_id: data.id,
      },
    })
  },
  getPageShareParams() {
    let path = "/pages/speech/detail/index"
    let query = {}
    query.id = this.data.questionInfo.item_no
    // query.type = "drill"
    return APP.createShareParams({
      title: this.data.share_info.title || "",
      imageUrl: this.data.share_info.image || "",
      path,
      query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  // 处理关闭校区微信客服
  closeWeichatCustomerService() {
    this.setData({
      weichatShow: false,
    })
  },

  // 处理打开校区微信客服
  openWeichatCustomerService() {
    let configList = APP?.globalData?.serverConfig?.customer_list
    const obj = BASE_CACHE.getBaseCache() || {}
    let province = obj.province?.key
    let customerService = configList.find(
      (item) => item.province_key == province
    ).customer_config
    if (customerService.type == "popup") {
      this.setData({
        weichatCustomerService: customerService,
        weichatShow: true,
      })
      return
    }
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: {
        url: customerService.url,
      },
    })
  },
})
