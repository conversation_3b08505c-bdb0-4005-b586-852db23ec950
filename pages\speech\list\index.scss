page {
  background: #f2f4f7;
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/banner_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
}

.main-content {
  padding: 0 32rpx;
  // padding-top: 24rpx;
  padding-bottom: 130rpx;
}

.speech-box {
  margin-bottom: 64rpx;
  margin-top: 16rpx;
}

.list-area {
  .top-title-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    &-right {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      .image-arrow {
        width: 24rpx;
        height: 24rpx;
      }
    }
    .title {
      font-weight: 500;
      font-size: 34rpx;
      color: #22242e;
      font-weight: bold;
    }
  }
}

.tab-list {
  // background: #fff;
  padding-left: 32rpx;
  padding-bottom: 24rpx;
  padding-top: 24rpx;
  position: sticky;
  z-index: 222;
  left: 0;
  &.bgf {
    background: #fff;
  }
  .tab-mt {
    margin-top: 12rpx;
  }
}

.deafult-box {
  width: 100%;
  position: relative;
  margin-bottom: 64rpx;
  margin-top: 16rpx;
  .deafult-bg {
    width: 100%;
    display: block;
  }
  .pr-box {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    image {
      width: 96rpx;
    }
    .text {
      font-size: 24rpx;
      color: rgba(234, 234, 234, 1);
      margin-top: 40rpx;
    }
  }
}

.oh-hideen {
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  z-index: 0;
}
