// components/image-preview/image-preview.js
Component({
  properties: {
    // 图片 URL
    imageUrl: {
      type: String,
      value: "",
    },
  },

  data: {
    isVisible: false, // 是否显示预览弹窗
  },

  methods: {
    // 打开预览弹窗
    openModal() {
      this.setData({
        isVisible: true,
      })
    },

    // 关闭预览弹窗
    closeModal() {
      this.setData({
        isVisible: false,
      })
    },
    // 新增阻止关闭方法
    noop(e) {
      // 空函数仅用于阻止事件冒泡
    },
  },
})
