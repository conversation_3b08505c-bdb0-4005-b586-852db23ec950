<view class="main-content" wx:if="{{examListArray.length>0}}">
  <view class="main-title">选择报考方向和地区</view>
  <view class="exam-direction">
    <view class="title">报考方向</view>
    <exam-list data="{{examListArray}}" activeKey="{{examKey}}" bind:onChange="onChangeExam" />
    <view class="title mt32">报考地区</view>
    <province-list wx:if="{{provinceListArray.length>0}}" data="{{provinceListArray}}" activeKey="{{currentProvincekey}}" bind:onChange="onChangeProvince" />
  </view>
  <!-- 底部操作栏 -->
  <view class="action-bar-box">
    <view class="action-bar container flex-justify_between">
      <view class="button {{(!examKey||!currentProvincekey)?'hui':''}}" bindtap="goHome">确定</view>
    </view>
  </view>
</view>