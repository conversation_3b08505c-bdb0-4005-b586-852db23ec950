<!-- component/calendar/calendar.wxml -->
<wxs src="./index.wxs" module="calculate" />
<view class="calendar">
  <!-- <view class="header">
    <view class="title">
      <view class="header-wrap">
        <view class="flex">
          <view class="title">{{title}}</view>
          <view class="month">
            <block wx:if="{{title}}">(</block>
            {{selectDay.year}}年{{selectDay.month}}月
            <block wx:if="{{title}}">)</block>
          </view>
        </view>
        <block wx:if="{{goNow}}">
          <view wx:if="{{open && !(nowDay.year==selectDay.year&&nowDay.month==selectDay.month&&nowDay.day==selectDay.day)}}" class="today" bindtap="switchNowDate">
            今日
          </view>
        </block>
      </view>
    </view>
  </view> -->
  <!-- 日历头部 -->
  <view class="flex-around calendar-week">
    <view class="calendar-week-item" wx:for="{{calendarHeadDate}}" wx:key="item">
      {{calendarHeadDate[(index-firstDayOfWeek+7)%7]}}
    </view>
  </view>
  <!-- 日历主体 -->
  <swiper class="swiper" style="height:{{swiperHeight}}rpx" bind:animationfinish="swiperChange" circular="{{true}}" current="{{swiperCurrent}}" duration="{{swiperDuration}}">
    <swiper-item wx:for="{{[dateList0, dateList1, dateList2]}}" wx:for-index="listIndex" wx:for-item="listItem" wx:key="listIndex">
      <view class="flex-around flex-wrap calendar-main" style="height:{{listItem.length/7*82}}rpx">
        <view wx:for="{{listItem}}" wx:key="dateList" class="day">
          <view class="bg {{calculate.spot(item,selectDay,spotMap)}} {{ !item.show && open? 'not-show':''}} {{calculate.hasNow(item,nowDay)}}  {{calculate.hasClass(item,footData)}} {{calculate.hasSelect(item,clickDay,oldCurrent,listIndex)}} {{calculate.hasDisable(item,disabledDateList)}}" catchtap="selectChange" data-day="{{item.day}}" data-year="{{item.year}}" data-month="{{item.month}}">
            <view wx:if="{{ calculate.hasNow(item,nowDay) }}">今</view>
            <view wx:else>{{ item.day }}</view>
            <!-- <view class="circle-text {{ calculate.hasNow(item,nowDay)?'today-circle':''}} "></view> -->
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
  <!-- 展开收缩 -->
  <view wx:if="{{showShrink}}" catchtap="openChange" class="flex list-open">
    <view class="bg-color">
      <view class="icon {{open?'fold':'unfold'}}"></view>
    </view>
  </view>
</view>