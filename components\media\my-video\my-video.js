// components/media/my-video.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    videoUrl: String,
    poster: String,
    videoId: String,
  },

  lifetimes: {
    detached() {
      // 确保在组件销毁时停止视频播放并清理资源
      if (this.data.videoId) {
        wx.createVideoContext(this.data.videoId, this).pause()
      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    pause() {
      wx.createVideoContext(this.data.videoId, this).pause()
    },
    onPlay() {
      this.triggerEvent("videoOnPlay", { mediaId: this.data.videoId })
    },
  },
})
