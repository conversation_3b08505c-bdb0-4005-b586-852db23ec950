const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
let PAGE_OPTIONS = {}
Page({
  data: {
    show_white: false,
    drillList: ["1", "2", "3"],
    stickyHeight: null,
    active: "",
    examTabs: {},
    speechData: null,
    isComplete: false, //是否加载完成
    tabShow: false,
    isLogin: false, // 用户是否登录
    weichatShow: false, // 微信客服弹窗
    weichatCustomerService: false, // 微信客服信息
    hideenShow: false,
  },
  async onLoad(options) {
    PAGE_OPTIONS = options
    await APP.checkLoadRequest()
    if (PAGE_OPTIONS.examActive) {
      this.setData({
        ["examTabs.active"]: PAGE_OPTIONS.examActive,
      })
    }
    this.getJobList()
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })

    APP.setCopyCurrentPageUrl(options)
  },
  async onShow() {
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
    await APP.checkLoadRequest()
    this.isSpeech()
    this.getJobList()
    if (this.data.isComplete) {
      this.getList()
    }
  },
  isSpeech() {
    // 获取全局配置中的菜单信息以及缓存中的省份信息
    const menu = APP.globalData?.serverConfig?.wx_menu_province_button_map
    const { province } = BASE_CACHE.getBaseCache() || {}
    let menuList = []

    console.log(province, "省份信息", menu?.[province?.key])

    // 检查菜单是否存在
    if (menu) {
      // 根据省份获取对应的菜单列表，如果不存在则使用默认菜单
      if (menu[province?.key] && menu[province?.key].length > 0) {
        menuList = menu[province?.key]
      } else {
        menuList = menu.default || []
      }

      console.log(menuList, "数据")

      // 检查menuList中是否有item.pathKey等于'speech'
      const hasSpeechPath = menuList.some((item) => item.pathKey === "speech")

      if (!hasSpeechPath) {
        // 如果没有找到pathKey为'speech'的项，则重定向到指定页面
        ROUTER.reLaunch({
          path: "/pages/practice/home/<USER>",
          query: {},
        })
      }

      return hasSpeechPath // 返回检查结果
    }

    return false // 如果menu不存在，则返回false
  },
  async getJobList() {
    const res = await UTIL.request(API.getJobList)
    let arr = res.data.list

    this.setData({
      tabShow: true,
    })

    if (arr.length) {
      arr.forEach((item) => {
        item.exam_key = item.id
        item.exam_name = item.title
      })

      // 判断 this.data.examTabs.active 是否有值并且是否在 arr 中存在对应的 item.exam_key
      let activeKey = null
      if (this.data.examTabs && this.data.examTabs.active) {
        const foundItem = arr.find(
          (item) => item.exam_key === this.data.examTabs.active
        )
        if (foundItem) {
          activeKey = this.data.examTabs.active
        } else {
          activeKey = arr[0].exam_key
        }
      } else {
        activeKey = arr[0].exam_key
      }

      this.setData({
        ["examTabs.list"]: arr,
        ["examTabs.active"]: activeKey,
      })

      this.getList()
      this.getHeight()
    } else {
      this.setData({
        ["examTabs.list"]: [],
      })
    }
  },
  closeHidden() {
    this.setData({
      hideenShow: false,
    })
  },
  openHideen() {
    this.setData({
      hideenShow: true,
    })
  },
  handleExamTabsItemEvent(e) {
    const data = e.detail.data
    this.setData({
      ["examTabs.active"]: data.exam_key,
    })
    this.getList()
  },
  formatStartTime(date) {
    const monthNames = [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月",
    ]

    const month = monthNames[date.getMonth()]
    const day = date.getDate()
    const hours = String(date.getHours()).padStart(2, "0")
    const minutes = String(date.getMinutes()).padStart(2, "0")

    return `${month}${day}日 ${hours}:${minutes}`
  },
  async getList() {
    console.log("进来没得屁")
    this.setData({
      isComplete: true,
    })
    const res = await UTIL.request(API.getJobHome, {
      job_id: this.data.examTabs.active,
    })

    const resData = res.data
    if (resData.offline.length > 0) {
      resData.offline.forEach((outerItem) => {
        if (outerItem.drill_list.length > 0) {
          const now = new Date()

          outerItem.drill_list = outerItem.drill_list.map((item) => {
            const startTime = new Date(item.start_time.replace(" ", "T"))
            const status = now > startTime ? 1 : 2
            const formattedStartTime = this.formatStartTime(startTime)

            return {
              ...item,
              status: status,
              formattedStartTime: formattedStartTime,
            }
          })
        }
      })
    }
    if (resData.online.length > 0) {
      resData.online.forEach((outerItem) => {
        if (outerItem.drill_list.length > 0) {
          const now = new Date()

          outerItem.drill_list = outerItem.drill_list.map((item) => {
            const startTime = new Date(item.start_time.replace(" ", "T"))
            const status = now > startTime ? 1 : 2
            const formattedStartTime = this.formatStartTime(startTime)

            return {
              ...item,
              status: status,
              formattedStartTime: formattedStartTime,
            }
          })
        }
      })
    }
    this.setData({
      speechData: resData,
      // isComplete: true,
    })
    console.log(res.data)
  },
  getHeight() {
    if (this.data.stickyHeight) {
      return
    }
    const query = wx.createSelectorQuery()
    query
      .select(".tab-list") // 替换为你的类名
      .boundingClientRect((rect) => {
        if (rect) {
          let heightText = "top:" + rect.top + "px"
          this.setData({
            stickyHeight: heightText,
          })
          console.log("距离顶部高度: ", rect.top) // 输出距离顶部的高度
        }
      })
      .exec()
  },
  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  goDetail(e) {
    console.log(e)
    const data = e.detail.data
    // 获取当前时间
    const now = new Date()

    // 假设 start_time 是 "2025-03-19 18:43:48"
    const startTimeStr = data.start_time

    // 解析开始时间字符串为 Date 对象
    const startTime = new Date(startTimeStr.replace(" ", "T")) // 将空格替换为 'T' 以符合 ISO 8601 格式
    if (now > startTime) {
      ROUTER.navigateTo({
        path: "/pages/speech/detail/index",
        query: {
          id: data.id,
        },
      })
    } else {
      wx.showToast({
        title: "面试PK赛暂未开启",
        icon: "none",
        duration: 2000,
      })
    }
  },
  handleJoinButton(e) {
    const data = e.detail.data
    // 获取当前时间
    const now = new Date()

    // 假设 start_time 是 "2025-03-19 18:43:48"
    const startTimeStr = data.start_time

    // 解析开始时间字符串为 Date 对象
    const startTime = new Date(startTimeStr.replace(" ", "T")) // 将空格替换为 'T' 以符合 ISO 8601 格式
    if (now > startTime) {
      if (data.join_record) {
        ROUTER.navigateTo({
          path: "/pages/speech/report/index",
          query: {
            record_id: data.join_record.id,
          },
        })
      } else {
        ROUTER.navigateTo({
          path: "/pages/speech/detail/index",
          query: {
            id: data.id,
          },
        })
      }
    }
  },
  // 参加演练
  joinSpeech(e) {
    const data = e.detail.data
    // 获取当前时间
    const now = new Date()

    // 假设 start_time 是 "2025-03-19 18:43:48"
    const startTimeStr = data.start_time

    // 解析开始时间字符串为 Date 对象
    const startTime = new Date(startTimeStr.replace(" ", "T")) // 将空格替换为 'T' 以符合 ISO 8601 格式
    if (now > startTime) {
      // 如果当前时间大于开始时间，则进行跳转
      ROUTER.navigateTo({
        path: "/pages/question/many-preparation/index",
        query: {
          type: "drill",
          item_no: data.id,
        },
      })
    } else {
      // 否则，提示用户活动尚未开始
      wx.showToast({
        title: "面试PK赛暂未开启",
        icon: "none",
        duration: 2000,
      })
    }
  },
  seeMore(e) {
    const index = e.currentTarget.dataset.index
    ROUTER.navigateTo({
      path: "/pages/speech/history-list/index",
      query: {
        tag_id: this.data.speechData.offline[index].tag_id,
        job_id: this.data.examTabs.active,
      },
    })
  },
  toHistoy(e) {
    console.log(e)
    ROUTER.navigateTo({
      path: "/pages/speech/period-list/index",
      query: {
        tag_id: this.data.speechData.online[0].tag_id,
        job_id: this.data.examTabs.active,
      },
    })
  },
  // 处理关闭校区微信客服
  closeWeichatCustomerService() {
    this.setData({
      weichatShow: false,
    })
  },
  // 处理打开校区微信客服
  openWeichatCustomerService() {
    let configList = APP?.globalData?.serverConfig?.customer_list
    const obj = BASE_CACHE.getBaseCache() || {}
    let province = obj.province?.key
    let customerService = configList.find(
      (item) => item.province_key == province
    ).customer_config
    if (customerService.type == "popup") {
      this.setData({
        weichatCustomerService: customerService,
        weichatShow: true,
      })
      return
    }
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: {
        url: customerService.url,
      },
    })
  },
  onReachBottom() {},
  getPageShareParams() {
    let query = { ...PAGE_OPTIONS, examActive: this.data.examTabs.active }
    return APP.createShareParams({
      title: this.data?.speechData?.share_info?.title || "",
      imageUrl: this.data?.speechData?.share_info?.image || "",
      path: "/pages/speech/list/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
