const EVENT_MAP = {
  offWaiting: "offWaiting",
  onCanplay: "onCanplay",
  onEnded: "onEnded",
  onError: "onError",
  onPause: "onPause",
  onPlay: "onPlay",
  onSeeked: "onSeeked",
  onSeeking: "onSeeking",
  onStop: "onStop",
  onTimeUpdate: "onTimeUpdate",
  onWaiting: "onWaiting",
}

class Player {
  constructor() {
    this._state = {
      src: null,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      // volume: wx.getStorageSync("playerVolume") || 1,
      volume: 1,
    }

    try {
      console.log("初始化Player constructor")
      this.audioContext = wx.createInnerAudioContext({
        // useWebAudioImplement: true,
      })
      this.audioContext.obeyMuteSwitch = false
      this.audioContext.volume = this._state.volume
      this.audioContext.autoplay = false

      this.audioContext.onPlay(function () {
        console.log("AudioPlayer内部固定监听 onPlay")
      })

      this.audioContext.onStop(function () {
        console.log("AudioPlayer内部固定监听 onStop")
      })
      this.audioContext.onError((e) => {
        this.audioContext.src = "1"
        console.log("AudioPlayer内部固定监听 onError")
      })
      this.audioContext.onEnded(function () {
        console.log("AudioPlayer内部固定监听 onEnded")
      })
      this.audioContext.onPause(function () {
        console.log("AudioPlayer内部固定监听 onPause")
      })

      this.audioContext.onWaiting(function () {
        console.log("AudioPlayer内部固定监听 onWaiting")
      })
    } catch (error) {
      console.error("音频上下文创建失败:", error)
      throw new Error("无法初始化音频播放器")
    }

    this.events = new Map()
    this._initAudioEvents()
    this._initStateUpdates()
  }

  _initAudioEvents() {
    Object.entries(EVENT_MAP).forEach(([eventName, methodName]) => {
      const audioContext = this.audioContext
      if (!audioContext) return

      if (typeof audioContext[methodName] !== "function") {
        console.error(
          `无法监听 ${eventName}: audioContext.${methodName} 不是函数`
        )
        return
      }

      try {
        audioContext[methodName]((res) => {
          this._handleAudioEvent(eventName, res)
        })
      } catch (error) {
        console.error(`监听 ${eventName} 事件失败:`, error)
      }
    })
  }

  _initStateUpdates() {
    let lastUpdate = 0
    this.audioContext.onTimeUpdate(() => {
      const now = Date.now()
      if (now - lastUpdate > 500) {
        this._updateTimeState()
        lastUpdate = now
      }
    })
  }

  _updateTimeState() {
    this._state.currentTime = this.audioContext.currentTime
    this._state.duration = this.audioContext.duration || 0
    this.emit("timeUpdate", this._state)
  }

  _handleAudioEvent(eventName, eventData) {
    switch (eventName) {
      case "onPlay":
        this._state.isPlaying = true
        break
      case "onPause":
      case "onStop":
      case "onEnded":
        this._state.isPlaying = false
        break
      case "onCanplay":
        this._state.duration = this.audioContext.duration
        break
    }
    this.emit(eventName, eventData)
  }

  setSrc(src) {
    this._state.src = src
    if (this.audioContext.src !== src) {
      console.log("触发修改src", src)
      this.audioContext.src = src
      this.audioContext.volume = 1
      this.audioContext.seek(0)
    }
    this.emit("sourceChanged", { src })
  }

  play(src, callback, errorCallback) {
    if (src) {
      this.setSrc(src)
    }

    if (!this._state.src) {
      this.emit("error", { code: "NO_SOURCE", message: "未设置音频源" })
      return
    }

    if (this.audioContext.src !== this._state.src) {
      console.log("触发修改src", this.audioContext.src, this._state.src)
      this.audioContext.src = this._state.src
    }

    if (typeof callback === "function") {
      const onceCallback = (res) => {
        callback(res)
        this.off("onEnded", onceCallback)
      }
      this.on("onEnded", onceCallback)
    }
    if (typeof errorCallback === "function") {
      const onceCallback = (res) => {
        errorCallback(res)
        this.off("onError", onceCallback)
      }
      this.on("onError", onceCallback)
    }

    console.log("AudioPlayer 播放")
    this.audioContext.play()
  }

  pause() {
    this.audioContext.pause()
  }

  stop() {
    this.audioContext.stop()
    this.audioContext.src = ""
  }

  setVolume(vol) {
    const volume = Math.min(1, Math.max(0, vol))
    this.audioContext.volume = volume
    this._state.volume = volume
    wx.setStorageSync("playerVolume", volume)
  }

  seek(position) {
    if (typeof position !== "number" || position < 0) {
      this.emit("error", {
        code: "INVALID_SEEK_POSITION",
        message: "无效的播放位置",
      })
      return
    }
    const currentDuration = this._state.duration
    if (position > currentDuration) {
      position = currentDuration
    }
    this.audioContext.seek(position)
    this._state.currentTime = position
  }

  removeAllListeners() {
    this.events.clear()
  }

  on(event, callback, unique = false) {
    // console.log(
    //   "监听绑定事件 AudioPlayer",
    //   this.events,
    //   event,
    //   this.events.has(event)
    // )

    if (!this.events.has(event)) {
      this.events.set(event, new Set())
    }
    if (unique) {
      const callbacks = this.events.get(event)
      callbacks.clear()
    }

    this.events.get(event).add(callback)
  }

  off(event, callback) {
    if (this.events.has(event)) {
      const callbacks = this.events.get(event)
      const success = callbacks.delete(callback)

      if (!success) {
        console.warn(`Callback for event '${event}' not found.`)
      }
    }
  }

  emit(event, ...args) {
    if (this.events.has(event)) {
      this.events.get(event).forEach((cb) => cb(...args))
    }
  }

  getState() {
    return { ...this._state }
  }

  destroy() {
    try {
      console.log("销毁音频", this.audioContext)
      this.audioContext?.destroy?.()
      this.events.clear()
      // wx.setStorageSync("playerVolume", this._state.volume)
    } catch (error) {
      console.log(error)
    }
  }
}

module.exports = Player
