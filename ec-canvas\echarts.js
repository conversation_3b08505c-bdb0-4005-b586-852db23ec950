!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var w=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},b=new function(){this.browser=new w,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(b.wxa=!0,b.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?b.worker=!0:!b.hasGlobalWindow||"Deno"in window?(b.node=!0,b.svgSupported=!0):(J=navigator.userAgent,ae=(Gt=b).browser,rt=J.match(/Firefox\/([\d.]+)/),U=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(ae.firefox=!0,ae.version=rt[1]),U&&(ae.ie=!0,ae.version=U[1]),Q&&(ae.edge=!0,ae.version=Q[1],ae.newEdge=18<+Q[1].split(".")[0]),J&&(ae.weChat=!0),Gt.svgSupported="undefined"!=typeof SVGRect,Gt.touchEventsSupported="ontouchstart"in window&&!ae.ie&&!ae.edge,Gt.pointerEventsSupported="onpointerdown"in window&&(ae.edge||ae.ie&&11<=+ae.version),Gt.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Gt.transform3dSupported=(ae.ie&&"transition"in rt||ae.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Gt.transformSupported=Gt.transform3dSupported||ae.ie&&9<=+ae.version);var j="12px sans-serif";var T,C,D=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),H={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(T||(n=H.createCanvas(),T=n&&n.getContext("2d")),T)return C!==e&&(C=T.font=e||j),T.measureText(t);t=t||"",e=e||j;var n=/((?:\d+)?\.?\d*)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=D[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function A(t){for(var e in H)t[e]&&(H[e]=t[e])}var P=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),W=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),G=Object.prototype.toString,U=Array.prototype,Y=U.forEach,Z=U.filter,K=U.slice,$=U.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function S(t){if(null==t||"object"!=typeof t)return t;var e=t,n=G.call(t);if("[object Array]"===n){if(!Pt(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=S(t[i])}else if(W[n]){if(!Pt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!P[n]&&!Pt(t)&&!vt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=S(t[a]));return e}function d(t,e,n){if(!R(e)||!R(t))return n?S(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!R(o=e[i])||!R(r)||F(o)||F(r)||vt(o)||vt(r)||yt(o)||yt(r)||Pt(o)||Pt(r)?!n&&i in t||(t[i]=S(e[i])):d(r,o,n))}return t}function L(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function B(t,e,n){for(var i=ct(e),r=0,o=i.length;r<o;r++){var a=i[r];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var rt=H.createCanvas;function I(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else B(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===Y)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function z(t,e,n){if(!t)return[];if(!e)return Tt(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return Tt(t);if(t.filter&&t.filter===Z)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function ht(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function ct(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var pt=J&&k(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(K.call(arguments)))}};function dt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(K.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===G.call(t)}function k(t){return"function"==typeof t}function V(t){return"string"==typeof t}function ft(t){return"[object String]"===G.call(t)}function gt(t){return"number"==typeof t}function R(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function yt(t){return!!P[G.call(t)]}function mt(t){return!!W[G.call(t)]}function vt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function _t(t){return null!=t.colorStops}function xt(t){return null!=t.image}function wt(t){return"[object RegExp]"===G.call(t)}function bt(t){return t!=t}function St(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function E(t,e){return null!=t?t:e}function Mt(t,e,n){return null!=t?t:null!=e?e:n}function Tt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return K.apply(t,e)}function Ct(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function It(t,e){if(!t)throw new Error(e)}function kt(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var Dt="__ec_primitive__";function At(t){t[Dt]=!0}function Pt(t){return t[Dt]}Ot.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},Ot.prototype.has=function(t){return this.data.hasOwnProperty(t)},Ot.prototype.get=function(t){return this.data[t]},Ot.prototype.set=function(t,e){return this.data[t]=e,this},Ot.prototype.keys=function(){return ct(this.data)},Ot.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var Lt=Ot;function Ot(){this.data={}}var Rt="function"==typeof Map;Nt.prototype.hasKey=function(t){return this.data.has(t)},Nt.prototype.get=function(t){return this.data.get(t)},Nt.prototype.set=function(t,e){return this.data.set(t,e),e},Nt.prototype.each=function(n,i){this.data.forEach(function(t,e){n.call(i,t,e)})},Nt.prototype.keys=function(){var t=this.data.keys();return Rt?Array.from(t):t},Nt.prototype.removeKey=function(t){this.data.delete(t)};var Et=Nt;function Nt(t){var n=F(t),i=(this.data=new(Rt?Map:Lt),this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Nt?t.each(e):t&&O(t,e)}function N(t){return new Et(t)}function Bt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function zt(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&L(t,e),t}function Ft(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Vt(t,e){return t.hasOwnProperty(e)}function Ht(){}var Wt=180/Math.PI,Gt=Object.freeze({__proto__:null,HashMap:Et,RADIAN_TO_DEGREE:Wt,assert:It,bind:pt,clone:S,concatArray:Bt,createCanvas:rt,createHashMap:N,createObject:zt,curry:dt,defaults:B,disableUserSelect:Ft,each:O,eqNaN:bt,extend:L,filter:ut,find:ht,guid:nt,hasOwn:Vt,indexOf:I,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:yt,isDom:vt,isFunction:k,isGradientObject:_t,isImagePatternObject:xt,isNumber:gt,isObject:R,isPrimitive:Pt,isRegExp:wt,isString:V,isStringSafe:ft,isTypedArray:mt,keys:ct,logError:it,map:z,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Ht,normalizeCssArray:Ct,reduce:lt,retrieve:St,retrieve2:E,retrieve3:Mt,setAsPrimitive:At,slice:Tt,trim:kt});function Ut(t,e){return[t=null==t?0:t,e=null==e?0:e]}function Xt(t){return[t[0],t[1]]}function qt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Yt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Zt(t){return Math.sqrt(jt(t))}function jt(t){return t[0]*t[0]+t[1]*t[1]}function Kt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function $t(t,e){var n=Zt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Qt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Jt=Qt;function te(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var ee=te;function ne(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ie(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function re(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function oe(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var ae=Object.freeze({__proto__:null,add:qt,applyTransform:ie,clone:Xt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Ut,dist:Jt,distSquare:ee,distance:Qt,distanceSquare:te,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Zt,lenSquare:jt,length:Zt,lengthSquare:jt,lerp:ne,max:oe,min:re,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:$t,scale:Kt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Yt}),se=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},le=(ue.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new se(e,t),"dragstart",t.event))},ue.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new se(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new se(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new se(i,t),"dragenter",t.event)},ue.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new se(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new se(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},ue);function ue(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}ce.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},ce.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},ce.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},ce.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},ce.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var he=ce;function ce(t){t&&(this._$eventProcessor=t)}var pe=Math.log(2);function de(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/pe),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*de(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function fe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=de(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*de(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var ge="___zrEVENTSAVED";function ye(t,e,n,i,r){if(e.getBoundingClientRect&&b.domSupported&&!me(e)){var o=e[ge]||(e[ge]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?fe(s,a):fe(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),1}}function me(t){return"CANVAS"===t.nodeName.toUpperCase()}var ve=/([&<>"'])/g,_e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function xe(t){return null==t?"":(t+"").replace(ve,function(t,e){return _e[e]})}var we=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,be=[],Se=b.browser.firefox&&+b.browser.version.split(".")[0]<39;function Me(t,e,n,i){return n=n||{},i?Te(t,e,n):Se&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Te(t,e,n),n}function Te(t,e,n){if(b.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(me(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(ye(be,t,r,e))return n.zrX=be[0],void(n.zrY=be[1])}n.zrX=n.zrY=0}function Ce(t){return t||window.event}function Ie(t,e,n){var i;return null==(e=Ce(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&Me(t,i,e,n):(Me(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&we.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var ke=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},De=(Ae.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},Ae.prototype.clear=function(){return this._track.length=0,this},Ae.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Me(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},Ae.prototype._recognize=function(t){for(var e in Le)if(Le.hasOwnProperty(e)){e=Le[e](this._track,t);if(e)return e}},Ae);function Ae(){this._track=[]}function Pe(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Le={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=Pe(n)/Pe(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Oe(){return[1,0,0,1,0,0]}function Re(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Ee(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Ne(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Be(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ze(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],e=e[5],u=Math.sin(n),n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function Fe(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Ve(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var He=Object.freeze({__proto__:null,clone:function(t){var e=Oe();return Ee(e,t),e},copy:Ee,create:Oe,identity:Re,invert:Ve,mul:Ne,rotate:ze,scale:Fe,translate:Be}),M=(e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,e){return this.x=t,this.y=e,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},e.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,e,n){t.x=e,t.y=n},e.copy=function(t,e){t.x=e.x,t.y=e.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,e){return t.x*e.x+t.y*e.y},e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},e);function e(t,e){this.x=t||0,this.y=e||0}var We=Math.min,Ge=Math.max,Ue=new M,Xe=new M,qe=new M,Ye=new M,Ze=new M,je=new M,X=(Ke.prototype.union=function(t){var e=We(t.x,this.x),n=We(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ge(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},Ke.prototype.applyTransform=function(t){Ke.applyTransform(this,this,t)},Ke.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Oe();return Be(i,i,[-this.x,-this.y]),Fe(i,i,[e,n]),Be(i,i,[t.x,t.y]),i},Ke.prototype.intersect=function(t,e){if(!t)return!1;t instanceof Ke||(t=Ke.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(je,-r,0):M.set(je,o,0)):l<n&&(n=l,r<o?M.set(Ze,r,0):M.set(Ze,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(je,0,-a):M.set(je,0,s)):l<n&&(n=l,a<s?M.set(Ze,0,a):M.set(Ze,0,-s))),e&&M.copy(e,m?Ze:je),m},Ke.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},Ke.prototype.clone=function(){return new Ke(this.x,this.y,this.width,this.height)},Ke.prototype.copy=function(t){Ke.copy(this,t)},Ke.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},Ke.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},Ke.prototype.isZero=function(){return 0===this.width||0===this.height},Ke.create=function(t){return new Ke(t.x,t.y,t.width,t.height)},Ke.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},Ke.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(Ue.x=qe.x=e.x,Ue.y=Ye.y=e.y,Xe.x=Ye.x=e.x+e.width,Xe.y=qe.y=e.y+e.height,Ue.transform(n),Ye.transform(n),Xe.transform(n),qe.transform(n),t.x=We(Ue.x,Xe.x,qe.x,Ye.x),t.y=We(Ue.y,Xe.y,qe.y,Ye.y),o=Ge(Ue.x,Xe.x,qe.x,Ye.x),a=Ge(Ue.y,Xe.y,qe.y,Ye.y),t.width=o-t.x,t.height=a-t.y):t!==e&&Ke.copy(t,e)},Ke);function Ke(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var $e="silent";function Qe(){ke(this.event)}u(en,Je=he),en.prototype.dispose=function(){},en.prototype.setCursor=function(){};var Je,tn=en;function en(){var t=null!==Je&&Je.apply(this,arguments)||this;return t.handler=null,t}var nn,rn=function(t,e){this.x=t,this.y=e},on=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],an=new X(0,0,0,0),sn=(u(ln,nn=he),ln.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(on,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},ln.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=hn(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new rn(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},ln.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},ln.prototype.resize=function(){this._hovered=new rn(0,0)},ln.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},ln.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},ln.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},ln.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Qe};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},ln.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new rn(t,e);if(un(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new X(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(an.copy(h.getBoundingRect()),h.transform&&an.applyTransform(h.transform),an.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(un(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},ln.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new De);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new rn).target=i.target,this.dispatchToElement(n,e,i.event))},ln);function ln(t,e,n,i,r){var o=nn.call(this)||this;return o._hovered=new rn(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new tn,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new le(o),o}function un(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);a=i.__hostTarget,i=a||i.parent}return!r||$e}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),s!==$e)){e.target=a;break}}}function hn(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){sn.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=hn(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<Jt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var cn=32,pn=7;function dn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function fn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function gn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function yn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function mn(A,P){var L,O,R=pn,E=0,N=[];function e(t){var e=L[t],n=O[t],i=L[t+1],r=O[t+1],t=(O[t]=n+r,t===E-3&&(L[t+1]=L[t+2],O[t+1]=O[t+2]),E--,yn(A[i],A,e,n,0,P));if(e+=t,0!=(n-=t)&&0!==(r=gn(A[e+n-1],A,i,r,r-1,P)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)N[l]=A[o+l];var u=0,h=t,c=o;if(A[c++]=A[h++],0==--s)for(l=0;l<a;l++)A[c+l]=N[u+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=N[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(P(A[h],N[u])<0){if(A[c++]=A[h++],d++,(p=0)==--s){f=!0;break}}else if(A[c++]=N[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=yn(A[h],N,u,a,0,P))){for(l=0;l<p;l++)A[c+l]=N[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(A[c++]=A[h++],0==--s){f=!0;break}if(0!==(d=gn(N[u],A,h,s,0,P))){for(l=0;l<d;l++)A[c+l]=A[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(A[c++]=N[u++],1==--a){f=!0;break}}while(g--,pn<=p||pn<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=N[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=N[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)N[x]=A[v+x];var w=y+m-1,b=_-1,S=v+_-1,M=0,T=0;if(A[S--]=A[w--],0==--m)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=N[x];else if(1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=N[b]}else{for(var C=R;;){var I=0,k=0,D=!1;do{if(P(N[b],A[w])<0){if(A[S--]=A[w--],I++,(k=0)==--m){D=!0;break}}else if(A[S--]=N[b--],k++,I=0,1==--_){D=!0;break}}while((I|k)<C);if(D)break;do{if(0!==(I=m-yn(N[b],A,y,m,m-1,P))){for(m-=I,T=(S-=I)+1,M=(w-=I)+1,x=I-1;0<=x;x--)A[T+x]=A[M+x];if(0===m){D=!0;break}}if(A[S--]=N[b--],1==--_){D=!0;break}if(0!==(k=_-gn(A[w],N,0,_,_-1,P))){for(_-=k,T=(S-=k)+1,M=(b-=k)+1,x=0;x<k;x++)A[T+x]=N[M+x];if(_<=1){D=!0;break}}if(A[S--]=A[w--],0==--m){D=!0;break}}while(C--,pn<=I||pn<=k);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=N[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=N[x]}}}}return L=[],O=[],{mergeRuns:function(){for(;1<E;){var t=E-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<E;){var t=E-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){L[E]=t,O[E]=e,E+=1}}}function vn(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<cn)fn(t,n,i,n+(o=dn(t,n,i,e)),e);else{var a,s=mn(t,e),l=function(t){for(var e=0;cn<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=dn(t,n,i,e))<l&&(fn(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var _n=1,xn=4,wn=!1;function bn(){wn||(wn=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Sn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Tn.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Tn.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Tn.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,vn(n,Sn)},Tn.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=_n),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(bn(),i.z=0),isNaN(i.z2)&&(bn(),i.z2=0),isNaN(i.zlevel)&&(bn(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},Tn.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Tn.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=I(this._roots,t);0<=i&&this._roots.splice(i,1)}},Tn.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Tn.prototype.getRoots=function(){return this._roots},Tn.prototype.dispose=function(){this._displayList=null,this._roots=null};var Mn=Tn;function Tn(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Sn}var Cn=b.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},In={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-In.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*In.bounceIn(2*t):.5*In.bounceOut(2*t-1)+.5}},kn=Math.pow,Dn=Math.sqrt,An=1e-8,Pn=Dn(3),Ln=1/3,On=Ut(),Rn=Ut(),En=Ut();function Nn(t){return-An<t&&t<An}function Bn(t){return An<t||t<-An}function zn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Fn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Vn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return Nn(r)&&Nn(l)?Nn(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):Nn(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=Dn(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-kn(-t,Ln):kn(t,Ln))+(e=e<0?-kn(-e,Ln):kn(e,Ln))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*Dn(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=Dn(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+Pn*Math.sin(e)))/(3*i),r=(-n+l*(t-Pn*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function Hn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return Nn(i)?Bn(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):Nn(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=Dn(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function Wn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function Gn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;On[0]=l,On[1]=u;for(var m=0;m<1;m+=.05)Rn[0]=zn(t,n,r,a,m),Rn[1]=zn(e,i,o,s,m),(d=ee(On,Rn))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,Rn[0]=zn(t,n,r,a,f=c-g),Rn[1]=zn(e,i,o,s,f),d=ee(Rn,On),0<=f&&d<y?(c=f,y=d):(En[0]=zn(t,n,r,a,p),En[1]=zn(e,i,o,s,p),f=ee(En,On),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=zn(t,n,r,a,c),h[1]=zn(e,i,o,s,c)),Dn(y)}function Un(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Xn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function qn(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function Yn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function Zn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;On[0]=a,On[1]=s;for(var p=0;p<1;p+=.05)Rn[0]=Un(t,n,r,p),Rn[1]=Un(e,i,o,p),(y=ee(On,Rn))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(Rn[0]=Un(t,n,r,f),Rn[1]=Un(e,i,o,f),ee(Rn,On));0<=f&&y<c?(u=f,c=y):(En[0]=Un(t,n,r,g),En[1]=Un(e,i,o,g),f=ee(En,On),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=Un(t,n,r,u),l[1]=Un(e,i,o,u)),Dn(c)}var jn=/cubic-bezier\(([0-9,\.e ]+)\)/;function Kn(t){t=t&&jn.exec(t);if(t){var e,t=t[1].split(","),n=+kt(t[0]),i=+kt(t[1]),r=+kt(t[2]),o=+kt(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:Vn(0,n,r,1,t,e)&&zn(0,i,o,1,e[0])}}}Qn.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Qn.prototype.pause=function(){this._paused=!0},Qn.prototype.resume=function(){this._paused=!1},Qn.prototype.setEasing=function(t){this.easing=t,this.easingFunc=k(t)?t:In[t]||Kn(t)};var $n=Qn;function Qn(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ht,this.ondestroy=t.ondestroy||Ht,this.onrestart=t.onrestart||Ht,t.easing&&this.setEasing(t.easing)}var Jn=function(t){this.value=t},ti=(ei.prototype.insert=function(t){t=new Jn(t);return this.insertEntry(t),t},ei.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},ei.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},ei.prototype.len=function(){return this._len},ei.prototype.clear=function(){this.head=this.tail=null,this._len=0},ei);function ei(){this._len=0}ii.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Jn(e),n.key=t,r.insertEntry(n),o[t]=n),a},ii.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},ii.prototype.clear=function(){this._list.clear(),this._map={}},ii.prototype.len=function(){return this._list.len()};var ni=ii;function ii(t){this._list=new ti,this._maxSize=10,this._map={},this._maxSize=t}var ri={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function oi(t){return(t=Math.round(t))<0?0:255<t?255:t}function ai(t){return t<0?0:1<t?1:t}function si(t){return t.length&&"%"===t.charAt(t.length-1)?oi(parseFloat(t)/100*255):oi(parseInt(t,10))}function li(t){return t.length&&"%"===t.charAt(t.length-1)?ai(parseFloat(t)/100):ai(parseFloat(t))}function ui(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function hi(t,e,n){return t+(e-t)*n}function ci(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function pi(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var di=new ni(20),fi=null;function gi(t,e){fi&&pi(fi,e),fi=di.put(t,fi||e.slice())}function yi(t,e){if(t){e=e||[];var n=di.get(t);if(n)return pi(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in ri)return pi(e,ri[n]),gi(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(ci(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),gi(t,e),e):void ci(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(ci(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),gi(t,e),e):void ci(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?ci(e,+a[0],+a[1],+a[2],1):ci(e,0,0,0,1);s=li(a.pop());case"rgb":return 3<=a.length?(ci(e,si(a[0]),si(a[1]),si(a[2]),3===a.length?s:li(a[3])),gi(t,e),e):void ci(e,0,0,0,1);case"hsla":return 4!==a.length?void ci(e,0,0,0,1):(a[3]=li(a[3]),mi(a,e),gi(t,e),e);case"hsl":return 3!==a.length?void ci(e,0,0,0,1):(mi(a,e),gi(t,e),e);default:return}}ci(e,0,0,0,1)}}function mi(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=li(t[1]),r=li(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return ci(e=e||[],oi(255*ui(r,i,n+1/3)),oi(255*ui(r,i,n)),oi(255*ui(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function vi(t,e){var n=yi(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return bi(n,4===n.length?"rgba":"rgb")}}function _i(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=oi(hi(r[0],e[0],o=t-i)),n[1]=oi(hi(r[1],e[1],o)),n[2]=oi(hi(r[2],e[2],o)),n[3]=ai(hi(r[3],e[3],o)),n}var xi=_i;function wi(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=yi(e[i]),e=yi(e[r]),a=bi([oi(hi(a[0],e[0],o=t-i)),oi(hi(a[1],e[1],o)),oi(hi(a[2],e[2],o)),ai(hi(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var n=wi;function bi(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function Si(t,e){t=yi(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var Mi=new ni(100);function Ti(t){var e;return V(t)?((e=Mi.get(t))||(e=vi(t,-.1),Mi.put(t,e)),e):_t(t)?((e=L({},t)).colorStops=z(t.colorStops,function(t){return{offset:t.offset,color:vi(t.color,-.1)}}),e):t}xi=Object.freeze({__proto__:null,fastLerp:_i,fastMapToColor:xi,lerp:wi,lift:vi,liftColor:Ti,lum:Si,mapToColor:n,modifyAlpha:function(t,e){if((t=yi(t))&&null!=e)return t[3]=ai(e),bi(t,"rgba")},modifyHSL:function(t,e,n,i){var r=yi(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=li(n)),null!=i&&(r[2]=li(i)),bi(mi(r),"rgba")},parse:yi,random:function(){return bi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:bi,toHex:function(t){if(t=yi(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});b.hasGlobalWindow&&k(window.btoa);var Ci=Array.prototype.slice;function Ii(t,e,n){return(e-t)*n+t}function ki(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Ii(e[o],n[o],i);return t}function Di(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Ai(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Pi(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ci.call(t[i]));return n}return Ci.call(t)}return t}function Li(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Oi(t){return 4===t||5===t}function Ri(t){return 1===t||2===t}var Ei=[0,0,0,0],Ni=(Bi.prototype.isFinished=function(){return this._finished},Bi.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Bi.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Bi.prototype.getAdditiveTrack=function(){return this._additiveTrack},Bi.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!gt(e[0])||2==i&&!gt(e[0][0]))&&(a=!0):gt(e)&&!bt(e)?s=0:V(e)?isNaN(+e)?(i=yi(e))&&(l=i,s=3):s=0:_t(e)&&((u=L({},l)).colorStops=z(e.colorStops,function(t){return{offset:t.offset,color:yi(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=k(n)?n:In[n]||Kn(n)),r.push(u),u},Bi.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=Ri(i),l=Oi(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Ci.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=b=w=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,w=d.length,b=h.length,S=b<w?h:d,h=Math.min(w,b),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=Di([],n[u].value,C,-1):Ri(i)&&(n[u].additiveValue=(1===i?Di:Ai)([],n[u].value,C,-1))}},Bi.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?Ei:t[c],(Ri(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:Ri(l)?(1===l?ki:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Ii(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):Oi(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Ii(d.x,o.x,r),y:Ii(d.y,o.y,r),colorStops:z(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:Ii(t.offset,e.offset,r),color:Li(ki([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=Ii(d.x2,o.x2,r),t[c].y2=Ii(d.y2,o.y2,r)):t[c].r=Ii(d.r,o.r,r)):p?(ki(f,n[s],i[s],r),a||(t[c]=Li(f))):(l=Ii(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},Bi.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(yi(t[n],Ei),Di(Ei,Ei,i,1),t[n]=Li(Ei)):1===e?Di(t[n],t[n],i,1):2===e&&Ai(t[n],t[n],i,1)},Bi);function Bi(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}i.prototype.getMaxTime=function(){return this._maxTime},i.prototype.getDelay=function(){return this._delay},i.prototype.getLoop=function(){return this._loop},i.prototype.getTarget=function(){return this._target},i.prototype.changeTarget=function(t){this._target=t},i.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,ct(e),n)},i.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new Ni(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&Li(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,Pi(u),i),this._trackKeys.push(a)}l.addKeyframe(t,Pi(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},i.prototype.pause=function(){this._clip.pause(),this._paused=!0},i.prototype.resume=function(){this._clip.resume(),this._paused=!1},i.prototype.isPaused=function(){return!!this._paused},i.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},i.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},i.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},i.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},i.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},i.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new $n({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},i.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},i.prototype.delay=function(t){return this._delay=t,this},i.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},i.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},i.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},i.prototype.getClip=function(){return this._clip},i.prototype.getTrack=function(t){return this._tracks[t]},i.prototype.getTracks=function(){var e=this;return z(this._trackKeys,function(t){return e._tracks[t]})},i.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},i.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=Pi(o.rawValue))}}},i.prototype.__changeFinalValue=function(t,e){e=e||ct(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var zi=i;function i(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function Fi(){return(new Date).getTime()}u(Wi,Vi=he),Wi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Wi.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},Wi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Wi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Wi.prototype.update=function(t){for(var e=Fi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},Wi.prototype._startLoop=function(){var e=this;this._running=!0,Cn(function t(){e._running&&(Cn(t),e._paused||e.update())})},Wi.prototype.start=function(){this._running||(this._time=Fi(),this._pausedTime=0,this._startLoop())},Wi.prototype.stop=function(){this._running=!1},Wi.prototype.pause=function(){this._paused||(this._pauseStart=Fi(),this._paused=!0)},Wi.prototype.resume=function(){this._paused&&(this._pausedTime+=Fi()-this._pauseStart,this._paused=!1)},Wi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Wi.prototype.isFinished=function(){return null==this._head},Wi.prototype.animate=function(t,e){e=e||{},this.start();t=new zi(t,e.loop);return this.addAnimator(t),t};var Vi,Hi=Wi;function Wi(t){var e=Vi.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var Gi,Ui=b.domSupported,Xi=(Gi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:n=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:z(n,function(t){var e=t.replace("mouse","pointer");return Gi.hasOwnProperty(e)?e:t})}),qi=["mousemove","mouseup"],Yi=["pointermove","pointerup"],Zi=!1;function ji(t){t=t.pointerType;return"pen"===t||"touch"===t}function Ki(t){t&&(t.zrByTouch=!0)}function $i(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Qi=function(t,e){this.stopPropagation=Ht,this.stopImmediatePropagation=Ht,this.preventDefault=Ht,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Ji={mousedown:function(t){t=Ie(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ie(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ie(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){$i(this,(t=Ie(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Zi=!0,t=Ie(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Zi||(t=Ie(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Ki(t=Ie(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ji.mousemove.call(this,t),Ji.mousedown.call(this,t)},touchmove:function(t){Ki(t=Ie(this.dom,t)),this.handler.processGesture(t,"change"),Ji.mousemove.call(this,t)},touchend:function(t){Ki(t=Ie(this.dom,t)),this.handler.processGesture(t,"end"),Ji.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Ji.click.call(this,t)},pointerdown:function(t){Ji.mousedown.call(this,t)},pointermove:function(t){ji(t)||Ji.mousemove.call(this,t)},pointerup:function(t){Ji.mouseup.call(this,t)},pointerout:function(t){ji(t)||Ji.mouseout.call(this,t)}},tr=(O(["click","dblclick","contextmenu"],function(e){Ji[e]=function(t){t=Ie(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){ji(t)||tr.mousemove.call(this,t)},pointerup:function(t){tr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function er(i,r){var o=r.domHandlers;b.pointerEventsSupported?O(Xi.pointer,function(e){ir(r,e,function(t){o[e].call(i,t)})}):(b.touchEventsSupported&&O(Xi.touch,function(n){ir(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(Xi.mouse,function(e){ir(r,e,function(t){t=Ce(t),r.touching||o[e].call(i,t)})}))}function nr(i,r){function t(n){ir(r,n,function(t){var e;t=Ce(t),$i(i,t.target)||(e=t,t=Ie(i.dom,new Qi(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}b.pointerEventsSupported?O(Yi,t):b.touchEventsSupported||O(qi,t)}function ir(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function rr(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var or,ar=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},sr=(u(lr,or=he),lr.prototype.dispose=function(){rr(this._localHandlerScope),Ui&&rr(this._globalHandlerScope)},lr.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},lr.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Ui&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?nr(this,e):rr(e))},lr);function lr(t,e){var n=or.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new ar(t,Ji),Ui&&(n._globalHandlerScope=new ar(document,tr)),er(n,n._localHandlerScope),n}var n=1,ur=n=b.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):n,hr="#333",cr="#ccc",pr=Re;function dr(t){return 5e-5<t||t<-5e-5}var fr=[],gr=[],yr=Oe(),mr=Math.abs,vr=(_r.prototype.getLocalTransform=function(t){return _r.getLocalTransform(this,t)},_r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},_r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},_r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},_r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},_r.prototype.needLocalTransform=function(){return dr(this.rotation)||dr(this.x)||dr(this.y)||dr(this.scaleX-1)||dr(this.scaleY-1)||dr(this.skewX)||dr(this.skewY)},_r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Oe(),e?this.getLocalTransform(n):pr(n),t&&(e?Ne(n,t,n):Ee(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(pr(n),this.invTransform=null)},_r.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(fr),n=((fr[1]-(n=fr[1]<0?-1:1))*i+n)/fr[1]||0,t[0]*=i=((fr[0]-(e=fr[0]<0?-1:1))*i+e)/fr[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Oe(),Ve(this.invTransform,t)},_r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},_r.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},_r.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||Oe(),Ne(gr,e.invTransform,t),t=gr),e=this.originX,n=this.originY,(e||n)&&(yr[4]=e,yr[5]=n,Ne(gr,t,yr),gr[4]-=e,gr[5]-=n,t=gr),this.setLocalTransform(t))},_r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},_r.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&ie(t,t,e),t},_r.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&ie(t,t,e),t},_r.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<mr(t[0]-1)&&1e-10<mr(t[3]-1)?Math.sqrt(mr(t[0]*t[3]-t[2]*t[1])):1},_r.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<xr.length;i++){var r=xr[i];e[r]=n[r]}},_r.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&ze(e,e,l),e[4]+=n+u,e[5]+=i+h,e},_r.initDefaultProps=((n=_r.prototype).scaleX=n.scaleY=n.globalScaleRatio=1,void(n.x=n.y=n.originX=n.originY=n.skewX=n.skewY=n.rotation=n.anchorX=n.anchorY=0)),_r);function _r(){}var xr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var wr={};function br(t,e){var n=wr[e=e||j],i=(n=n||(wr[e]=new ni(500))).get(t);return null==i&&(i=H.measureText(t,e).width,n.put(t,i)),i}function Sr(t,e,n,i){t=br(t,e),e=Ir(e),n=Tr(0,t,n),i=Cr(0,e,i);return new X(n,i,t,e)}function Mr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return Sr(r[0],e,n,i);for(var o=new X(0,0,0,0),a=0;a<r.length;a++){var s=Sr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Tr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Cr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Ir(t){return br("国",t)}function kr(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Dr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=kr(i[0],n.width),u+=kr(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Ar,Pr="__zr_normal__",Lr=xr.concat(["ignore"]),Or=lt(xr,function(t,e){return t[e]=!0,t},{ignore:!1}),Rr={},Er=new X(0,0,0,0),n=(r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Er,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Rr,t,u):Dr(Rr,t,u),e.x=Rr.x,e.y=Rr.y,n=Rr.align,i=Rr.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=kr(o[0],u.width),kr(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=_n,r&&c.dirtyStyle(!0))},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?cr:hr},r.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&yi(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,bi(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},L(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(R(t))for(var n=ct(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==Pr||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},r.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Lr)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},r.prototype.hasState=function(){return 0<this.currentStates.length},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Pr,!1,t)},r.prototype.useState=function(t,e,n,i){var r=t===Pr,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=I(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~_n),a;it("State "+t+" not exists.")}}},r.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~_n)}}else this.clearStates()},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e,t=I(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=I(i,t),r=0<=I(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];L(n,r),r.textConfig&&L(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},r.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=L({},(i?this:n).textConfig),L(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<Lr.length;u++){var h=Lr[u],c=r&&Or[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},r.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new vr,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),L(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=_n;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new zi(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},r.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=I(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},r.prototype.animateTo=function(t,e,n){Br(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){Br(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var r=Br(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=((Ar=r.prototype).type="element",Ar.name="",Ar.ignore=Ar.silent=Ar.isGroup=Ar.draggable=Ar.dragging=Ar.ignoreClip=Ar.__inHover=!1,Ar.__dirty=_n,void(Object.defineProperty&&(Nr("position","_legacyPos","x","y"),Nr("scale","_legacyScale","scaleX","scaleY"),Nr("origin","_legacyOrigin","originX","originY")))),r);function r(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Nr(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Ar,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function Br(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=ct(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!R(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!R(_)||st(_)||_t(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=I(g,S),g.splice(w,1));o.force||(y=ut(y,function(t){return!Vr(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(b=0;b<x;b++){v=y[b];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(b=0;b<x;b++){v=y[b];C[v]=Pi(i[v]),Fr(i,r,v)}}(S=new zi(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function zr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Fr(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),mt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),zr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?zr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else zr(o,r,a);o.length=r.length}else t[n]=e[n]}function Vr(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(n,he),at(n,vr);u(Gr,Hr=n),Gr.prototype.childrenRef=function(){return this._children},Gr.prototype.children=function(){return this._children.slice()},Gr.prototype.childAt=function(t){return this._children[t]},Gr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Gr.prototype.childCount=function(){return this._children.length},Gr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Gr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Gr.prototype.replace=function(t,e){t=I(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Gr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Gr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Gr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=I(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Gr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Gr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Gr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Gr.prototype.addSelfToZr=function(t){Hr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Gr.prototype.removeSelfFromZr=function(t){Hr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Gr.prototype.getBoundingRect=function(t){for(var e=new X(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(X.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Hr,Wr=Gr;function Gr(t){var e=Hr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Wr.prototype.type="group";var Ur={},Xr={};o.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},o.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},o.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},o.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return Si(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=Si(e[r].color,1);return(n/=i)<.4}}return!1}(t))},o.prototype.getBackgroundColor=function(){return this._backgroundColor},o.prototype.setDarkMode=function(t){this._darkMode=t},o.prototype.isDarkMode=function(){return this._darkMode},o.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},o.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},o.prototype.flush=function(){this._disposed||this._flush(!1)},o.prototype._flush=function(t){var e,n=Fi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),Fi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},o.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},o.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},o.prototype.refreshHover=function(){this._needsRefreshHover=!0},o.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},o.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},o.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},o.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},o.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},o.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},o.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},o.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},o.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},o.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},o.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Wr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},o.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Xr[t])};var qr,Yr=o;function o(t,e,n){var i,r=this,o=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new Mn),a=n.renderer||"canvas",a=(Ur[a]||(a=ct(Ur)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Ur[a](e,o,n,t)),e=n.ssr||a.ssrOnly,t=(this.storage=o,this.painter=a,b.node||b.worker||e?null:new sr(a.getViewportRoot(),a.root)),s=n.useCoarsePointer;(null==s||"auto"===s?b.touchEventsSupported:!!s)&&(i=E(n.pointerSize,44)),this.handler=new sn(o,a,t,a.root,i),this.animation=new Hi({stage:{update:e?null:function(){return r._flush(!0)}}}),e||this.animation.start()}function Zr(t,e){t=new Yr(et++,t,e);return Xr[t.id]=t}function jr(t,e){Ur[t]=e}function Kr(t){qr=t}var $r=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Xr)Xr.hasOwnProperty(t)&&Xr[t].dispose();Xr={}},getElementSSRData:function(t){if("function"==typeof qr)return qr(t)},getInstance:function(t){return Xr[t]},init:Zr,registerPainter:jr,registerSSRDataGetter:Kr,version:"5.6.1"}),Qr=20;function Jr(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function f(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function to(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),Qr),t=(+t).toFixed(e),n?t:+t}function eo(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return no(t)}function no(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function io(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function ro(t){var e=2*Math.PI;return(t%e+e)%e}function oo(t){return-1e-4<t&&t<1e-4}var ao=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function so(t){var e,n;return t instanceof Date?t:V(t)?(e=ao.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function lo(t){return Math.pow(10,uo(t))}function uo(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function ho(t,e){var n=uo(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function co(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function po(t){return!isNaN(co(t))}function fo(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function g(t){throw new Error(t)}function go(t,e,n){return(e-t)*n+t}var yo="series\0";function mo(t){return t instanceof Array?t:null==t?[]:[t]}function vo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var _o=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function xo(t){return!R(t)||F(t)||t instanceof Date?t:t.value}function wo(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),N()),e=(O(n,function(t,e){R(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||Co(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,O(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=So(t.id),null!=(i=s.get(n)))&&(It(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,O(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!Co(t)&&!Co(i)&&bo("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,O(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||Co(e.existing)||e.existing&&null!=t.id&&!bo("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,O(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=N(),O(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),O(t,function(t){var e=t.newOption;It(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),O(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(R(i)){if(r.name=null!=i.name?So(i.name):n?n.name:yo+e,n)r.id=So(n.id);else if(null!=i.id)r.id=So(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function bo(t,e,n){e=Mo(e[t],null),n=Mo(n[t],null);return null!=e&&null!=n&&e===n}function So(t){return Mo(t,"")}function Mo(t,e){return null==t?e:V(t)?t:gt(t)||ft(t)?t+"":e}function To(t){t=t.name;return!(!t||!t.indexOf(yo))}function Co(t){return t&&null!=t.id&&0===So(t.id).indexOf("\0_ec_\0")}function Io(t,r,o){O(t,function(t){var e,n,i=t.newOption;R(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function ko(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?z(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?z(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Do(){var e="__ec_inner_"+Ao++;return function(t){return t[e]||(t[e]={})}}var Ao=Math.round(9*Math.random());function Po(n,t,i){var t=Lo(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=Ro(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function Lo(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=N(),o={},a=!1;return O(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&I(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Oo={useDefault:!0,enableAll:!1,enableNone:!1};function Ro(t,e,n,i){i=i||Oo;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(It(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(It(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function Eo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var No=".",Bo="___EC__COMPONENT__CONTAINER___",zo="___EC__EXTENDED_CLASS___";function Fo(t){var e={main:"",sub:""};return t&&(t=t.split(No),e.main=t[0]||"",e.sub=t[1]||""),e}function Vo(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return k(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(u(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),L(e.prototype,t),e[zo]=!0,e.extend=this.extend,e.superCall=Go,e.superApply=Uo,e.superClass=r,e}}function Ho(t,e){t.extend=e.extend}var Wo=Math.round(10*Math.random());function Go(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function Uo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Xo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(It(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Fo(t.prototype.type=n)).sub?e.sub!==Bo&&(function(t){var e=r[t.main];e&&e[Bo]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[Bo]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Fo(t),n=[],t=r[t.main];return t&&t[Bo]?O(t,function(t,e){e!==Bo&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Fo(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return O(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Fo(t),t=r[t.main];return t&&t[Bo]}}function qo(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=I(e,o)||n&&I(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var Yo=qo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Zo=(jo.prototype.getAreaStyle=function(t,e){return Yo(this,t,e)},jo);function jo(){}var Ko=new ni(50);function $o(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=Ko.get(t))?Jo(e=i.image)||i.pending.push(n):((e=H.loadImage(t,Qo,Qo)).__zrImageSrc=t,Ko.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function Qo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Jo(t){return t&&t.width&&t.height}var ta=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function ea(t,e,n,i,r,o){if(n){for(var a=(e+"").split("\n"),s=(o=na(n,i,r,o),!1),l={},u=0,h=a.length;u<h;u++)ia(l,a[u],o),a[u]=l.textLine,s=s||l.isTruncated;t.text=a.join("\n"),t.isTruncated=s}else t.text="",t.isTruncated=!1}function na(t,e,n,i){for(var r=L({},i=i||{}),o=(r.font=e,n=E(n,"..."),r.maxIterations=E(i.maxIterations,2),r.minChar=E(i.minChar,0)),a=(r.cnCharWidth=br("国",e),r.ascCharWidth=br("a",e)),s=(r.placeholder=E(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=br(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function ia(t,e,n){var i=n.containerWidth,r=n.font,o=n.contentWidth;if(i)if((l=br(e,r))<=i)t.textLine=e,t.isTruncated=!1;else{for(var a=0;;a++){if(l<=o||a>=n.maxIterations){e+=n.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(e,o,n.ascCharWidth,n.cnCharWidth):0<l?Math.floor(e.length*o/l):0,l=br(e=e.substr(0,s),r)}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}else t.textLine="",t.isTruncated=!1}var ra=function(){},oa=function(t){this.tokens=[],t&&(this.tokens=t)},aa=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function sa(t,e){var n=new aa;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=ta.lastIndex=0;null!=(i=ta.exec(t));){var u=i.index;l<u&&la(n,t.substring(l,u),e,s),la(n,i[2],e,s,i[1]),l=ta.lastIndex}l<t.length&&la(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow,m={};t:for(var v=0;v<n.lines.length;v++){for(var _=n.lines[v],x=0,w=0,b=0;b<_.tokens.length;b++){var S=(A=_.tokens[b]).styleName&&e.rich[A.styleName]||{},M=A.textPadding=S.padding,T=M?M[1]+M[3]:0,C=A.font=S.font||e.font,I=(A.contentHeight=Ir(C),E(S.height,A.contentHeight));if(A.innerHeight=I,M&&(I+=M[0]+M[2]),A.height=I,A.lineHeight=Mt(S.lineHeight,e.lineHeight,I),A.align=S&&S.align||e.align,A.verticalAlign=S&&S.verticalAlign||"middle",y&&null!=o&&p+A.lineHeight>o){M=n.lines.length;0<b?(_.tokens=_.tokens.slice(0,b),L(_,w,x),n.lines=n.lines.slice(0,v+1)):n.lines=n.lines.slice(0,v),n.isTruncated=n.isTruncated||n.lines.length<M;break t}var k,M=S.width,D=null==M||"auto"===M;"string"==typeof M&&"%"===M.charAt(M.length-1)?(A.percentWidth=M,c.push(A),A.contentWidth=br(A.text,C)):(D&&(M=(M=S.backgroundColor)&&M.image)&&(k=void 0,Jo(M="string"==typeof(h=M)?(k=Ko.get(h))&&k.image:h))&&(A.width=Math.max(A.width,M.width*I/M.height)),null!=(k=g&&null!=r?r-w:null)&&k<A.width?!D||k<T?(A.text="",A.width=A.contentWidth=0):(ea(m,A.text,k-T,C,e.ellipsis,{minChar:e.truncateMinChar}),A.text=m.text,n.isTruncated=n.isTruncated||m.isTruncated,A.width=A.contentWidth=br(A.text,C)):A.contentWidth=br(A.text,C)),A.width+=T,w+=A.width,S&&(x=Math.max(x,A.lineHeight))}L(_,w,x)}n.outerWidth=n.width=E(r,d),n.outerHeight=n.height=E(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(v=0;v<c.length;v++){var A,P=(A=c[v]).percentWidth;A.width=parseInt(P,10)/100*n.width}}return n;function L(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function la(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=kr(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=ha(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new ra;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:br(g,h),p||c?u.push(new oa([y])):1===(f=(d=(u[u.length-1]||(u[0]=new oa)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var ua=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function ha(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=br(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||!!ua[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var ca,pa="__zr_style_"+Math.round(10*Math.random()),da={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},fa={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},ga=(da[pa]=!0,["z","z2","invisible"]),ya=["invisible"],n=(u(a,ca=n),a.prototype._init=function(t){for(var e=ct(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):ca.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},a.prototype.beforeBrush=function(){},a.prototype.afterBrush=function(){},a.prototype.innerBeforeBrush=function(){},a.prototype.innerAfterBrush=function(){},a.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){ma.copy(t.getBoundingRect()),t.transform&&ma.applyTransform(t.transform);return va.width=e,va.height=n,!ma.intersect(va)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},a.prototype.contain=function(t,e){return this.rectContain(t,e)},a.prototype.traverse=function(t,e){t.call(e,this)},a.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},a.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new X(0,0,0,0)),r?X.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},a.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},a.prototype.getPrevPaintRect=function(){return this._prevPaintRect},a.prototype.animateStyle=function(t){return this.animate("style",t)},a.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},a.prototype.attrKV=function(t,e){"style"!==t?ca.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},a.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:L(this.style,t),this.dirtyStyle(),this},a.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},a.prototype.dirty=function(){this.dirtyStyle()},a.prototype.styleChanged=function(){return!!(2&this.__dirty)},a.prototype.styleUpdated=function(){this.__dirty&=-3},a.prototype.createStyle=function(t){return zt(da,t)},a.prototype.useStyle=function(t){t[pa]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},a.prototype.isStyleObject=function(t){return t[pa]},a.prototype._innerSaveToNormal=function(t){ca.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,ga)},a.prototype._applyStateObj=function(t,e,n,i,r,o){ca.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=ct(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=ct(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?ya:ga,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},a.prototype._mergeStates=function(t){for(var e,n=ca.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},a.prototype._mergeStyle=function(t,e){return L(t,e),t},a.prototype.getAnimationStyleProps=function(){return fa},a.initDefaultProps=((n=a.prototype).type="displayable",n.invisible=!1,n.z=0,n.z2=0,n.zlevel=0,n.culling=!1,n.cursor="pointer",n.rectHover=!1,n.incremental=!1,n._rect=null,n.dirtyRectTolerance=0,void(n.__dirty=2|_n)),a);function a(t){return ca.call(this,t)||this}var ma=new X(0,0,0,0),va=new X(0,0,0,0);var _a=Math.min,xa=Math.max,wa=Math.sin,ba=Math.cos,Sa=2*Math.PI,Ma=Ut(),Ta=Ut(),Ca=Ut();function Ia(t,e,n,i,r,o){r[0]=_a(t,n),r[1]=_a(e,i),o[0]=xa(t,n),o[1]=xa(e,i)}var ka=[],Da=[];var q={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Aa=[],Pa=[],La=[],Oa=[],Ra=[],Ea=[],Na=Math.min,Ba=Math.max,za=Math.cos,Fa=Math.sin,Va=Math.abs,Ha=Math.PI,Wa=2*Ha,Ga="undefined"!=typeof Float32Array,Ua=[];function Xa(t){return Math.round(t/Ha*1e8)/1e8%2*Ha}s.prototype.increaseVersion=function(){this._version++},s.prototype.getVersion=function(){return this._version},s.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Va(n/ur/t)||0,this._uy=Va(n/ur/e)||0)},s.prototype.setDPR=function(t){this.dpr=t},s.prototype.setContext=function(t){this._ctx=t},s.prototype.getContext=function(){return this._ctx},s.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},s.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},s.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(q.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},s.prototype.lineTo=function(t,e){var n=Va(t-this._xi),i=Va(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(q.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},s.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(q.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},s.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(q.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},s.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),Ua[0]=i,Ua[1]=r,s=o,(l=Xa((a=Ua)[0]))<0&&(l+=Wa),h=l-a[0],u=a[1],u+=h,!s&&Wa<=u-l?u=l+Wa:s&&Wa<=l-u?u=l-Wa:!s&&u<l?u=l+(Wa-Xa(l-u)):s&&l<u&&(u=l-(Wa-Xa(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=Ua[1])-(i=Ua[0]);return this.addData(q.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=za(r)*n+t,this._yi=Fa(r)*n+e,this},s.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},s.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(q.R,t,e,n,i),this},s.prototype.closePath=function(){this._drawPendingPt(),this.addData(q.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},s.prototype.fill=function(t){t&&t.fill(),this.toStatic()},s.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},s.prototype.len=function(){return this._len},s.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Ga||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},s.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Ga&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},s.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},s.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},s.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},s.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,Ga)&&11<this._len&&(this.data=new Float32Array(t))},s.prototype.getBoundingRect=function(){La[0]=La[1]=Ra[0]=Ra[1]=Number.MAX_VALUE,Oa[0]=Oa[1]=Ea[0]=Ea[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var N=e[a++],B=1===a;switch(B&&(r=n=e[a],o=i=e[a+1]),N){case q.M:n=r=e[a++],i=o=e[a++],Ra[0]=r,Ra[1]=o,Ea[0]=r,Ea[1]=o;break;case q.L:Ia(n,i,e[a],e[a+1],Ra,Ea),n=e[a++],i=e[a++];break;case q.C:W=H=m=y=V=g=f=d=p=c=F=z=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],z=e[a++],F=e[a++],c=e[a],p=e[a+1],d=Ra,f=Ea,g=Hn,V=zn,y=g(s,u,z,c,ka);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,z,c,ka[m]);d[0]=_a(H,d[0]),f[0]=xa(H,f[0])}for(y=g(l,h,F,p,Da),m=0;m<y;m++){var W=V(l,h,F,p,Da[m]);d[1]=_a(W,d[1]),f[1]=xa(W,f[1])}d[0]=_a(s,d[0]),f[0]=xa(s,f[0]),d[0]=_a(c,d[0]),f[0]=xa(c,f[0]),d[1]=_a(l,d[1]),f[1]=xa(l,f[1]),d[1]=_a(p,d[1]),f[1]=xa(p,f[1]),n=e[a++],i=e[a++];break;case q.Q:g=n,L=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=Ra,T=Ea,t=w=t=_=void 0,_=Un,t=xa(_a((w=qn)(g,M,S),1),0),w=xa(_a(w(L,x,v),1),0),M=_(g,M,S,t),t=_(L,x,v,w),b[0]=_a(g,S,M),b[1]=_a(L,v,t),T[0]=xa(g,S,M),T[1]=xa(L,v,t),n=e[a++],i=e[a++];break;case q.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(B&&(r=za(S)*w+_,o=Fa(S)*b+x),E=v=U=G=R=O=L=P=A=D=k=I=C=void 0,_),I=x,k=w,D=b,A=S,P=M,L=T,O=Ra,R=Ea,G=re,U=oe;if((v=Math.abs(A-P))%Sa<1e-4&&1e-4<v)O[0]=C-k,O[1]=I-D,R[0]=C+k,R[1]=I+D;else{Ma[0]=ba(A)*k+C,Ma[1]=wa(A)*D+I,Ta[0]=ba(P)*k+C,Ta[1]=wa(P)*D+I,G(O,Ma,Ta),U(R,Ma,Ta),(A%=Sa)<0&&(A+=Sa),(P%=Sa)<0&&(P+=Sa),P<A&&!L?P+=Sa:A<P&&L&&(A+=Sa),L&&(v=P,P=A,A=v);for(var E=0;E<P;E+=Math.PI/2)A<E&&(Ca[0]=ba(E)*k+C,Ca[1]=wa(E)*D+I,G(O,Ca,O),U(R,Ca,R))}n=za(M)*w+_,i=Fa(M)*b+x;break;case q.R:Ia(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],Ra,Ea);break;case q.Z:n=r,i=o}re(La,La,Ra),oe(Oa,Oa,Ea)}return 0===a&&(La[0]=La[1]=Oa[0]=Oa[1]=0),new X(La[0],La[1],Oa[0]-La[0],Oa[1]-La[1])},s.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case q.M:r=a=t[c++],o=s=t[c++];break;case q.L:var g=t[c++],y=(_=t[c++])-o;(Va(I=g-r)>n||Va(y)>i||c===e-1)&&(f=Math.sqrt(I*I+y*y),r=g,o=_);break;case q.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],w=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=zn(t,n,r,a,f),f=zn(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,w,10),r=x,o=w;break;case q.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=Un(t,n,r,p),p=Un(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case q.A:var x=t[c++],w=t[c++],b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=za(M)*b+x,s=Fa(M)*S+w),f=Ba(b,S)*Na(Wa,Math.abs(T)),r=za(C)*b+x,o=Fa(C)*S+w;break;case q.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case q.Z:var I=a-r,y=s-o;f=Math.sqrt(I*I+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},s.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,N=this._ux,B=this._uy,z=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<z;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==q.L&&0<f&&(t.lineTo(l,u),f=0),y){case q.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case q.L:var m=h[g++],v=h[g++],_=Va(m-r),x=Va(v-o);if(N<_||B<x){if(c){if(s<p+(E=a[d++])){var w=(s-p)/E;t.lineTo(r*(1-w)+m*w,o*(1-w)+v*w);break t}p+=E}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case q.C:var b=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(E=a[d++])){Wn(r,b,M,x,w=(s-p)/E,Aa),Wn(o,S,T,_,w,Pa),t.bezierCurveTo(Aa[1],Pa[1],Aa[2],Pa[2],Aa[3],Pa[3]);break t}p+=E}t.bezierCurveTo(b,S,M,T,x,_),r=x,o=_;break;case q.Q:b=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(E=a[d++])){Yn(r,b,M,w=(s-p)/E,Aa),Yn(o,S,T,w,Pa),t.quadraticCurveTo(Aa[1],Pa[1],Aa[2],Pa[2]);break t}p+=E}t.quadraticCurveTo(b,S,M,T),r=M,o=T;break;case q.A:var C=h[g++],I=h[g++],k=h[g++],D=h[g++],A=h[g++],P=h[g++],L=h[g++],V=!h[g++],H=D<k?k:D,O=.001<Va(k-D),R=A+P,W=!1;if(c&&(s<p+(E=a[d++])&&(R=A+P*(s-p)/E,W=!0),p+=E),O&&t.ellipse?t.ellipse(C,I,k,D,L,A,R,V):t.arc(C,I,H,A,R,V),W)break t;F&&(n=za(A)*k+C,i=Fa(A)*D+I),r=za(R)*k+C,o=Fa(R)*D+I;break;case q.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var E,P=h[g++],O=h[g++];if(c){if(s<p+(E=a[d++])){L=s-p;t.moveTo(m,v),t.lineTo(m+Na(L,P),v),0<(L-=P)&&t.lineTo(m+P,v+Na(L,O)),0<(L-=O)&&t.lineTo(m+Ba(P-L,0),v+O),0<(L-=P)&&t.lineTo(m,v+Ba(O-L,0));break t}p+=E}t.rect(m,v,P,O);break;case q.Z:if(c){if(s<p+(E=a[d++])){w=(s-p)/E;t.lineTo(r*(1-w)+n*w,o*(1-w)+i*w);break t}p+=E}t.closePath(),r=n,o=i}}},s.prototype.clone=function(){var t=new s,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},s.CMD=q,s.initDefaultProps=((cu=s.prototype)._saveData=!0,cu._ux=0,cu._uy=0,cu._pendingPtDist=0,void(cu._version=0));var qa=s;function s(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Ya(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var Za=2*Math.PI;function ja(t){return(t%=Za)<0&&(t+=Za),t}var Ka=2*Math.PI;function $a(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var Qa=qa.CMD,Ja=2*Math.PI,ts=1e-4;var es=[-1,-1,-1],ns=[-1,-1];function is(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=Vn(e,i,o,s,u,es);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=es[y],v=0===m||1===m?.5:1;zn(t,n,r,a,m)<l||(d<0&&(d=Hn(e,i,o,s,ns),ns[1]<ns[0]&&1<d&&(c=void 0,c=ns[0],ns[0]=ns[1],ns[1]=c),f=zn(e,i,o,s,ns[0]),1<d)&&(g=zn(e,i,o,s,ns[1])),2===d?m<ns[0]?p+=f<e?v:-v:m<ns[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<ns[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function rs(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=es,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,Nn(h)?Bn(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):Nn(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=Dn(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=qn(e,i,o);if(0<=g&&g<=1){for(var y=0,m=Un(e,i,o,g),v=0;v<f;v++){var _=0===es[v]||1===es[v]?.5:1;Un(t,n,r,es[v])<a||(es[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===es[0]||1===es[0]?.5:1,Un(t,n,r,es[0])<a?0:o<e?_:-_}function os(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===Qa.M&&1<d&&(n||(l+=$a(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case Qa.M:u=c=a[d++],h=p=a[d++];break;case Qa.L:if(n){if(Ya(u,h,a[d],a[d+1],e,i,r))return!0}else l+=$a(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Qa.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&Gn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=is(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Qa.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&Zn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=rs(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Qa.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=$a(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%Ka<1e-4||((r=o?(e=i,i=ja(r),ja(e)):(i=ja(i),ja(r)))<i&&(r+=Ka),(t=Math.atan2(l,s))<0&&(t+=Ka),i<=t&&t<=r)||i<=t+Ka&&t+Ka<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(es[0]=-e,es[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(Ja-1e-4<=n)return r=Ja,h=o?1:-1,a>=es[i=0]+t&&a<=es[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=Ja,r+=Ja);for(var l=0,u=0;u<2;u++){var h,c=es[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?Ja+c:c)&&c<=r||i<=c+Ja&&c+Ja<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case Qa.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if(Ya(c,p,S,p,e,i,r)||Ya(S,p,S,M,e,i,r)||Ya(S,M,c,M,e,i,r)||Ya(c,M,c,p,e,i,r))return!0}else l=(l+=$a(S,p,S,M,i,r))+$a(c,M,c,p,i,r);break;case Qa.Z:if(n){if(Ya(u,h,c,p,e,i,r))return!0}else l+=$a(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<ts)||(l+=$a(u,h,c,p,i,r)||0),0!==l}var as,ss=B({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},da),ls={style:B({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},fa.style)},us=xr.concat(["invisible","culling","z","z2","zlevel","parent"]),hs=(u(l,as=n),l.prototype.update=function(){var e=this,t=(as.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new l,r=(i.buildPath===l.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<us.length;++o)i[us[o]]=this[us[o]];i.__dirty|=_n}else this._decalEl&&(this._decalEl=null)},l.prototype.getDecalElement=function(){return this._decalEl},l.prototype._init=function(t){var e=ct(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?L(this.style,o):this.useStyle(o):"shape"===r?L(this.shape,o):as.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},l.prototype.getDefaultStyle=function(){return null},l.prototype.getDefaultShape=function(){return{}},l.prototype.canBeInsideText=function(){return this.hasFill()},l.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=Si(e,0))?hr:.2<t?"#eee":cr;if(e)return cr}return hr},l.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==Si(t,0)<.4)return e}},l.prototype.buildPath=function(t,e,n){},l.prototype.pathUpdated=function(){this.__dirty&=~xn},l.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},l.prototype.createPathProxy=function(){this.path=new qa(!1)},l.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},l.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},l.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&xn)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},l.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),os(n,i/r,!0,t,e)))return!0}if(this.hasFill())return os(n,0,!1,t,e)}return!1},l.prototype.dirtyShape=function(){this.__dirty|=xn,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},l.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},l.prototype.animateShape=function(t){return this.animate("shape",t)},l.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},l.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):as.prototype.attrKV.call(this,t,e)},l.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:L(n,t),this.dirtyShape(),this},l.prototype.shapeChanged=function(){return!!(this.__dirty&xn)},l.prototype.createStyle=function(t){return zt(ss,t)},l.prototype._innerSaveToNormal=function(t){as.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=L({},this.shape))},l.prototype._applyStateObj=function(t,e,n,i,r,o){as.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=L({},n.shape),L(a,e.shape)):(a=L({},(i?this:n).shape),L(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=L({},this.shape);for(var l={},u=ct(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},l.prototype._mergeStates=function(t){for(var e,n=as.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},l.prototype.getAnimationStyleProps=function(){return ls},l.prototype.isZeroArea=function(){return!1},l.extend=function(n){u(r,i=l),r.prototype.getDefaultStyle=function(){return S(n.style)},r.prototype.getDefaultShape=function(){return S(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},l.initDefaultProps=((cu=l.prototype).type="path",cu.strokeContainThreshold=5,cu.segmentIgnoreThreshold=0,cu.subPixelOptimize=!1,cu.autoBatch=!1,void(cu.__dirty=2|_n|xn)),l);function l(t){return as.call(this,t)||this}var cs,ps=B({strokeFirst:!0,font:j,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},ss),ds=(u(fs,cs=n),fs.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},fs.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},fs.prototype.createStyle=function(t){return zt(ps,t)},fs.prototype.setBoundingRect=function(t){this._rect=t},fs.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=Mr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},fs.initDefaultProps=void(fs.prototype.dirtyRectTolerance=10),fs);function fs(){return null!==cs&&cs.apply(this,arguments)||this}ds.prototype.type="tspan";var gs=B({x:0,y:0},da),ys={style:B({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},fa.style)};u(_s,ms=n),_s.prototype.createStyle=function(t){return zt(gs,t)},_s.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},_s.prototype.getWidth=function(){return this._getSize("width")},_s.prototype.getHeight=function(){return this._getSize("height")},_s.prototype.getAnimationStyleProps=function(){return ys},_s.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var ms,vs=_s;function _s(){return null!==ms&&ms.apply(this,arguments)||this}vs.prototype.type="image";var xs=Math.round;function ws(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(xs(2*i)===xs(2*r)&&(t.x1=t.x2=Ss(i,n,!0)),xs(2*o)===xs(2*e))&&(t.y1=t.y2=Ss(o,n,!0)),t}function bs(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Ss(i,n,!0),t.y=Ss(r,n,!0),t.width=Math.max(Ss(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Ss(r+e,n,!1)-t.y,0===e?0:1)),t}function Ss(t,e,n){var i;return e?((i=xs(2*t))+xs(e))%2==0?i/2:(i+(n?1:-1))/2:t}var Ms,Ts=function(){this.x=0,this.y=0,this.width=0,this.height=0},Cs={},Is=(u(ks,Ms=hs),ks.prototype.getDefaultShape=function(){return new Ts},ks.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=bs(Cs,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},ks.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},ks);function ks(t){return Ms.call(this,t)||this}Is.prototype.type="rect";var Ds,As={fill:"#000"},Ps={style:B({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},fa.style)},Ls=(u(Os,Ds=n),Os.prototype.childrenRef=function(){return this._children},Os.prototype.update=function(){Ds.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Os.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ds.prototype.updateTransform.call(this)},Os.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ds.prototype.getLocalTransform.call(this,t)},Os.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ds.prototype.getComputedTransform.call(this)},Os.prototype._updateSubTexts=function(){var t;this._childCursor=0,zs(t=this.style),O(t.rich,zs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Os.prototype.addSelfToZr=function(t){Ds.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Os.prototype.removeSelfFromZr=function(t){Ds.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Os.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new X(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Os.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||As},Os.prototype.setTextContent=function(t){},Os.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},L(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Os.prototype._mergeRich=function(t,e){for(var n=ct(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},L(t[r],e[r])}},Os.prototype.getAnimationStyleProps=function(){return Ps},Os.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Os.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||j,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Ir(o),l=E(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=!1,p=e.width,i=(n=null==p||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?ha(t,e.font,p,"breakAll"===i,0).lines:[]).length*l,d=E(e.height,i);if(d<i&&h&&(h=Math.floor(d/l),c=c||n.length>h,n=n.slice(0,h)),t&&a&&null!=p)for(var f=na(p,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),g={},y=0;y<n.length;y++)ia(g,n[y],f),n[y]=g.textLine,c=c||g.isTruncated;for(var h=d,m=0,y=0;y<n.length;y++)m=Math.max(br(n[y],o),m);return null==p&&(p=m),t=m,r&&(h+=r[0]+r[2],t+=r[1]+r[3],p+=r[1]+r[3]),{lines:n,height:d,outerWidth:t=u?p:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:m,contentHeight:i,width:p,isTruncated:c}}(Ws(e),e),o=Gs(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=(this.isTruncated=!!r.isTruncated,e.x||0),f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=Cr(f,r.contentHeight,y),_=((o||i)&&(t=Tr(d,l,g),f=Cr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Hs(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(Vs(("fill"in e?e:(o=!0,p)).fill)),w=(Fs("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),b=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(ds),I=C.createStyle();C.useStyle(I),I.text=h[T],I.x=m,I.y=v,g&&(I.textAlign=g),I.textBaseline="middle",I.opacity=e.opacity,I.strokeFirst=!0,b&&(I.shadowBlur=e.textShadowBlur||0,I.shadowColor=e.textShadowColor||"transparent",I.shadowOffsetX=e.textShadowOffsetX||0,I.shadowOffsetY=e.textShadowOffsetY||0),I.stroke=w,I.fill=x,w&&(I.lineWidth=e.lineWidth||_,I.lineDash=e.lineDash,I.lineDashOffset=e.lineDashOffset||0),I.font=n,Bs(I,e),v+=c,S&&C.setBoundingRect(new X(Tr(I.x,u,I.textAlign),Cr(I.y,M,I.textBaseline),u,M))}},Os.prototype._updateRichTexts=function(){for(var t=this.style,e=sa(Ws(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=(this.isTruncated=!!e.isTruncated,Tr(a,i,u)),u=Cr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Gs(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},Os.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Gs(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Hs(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(ds)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=Vs(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Fs("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||j,i.opacity=Mt(s.opacity,e.opacity,1),Bs(i,s),l&&(i.lineWidth=Mt(s.lineWidth,e.lineWidth,p),i.lineDash=E(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new X(Tr(i.x,c,i.textAlign),Cr(i.y,h,i.textBaseline),c,h))},Os.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(Is)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=E(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(vs)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=E(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=Mt(t.opacity,e.opacity,1)},Os.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&kt(n)||t.textFont||t.font},Os);function Os(t){var e=Ds.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=As,e.attr(t),e}var Rs={left:!0,right:1,center:1},Es={top:1,bottom:1,middle:1},Ns=["fontStyle","fontWeight","fontSize","fontFamily"];function Bs(t,e){for(var n=0;n<Ns.length;n++){var i=Ns[n],r=e[i];null!=r&&(t[i]=r)}}function zs(t){var e;t&&(t.font=Ls.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||Rs[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Es[e]?e:"top",t.padding)&&(t.padding=Ct(t.padding))}function Fs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Vs(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Hs(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ws(t){t=t.text;return null!=t&&(t+=""),t}function Gs(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var Us=Do(),Xs=1,qs={},Ys=Do(),Zs=Do(),js=0,Ks=1,$s=2,Qs=["emphasis","blur","select"],Js=["normal","emphasis","blur","select"],tl="highlight",el="downplay",nl="select",il="unselect",rl="toggleSelect";function ol(t){return null!=t&&"none"!==t}function al(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function sl(t){al(t,"emphasis",$s)}function ll(t){t.hoverState===$s&&al(t,"normal",js)}function ul(t){al(t,"blur",Ks)}function hl(t){t.hoverState===Ks&&al(t,"normal",js)}function cl(t){t.selected=!0}function pl(t){t.selected=!1}function dl(t,e,n){e(t,n)}function fl(t,e,n){dl(t,e,n),t.isGroup&&t.traverse(function(t){dl(t,e,n)})}function gl(t,e,n){var i=0<=I(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=L({},n),e=L({opacity:i?r:.1*t.opacity},e),n.style=e),n}function yl(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=I(e,"select"),a=!1,n instanceof hs&&(r=Ys(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,ol(o)||ol(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=L({},i),(r=L({},r)).fill=o):!ol(r.fill)&&ol(o)?(a=!0,i=L({},i),(r=L({},r)).fill=Ti(o)):!ol(r.stroke)&&ol(e)&&(a||(i=L({},i),r=L({},r)),r.stroke=Ti(e)),i.style=r),i&&null==i.z2&&(a||(i=L({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return gl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=L({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function ml(t){t.stateProxy=yl;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=yl),t&&(t.stateProxy=yl)}function vl(t,e){Tl(t,e)||t.__highByOuter||fl(t,sl)}function _l(t,e){Tl(t,e)||t.__highByOuter||fl(t,ll)}function xl(t,e){t.__highByOuter|=1<<(e||0),fl(t,sl)}function wl(t,e){(t.__highByOuter&=~(1<<(e||0)))||fl(t,ll)}function bl(t){fl(t,hl)}function Sl(t){fl(t,cl)}function Ml(t){fl(t,pl)}function Tl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Cl(r){var e=r.getModel(),o=[],a=[];e.eachComponent(function(t,e){var n=Zs(e),t="series"===t,i=t?r.getViewOfSeriesModel(e):r.getViewOfComponentModel(e);t||a.push(i),n.isBlured&&(i.group.traverse(function(t){hl(t)}),t)&&o.push(e),n.isBlured=!1}),O(a,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(o,!1,e)})}function Il(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&bl(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&e&&"self"===o||ul(t)}),st(o))c(t.getData(),o);else if(R(o))for(var i=ct(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),Zs(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(h,!0,n)}))}function kl(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(Zs(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){ul(t)})}function Dl(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===Us(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function Al(i){O(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?Sl:Ml)(t)})})}function Pl(t,e,n){Nl(t,!0),fl(t,ml);t=Us(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Ll(t,e,n,i){i?Nl(t,!1):Pl(t,e,n)}var Ol=["emphasis","blur","select"],Rl={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function El(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Ol.length;r++){var o=Ol[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[Rl[n]]()}}function Nl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Bl(t){return!(!t||!t.__highDownDispatcher)}function zl(t){t=t.type;return t===nl||t===il||t===rl}function Fl(t){t=t.type;return t===tl||t===el}var Vl=qa.CMD,Hl=[[],[],[]],Wl=Math.sqrt,Gl=Math.atan2;var Ul=Math.sqrt,Xl=Math.sin,ql=Math.cos,Yl=Math.PI;function Zl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function jl(t,e){return(t[0]*e[0]+t[1]*e[1])/(Zl(t)*Zl(e))}function Kl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(jl(t,e))}function $l(t,e,n,i,r,o,a,s,l,u,h){var l=l*(Yl/180),c=ql(l)*(t-n)/2+Xl(l)*(e-i)/2,p=-1*Xl(l)*(t-n)/2+ql(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=Ul(d),s*=Ul(d)),(r===o?-1:1)*Ul((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+ql(l)*r-Xl(l)*d,n=(e+i)/2+Xl(l)*r+ql(l)*d,e=Kl([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=Kl(i,c);jl(i,c)<=-1&&(r=Yl),(r=1<=jl(i,c)?0:r)<0&&(p=Math.round(r/Yl*1e6)/1e6,r=2*Yl+p%2*Yl),h.addData(u,t,n,a,s,e,r,l,o)}var Ql=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Jl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;u(nu,tu=hs),nu.prototype.applyTransform=function(t){};var tu,eu=nu;function nu(){return null!==tu&&tu.apply(this,arguments)||this}function iu(t){return null!=t.setData}function ru(t,e){var S=function(t){var e=new qa;if(t){var n,i=0,r=0,o=i,a=r,s=qa.CMD,l=t.match(Ql);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(Jl)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,I=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],$l(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],$l(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=L({},e);return t.buildPath=function(t){var e;iu(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Vl.M,h=Vl.C,c=Vl.L,p=Vl.R,d=Vl.A,f=Vl.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Wl(n[0]*n[0]+n[1]*n[1]),x=Wl(n[2]*n[2]+n[3]*n[3]),w=Gl(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=w,s[g++]+=w,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],ie(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],ie(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var b=Hl[o];b[0]=s[g++],b[1]=s[g++],ie(b,b,n),s[y++]=b[0],s[y++]=b[1]}}e.increaseVersion()}this.dirtyShape()},t}var ou,au=function(){this.cx=0,this.cy=0,this.r=0},su=(u(lu,ou=hs),lu.prototype.getDefaultShape=function(){return new au},lu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},lu);function lu(t){return ou.call(this,t)||this}su.prototype.type="circle";var uu,hu=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},cu=(u(pu,uu=hs),pu.prototype.getDefaultShape=function(){return new hu},pu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},pu);function pu(t){return uu.call(this,t)||this}cu.prototype.type="ellipse";var du=Math.PI,fu=2*du,gu=Math.sin,yu=Math.cos,mu=Math.acos,vu=Math.atan2,_u=Math.abs,xu=Math.sqrt,wu=Math.max,bu=Math.min,Su=1e-4;function Mu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/xu(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*xu(wu(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function Tu(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,I,k,D,A,P,L=wu(e.r,0),O=wu(e.r0||0,0),R=0<L;(R||0<O)&&(R||(L=O,O=0),L<O&&(R=L,L=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=_u(n-R),Su<(a=fu<m&&m%fu)&&(m=a),Su<L?fu-Su<m?(t.moveTo(i+L*yu(R),r+L*gu(R)),t.arc(i,r,L,R,n,!o),Su<O&&(t.moveTo(i+O*yu(n),r+O*gu(n)),t.arc(i,r,O,n,R,o))):(S=b=w=x=_=v=c=h=I=C=T=M=u=l=s=a=void 0,p=L*yu(R),d=L*gu(R),f=O*yu(n),g=O*gu(n),(y=Su<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=_u(L-O)/2,M=bu(e,l),T=bu(e,u),C=bu(e,a),I=bu(e,s),v=h=wu(M,T),_=c=wu(C,I),Su<h||Su<c)&&(x=L*yu(n),w=L*gu(n),b=O*yu(R),S=O*gu(R),m<du)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<Su))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],I=w-e[1],m=1/gu(mu((M*C+T*I)/(xu(M*M+T*T)*xu(C*C+I*I)))/2),M=xu(e[0]*e[0]+e[1]*e[1]),v=bu(h,(L-M)/(1+m)),_=bu(c,(O-M)/(m-1))),y?Su<v?(k=bu(l,v),D=bu(u,v),A=Mu(b,S,p,d,L,k,o),P=Mu(x,w,f,g,L,D,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&k===D?t.arc(i+A.cx,r+A.cy,v,vu(A.y0,A.x0),vu(P.y0,P.x0),!o):(0<k&&t.arc(i+A.cx,r+A.cy,k,vu(A.y0,A.x0),vu(A.y1,A.x1),!o),t.arc(i,r,L,vu(A.cy+A.y1,A.cx+A.x1),vu(P.cy+P.y1,P.cx+P.x1),!o),0<D&&t.arc(i+P.cx,r+P.cy,D,vu(P.y1,P.x1),vu(P.y0,P.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,L,R,n,!o)):t.moveTo(i+p,r+d),Su<O&&y?Su<_?(k=bu(a,_),A=Mu(f,g,x,w,O,-(D=bu(s,_)),o),P=Mu(p,d,b,S,O,-k,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&k===D?t.arc(i+A.cx,r+A.cy,_,vu(A.y0,A.x0),vu(P.y0,P.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,vu(A.y0,A.x0),vu(A.y1,A.x1),!o),t.arc(i,r,O,vu(A.cy+A.y1,A.cx+A.x1),vu(P.cy+P.y1,P.cx+P.x1),o),0<k&&t.arc(i+P.cx,r+P.cy,k,vu(P.y1,P.x1),vu(P.y0,P.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var Cu,Iu=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},ku=(u(Du,Cu=hs),Du.prototype.getDefaultShape=function(){return new Iu},Du.prototype.buildPath=function(t,e){Tu(t,e)},Du.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Du);function Du(t){return Cu.call(this,t)||this}ku.prototype.type="sector";var Au,Pu=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Lu=(u(Ou,Au=hs),Ou.prototype.getDefaultShape=function(){return new Pu},Ou.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Ou);function Ou(t){return Au.call(this,t)||this}function Ru(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)re(h,h,t[p]),oe(c,c,t[p]);re(h,h,i[0]),oe(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(Xt(t[p]));continue}r=t[p-1],o=t[p+1]}Yt(s,o,r),Kt(s,s,e);var g=Qt(f,r),y=Qt(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),Kt(l,s,-g),Kt(u,s,y),qt([],f,l)),g=qt([],f,u);i&&(oe(m,m,h),re(m,m,c),oe(g,g,h),re(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Lu.prototype.type="ring";var Eu,Nu=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Bu=(u(zu,Eu=hs),zu.prototype.getDefaultShape=function(){return new Nu},zu.prototype.buildPath=function(t,e){Ru(t,e,!0)},zu);function zu(t){return Eu.call(this,t)||this}Bu.prototype.type="polygon";var Fu,Vu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Hu=(u(Wu,Fu=hs),Wu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Wu.prototype.getDefaultShape=function(){return new Vu},Wu.prototype.buildPath=function(t,e){Ru(t,e,!1)},Wu);function Wu(t){return Fu.call(this,t)||this}Hu.prototype.type="polyline";var Gu,Uu={},Xu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},qu=(u(Yu,Gu=hs),Yu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Yu.prototype.getDefaultShape=function(){return new Xu},Yu.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=ws(Uu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},Yu.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},Yu);function Yu(t){return Gu.call(this,t)||this}qu.prototype.type="line";var Zu=[],ju=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Ku(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?Fn:zn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Fn:zn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Xn:Un)(t.x1,t.cpx1,t.x2,e),(n?Xn:Un)(t.y1,t.cpy1,t.y2,e)]}u(Ju,$u=hs),Ju.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ju.prototype.getDefaultShape=function(){return new ju},Ju.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(Yn(n,a,r,e,Zu),a=Zu[1],r=Zu[2],Yn(i,s,o,e,Zu),s=Zu[1],o=Zu[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(Wn(n,a,l,r,e,Zu),a=Zu[1],l=Zu[2],r=Zu[3],Wn(i,s,u,o,e,Zu),s=Zu[1],u=Zu[2],o=Zu[3]),t.bezierCurveTo(a,s,l,u,r,o)))},Ju.prototype.pointAt=function(t){return Ku(this.shape,t,!1)},Ju.prototype.tangentAt=function(t){t=Ku(this.shape,t,!0);return $t(t,t)};var $u,Qu=Ju;function Ju(t){return $u.call(this,t)||this}Qu.prototype.type="bezier-curve";var th,eh=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},nh=(u(ih,th=hs),ih.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ih.prototype.getDefaultShape=function(){return new eh},ih.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},ih);function ih(t){return th.call(this,t)||this}nh.prototype.type="arc";u(ah,rh=hs),ah.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},ah.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},ah.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},ah.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},ah.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),hs.prototype.getBoundingRect.call(this)};var rh,oh=ah;function ah(){var t=null!==rh&&rh.apply(this,arguments)||this;return t.type="compound",t}lh.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var sh=lh;function lh(t){this.colorStops=t||[]}u(ch,uh=sh);var uh,hh=ch;function ch(t,e,n,i,r,o){r=uh.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}u(dh,ph=sh);var ph,sh=dh;function dh(t,e,n,i,r){i=ph.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var fh=[0,0],gh=[0,0],yh=new M,mh=new M,vh=(_h.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},_h.prototype.intersect=function(t,e){var n=!0,i=!e;return yh.set(1/0,1/0),mh.set(0,0),!this._intersectCheckOneSide(this,t,yh,mh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,yh,mh,i,-1)&&(n=!1,i)||i||M.copy(e,n?yh:mh),n},_h.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,fh),this._getProjMinMaxOnAxis(s,e._corners,gh),fh[1]<gh[0]||gh[1]<fh[0]){if(a=!1,r)return a;var u=Math.abs(gh[0]-fh[1]),h=Math.abs(fh[0]-gh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(gh[0]-fh[1]),h=Math.abs(fh[0]-gh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},_h.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},_h);function _h(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var xh,wh=[],n=(u(bh,xh=n),bh.prototype.traverse=function(t,e){t.call(e,this)},bh.prototype.useStyle=function(){this.style={}},bh.prototype.getCursor=function(){return this._cursor},bh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},bh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},bh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},bh.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},bh.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},bh.prototype.getDisplayables=function(){return this._displayables},bh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},bh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},bh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},bh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(wh)),t.union(i)}this._rect=t}return this._rect},bh.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},bh);function bh(){var t=null!==xh&&xh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Sh=Do();function Mh(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(k(r)?(a=o,o=r,r=null):R(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=E(l.duration,200),h=E(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),k(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=k(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function Th(t,e,n,i,r,o){Mh("update",t,e,n,i,r,o)}function Ch(t,e,n,i,r,o){Mh("enter",t,e,n,i,r,o)}function Ih(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function kh(t,e,n,i,r,o){Ih(t)||Mh("leave",t,e,n,i,r,o)}function Dh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),kh(t,{style:{opacity:0}},e,n,i)}function Ah(t){Sh(t).oldStyle=t.style}var Ph=Math.max,Lh=Math.min,Oh={};function Rh(t){return hs.extend(t)}var Eh=function(t,e){var n,i=ru(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return u(r,n=eu),r};function Nh(t,e){return Eh(t,e)}function Bh(t,e){Oh[t]=e}function zh(t){if(Oh.hasOwnProperty(t))return Oh[t]}function Fh(t,e,n,i){t=new eu(ru(t,e));return n&&("center"===i&&(n=Hh(n,t.getBoundingRect())),Gh(t,n)),t}function Vh(t,e,n){var i=new vs({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Hh(e,t)))}});return i}function Hh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}var Wh=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new hs(e)).createPathProxy(),e.buildPath=function(t){var e;iu(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e};function Gh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function Uh(t,e){return ws(t,t,{lineWidth:e}),t}var Xh=Ss;function qh(t,e){for(var n=Re([]);t&&t!==e;)Ne(n,t.getLocalTransform(),n),t=t.parent;return n}function Yh(t,e,n){return e&&!st(e)&&(e=vr.getLocalTransform(e)),ie([],t,e=n?Ve([],e):e)}function Zh(t){return!t.isGroup}function jh(t,e,i){var r,n;function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=L({},t.shape)),e}t&&e&&(n={},t.traverse(function(t){Zh(t)&&t.anid&&(n[t.anid]=t)}),r=n,e.traverse(function(t){var e,n;Zh(t)&&t.anid&&(e=r[t.anid])&&(n=o(t),t.attr(o(e)),Th(t,n,i,Us(t).dataIndex))}))}function Kh(t,n){return z(t,function(t){var e=t[0],e=Ph(e,n.x),t=(e=Lh(e,n.x+n.width),t[1]),t=Ph(t,n.y);return[e,Lh(t,n.y+n.height)]})}function $h(t,e){var n=Ph(t.x,e.x),i=Lh(t.x+t.width,e.x+e.width),r=Ph(t.y,e.y),t=Lh(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}}function Qh(t,e,n){var e=L({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),B(i,n),new vs(e)):Fh(t.replace("path://",""),e,n,"center")}function Jh(t,e,n,i,r,o,a,s){var l,n=n-t,i=i-e,a=a-r,s=s-o,u=a*i-n*s;return!((l=u)<=1e-6&&-1e-6<=l||(r=((l=t-r)*i-n*(t=e-o))/u)<0||1<r||(i=(l*s-a*t)/u)<0||1<i)}function tc(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,e=V(e)?{formatter:e}:e,r=n.mainType,n=n.componentIndex,o={componentType:r,name:i,$vars:["name"]},a=(o[r+"Index"]=n,t.formatterParamsExtra),t=(a&&O(ct(a),function(t){Vt(o,t)||(o[t]=a[t],o.$vars.push(t))}),Us(t.el));t.componentMainType=r,t.componentIndex=n,t.tooltipConfig={name:i,option:B({content:i,encodeHTMLContent:!0,formatterParams:o},e)}}function ec(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function nc(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)ec(t[n],e);else ec(t,e)}Bh("circle",su),Bh("ellipse",cu),Bh("sector",ku),Bh("ring",Lu),Bh("polygon",Bu),Bh("polyline",Hu),Bh("rect",Is),Bh("line",qu),Bh("bezierCurve",Qu),Bh("arc",nh);var ic=Object.freeze({__proto__:null,Arc:nh,BezierCurve:Qu,BoundingRect:X,Circle:su,CompoundPath:oh,Ellipse:cu,Group:Wr,Image:vs,IncrementalDisplayable:n,Line:qu,LinearGradient:hh,OrientedBoundingRect:vh,Path:hs,Point:M,Polygon:Bu,Polyline:Hu,RadialGradient:sh,Rect:Is,Ring:Lu,Sector:ku,Text:Ls,applyTransform:Yh,clipPointsByRect:Kh,clipRectByRect:$h,createIcon:Qh,extendPath:Nh,extendShape:Rh,getShapeClass:zh,getTransform:qh,groupTransition:jh,initProps:Ch,isElementRemoved:Ih,lineLineIntersect:Jh,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(Jh(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Vh,makePath:Fh,mergePath:Wh,registerShape:Bh,removeElement:kh,removeElementWithFadeOut:function(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){t.isGroup||Dh(t,e,n,i)}):Dh(t,e,n,i)},resizePath:Gh,setTooltipConfig:tc,subPixelOptimize:Xh,subPixelOptimizeLine:Uh,subPixelOptimizeRect:function(t){return bs(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=Yh(["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},traverseElements:nc,updateProps:Th}),rc={};function oc(t,e){for(var n=0;n<Qs.length;n++){var i=Qs[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function ac(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?k(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<Qs.length;u++){var h=Qs[u],c=e[h];l[h]=E(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function sc(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||rc;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||rc).rich;if(n){e=e||{};for(var i=ct(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),pc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),pc(s,l,d,u,h,c,!0,!1),e&&L(a,e),a}function lc(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=E(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var uc=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],hc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],cc=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function pc(t,e,n,i,r,o,a,s){n=!r&&n||rc;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=E(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),E(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),E(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),E(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<uc.length;p++){var d=uc[p];null!=(f=E(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<hc.length;p++){d=hc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<cc.length;p++){var f,d=cc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var dc=Do();function fc(n,i,r,t,o){var a,s,l,u=dc(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=E(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?Ch:Th)(n,{percent:1},t,i,null,function(t){var e=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(gt(i))return to(p=go(n||0,i,r),o?Math.max(eo(n||0),eo(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=go(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=to(p,o?Math.max(eo(d),eo(c)):e))}return a}(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,ac({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));oc(n,t)}))}var gc=["textStyle","color"],yc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],mc=new Ls,Xh=(vc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(gc):null)},vc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),kt([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},vc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<yc.length;n++)e[yc[n]]=this.getShallow(yc[n]);return mc.useStyle(e),mc.update(),mc.getBoundingRect()},vc);function vc(){}var _c=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],xc=qo(_c),wc=(bc.prototype.getLineStyle=function(t){return xc(this,t)},bc);function bc(){}var Sc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Mc=qo(Sc),Tc=(Cc.prototype.getItemStyle=function(t,e){return Mc(this,t,e)},Cc);function Cc(){}Dc.prototype.init=function(t,e,n){},Dc.prototype.mergeOption=function(t,e){d(this.option,t,!0)},Dc.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},Dc.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},Dc.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new Dc(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},Dc.prototype.isEmpty=function(){return null==this.option},Dc.prototype.restoreData=function(){},Dc.prototype.clone=function(){return new this.constructor(S(this.option))},Dc.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},Dc.prototype.resolveParentPath=function(t){return t},Dc.prototype.isAnimationEnabled=function(){if(!b.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},Dc.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var Ic,kc=Dc;function Dc(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Vo(kc),Uc=kc,Ic=["__\0is_clz",Wo++].join("_"),Uc.prototype[Ic]=!0,Uc.isInstance=function(t){return!(!t||!t[Ic])},at(kc,wc),at(kc,Tc),at(kc,Zo),at(kc,Xh);var Ac=Math.round(10*Math.random());function Pc(t){return[t||"",Ac++].join("_")}var Lc="ZH",Oc="EN",Rc=Oc,Ec={},Nc={},Bc=b.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Rc).toUpperCase().indexOf(Lc)?Lc:Rc;function zc(t,e){t=t.toUpperCase(),Nc[t]=new kc(e),Ec[t]=e}zc(Oc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),zc(Lc,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Fc=1e3,Vc=60*Fc,Hc=60*Vc,Wc=24*Hc,Wo=365*Wc,Gc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Uc="{yyyy}-{MM}-{dd}",Xc={year:"{yyyy}",month:"{yyyy}-{MM}",day:Uc,hour:Uc+" "+Gc.hour,minute:Uc+" "+Gc.minute,second:Uc+" "+Gc.second,millisecond:Gc.none},qc=["year","month","day","hour","minute","second","millisecond"],Yc=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Zc(t,e){return"0000".substr(0,e-(t+="").length)+t}function jc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Kc(t,e,n,i){var t=so(t),r=t[Jc(n)](),o=t[tp(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[ep(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[np(n)](),h=(u-1)%12+1,c=t[ip(n)](),p=t[rp(n)](),t=t[op(n)](),n=12<=u?"pm":"am",d=n.toUpperCase(),i=(i instanceof kc?i:Nc[i||Bc]||Nc[Rc]).getModel("time"),f=i.get("month"),g=i.get("monthAbbr"),y=i.get("dayOfWeek"),i=i.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,n+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,Zc(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,f[o-1]).replace(/{MMM}/g,g[o-1]).replace(/{MM}/g,Zc(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Zc(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,y[l]).replace(/{ee}/g,i[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Zc(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Zc(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Zc(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Zc(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Zc(t,3)).replace(/{S}/g,t+"")}function $c(t,e){var t=so(t),n=t[tp(e)]()+1,i=t[ep(e)](),r=t[np(e)](),o=t[ip(e)](),a=t[rp(e)](),t=0===t[op(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function Qc(t,e,n){var i=gt(t)?so(t):t;switch(e=e||$c(t,n)){case"year":return i[Jc(n)]();case"half-year":return 6<=i[tp(n)]()?1:0;case"quarter":return Math.floor((i[tp(n)]()+1)/4);case"month":return i[tp(n)]();case"day":return i[ep(n)]();case"half-day":return i[np(n)]()/24;case"hour":return i[np(n)]();case"minute":return i[ip(n)]();case"second":return i[rp(n)]();case"millisecond":return i[op(n)]()}}function Jc(t){return t?"getUTCFullYear":"getFullYear"}function tp(t){return t?"getUTCMonth":"getMonth"}function ep(t){return t?"getUTCDate":"getDate"}function np(t){return t?"getUTCHours":"getHours"}function ip(t){return t?"getUTCMinutes":"getMinutes"}function rp(t){return t?"getUTCSeconds":"getSeconds"}function op(t){return t?"getUTCMilliseconds":"getMilliseconds"}function ap(t){return t?"setUTCMonth":"setMonth"}function sp(t){return t?"setUTCDate":"setDate"}function lp(t){return t?"setUTCHours":"setHours"}function up(t){return t?"setUTCMinutes":"setMinutes"}function hp(t){return t?"setUTCSeconds":"setSeconds"}function cp(t){return t?"setUTCMilliseconds":"setMilliseconds"}function pp(t){var e;return po(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function dp(t,e){return"{"+t+(null==e?"":e)+"}"}var fp=Ct,gp=["a","b","c","d","e","f","g"];function yp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=gp[o];t=t.replace(dp(a),dp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(dp(gp[l],s),n?xe(u):u)}return t}var mp=O,vp=["left","right","top","bottom","width","height"],_p=[["width","left","right"],["height","top","bottom"]];function xp(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}function wp(t){t=t.layoutMode||t.constructor.layoutMode;return R(t)?t:t?{type:t}:null}function bp(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(_p[0],0)),e=n(_p[1],1);function n(t,e){var n={},i=0,r={},o=0;if(mp(t,function(t){r[t]=l[t]}),mp(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){mp(t,function(t){e[t]=n[t]})}i(_p[0],l,t),i(_p[1],l,e)}function Sp(t){return e={},(n=t)&&e&&mp(vp,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}dt(xp,"vertical"),dt(xp,"horizontal");var Mp,Tp,Cp,Ip,kp=Do(),y=(u(Dp,Mp=kc),Dp.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Dp.prototype.mergeDefaultAndTheme=function(t,e){var n=wp(this),i=n?Sp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&bp(t,i,n)},Dp.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=wp(this);n&&bp(this.option,t,n)},Dp.prototype.optionUpdated=function(t,e){},Dp.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[zo])return t.defaultOption;var e=kp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Dp.prototype.getReferringComponents=function(t,e){var n=t+"Id";return Ro(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Dp.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Dp.prototype.getZLevelKey=function(){return""},Dp.prototype.setZLevel=function(t){this.option.zlevel=t},Dp.protoInitialize=((wc=Dp.prototype).type="component",wc.id="",wc.name="",wc.mainType="",wc.subType="",void(wc.componentIndex=0)),Dp);function Dp(t,e,n){t=Mp.call(this,t,e,n)||this;return t.uid=Pc("ec_cpt_model"),t}function Ap(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Ho(y,kc),Xo(y),Cp={},(Tp=y).registerSubTypeDefaulter=function(t,e){t=Fo(t);Cp[t.main]=e},Tp.determineSubType=function(t,e){var n,i=e.type;return i||(n=Fo(t).main,Tp.hasSubTypes(t)&&Cp[n]&&(i=Cp[n](e))),i},Ip=function(t){var e=[];O(y.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=z(e,function(t){return Fo(t).main}),"dataset"!==t&&I(e,"dataset")<=0&&e.unshift("dataset");return e},y.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],O(o=e,function(n){var e,i,r=Ap(a,n),t=r.originalDeps=Ip(n),t=(e=o,i=[],O(t,function(t){0<=I(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),O(t,function(t){I(r.predecessor,t)<0&&r.predecessor.push(t);var e=Ap(a,t);I(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(O(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var Tc="",Zo=("undefined"!=typeof navigator&&(Tc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Pp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Zo,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Zo,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Zo,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Zo,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Zo,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Zo,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Tc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Lp=N(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Op="original",Rp="arrayRows",Ep="objectRows",Np="keyedColumns",Bp="typedArray",zp="unknown",Fp="column",Vp="row",Hp={Must:1,Might:2,Not:3},Wp=Do();function Gp(n,t,e){var r,o,a,i,s,l={},u=Up(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=Wp(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,O(n=n.slice(),function(t,e){t=R(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),O(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function Up(t){if(!t.get("data",!0))return Ro(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Oo).models[0]}function Xp(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!mt(o)){if(l&&(R(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?Hp.Must:Hp.Not;if(a===Rp){var c=o;if(s===Vp){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===Ep){var g=o;if(!i)return Hp.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===Np){l=o;if(!i)return Hp.Not;if(!(p=l[i])||mt(p))return Hp.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===Op)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=xo(m=y[d]);if(!F(v))return Hp.Not;if(null!=(n=_(v[h])))return n}}return Hp.Not;function _(t){var e=V(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?Hp.Might:Hp.Not:e&&"-"!==t?Hp.Must:void 0}}var qp=N();var Yp,Zp,jp,Kp=Do(),$p=(Do(),Qp.prototype.getColorFromPalette=function(t,e,n){var i=mo(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=Kp;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:Jp(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},Qp.prototype.clearColorPalette=function(){var t,e;(e=Kp)(t=this).paletteIdx=0,e(t).paletteNameMap={}},Qp);function Qp(){}function Jp(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var td,ed="\0_ec_inner",nd=(u(h,td=kc),h.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new kc(i),this._locale=new kc(r),this._optionManager=o},h.prototype.setOption=function(t,e,n){e=od(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},h.prototype.resetOption=function(t,e){return this._resetOption(t,od(e))},h.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):jp(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&O(n,function(t){i=!0,this._mergeOption(t,e)},this),i},h.prototype.mergeOption=function(t){this._mergeOption(t,null)},h.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=N(),p=t&&t.replaceMergeMainTypeMap;Wp(this).datasetMap=N(),O(i,function(t,e){null!=t&&(y.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?S(t):d(r[e],t,!0))}),p&&p.each(function(t,e){y.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),y.topologicalTravel(n,y.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=qp.get(e))&&e(t))?n.concat(e):n}(this,o,mo(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=wo(e,t,n),s=(Io(e,o,y),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;O(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=y.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=L({componentIndex:e},t.keyInfo),L(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&Yp(this)},this),this._seriesIndices||Yp(this)},h.prototype.getOption=function(){var a=S(this.option);return O(a,function(t,e){if(y.hasClass(e)){for(var n=mo(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!Co(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[ed],a},h.prototype.getTheme=function(){return this._theme},h.prototype.getLocaleModel=function(){return this._locale},h.prototype.setUpdatePayload=function(t){this._payload=t},h.prototype.getUpdatePayload=function(){return this._payload},h.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},h.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],O(mo(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?id("id",n,r):null!=i?id("name",i,r):ut(r,function(t){return!!t}),rd(o,t)):[]},h.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=rd(o,t),t.filter?ut(n,t.filter):n},h.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(k(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):R(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},h.prototype.getSeriesByName=function(t){var e=Mo(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},h.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},h.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},h.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},h.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},h.prototype.eachSeries=function(n,i){Zp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},h.prototype.eachRawSeries=function(e,n){O(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},h.prototype.eachSeriesByType=function(n,i,r){Zp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},h.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},h.prototype.isSeriesFiltered=function(t){return Zp(this),null==this._seriesIndicesMap.get(t.componentIndex)},h.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},h.prototype.filterSeries=function(n,i){Zp(this);var r=[];O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=N(r)},h.prototype.restoreData=function(n){Yp(this);var t=this._componentsMap,i=[];t.each(function(t,e){y.hasClass(e)&&i.push(e)}),y.topologicalTravel(i,y.getAllClassMainTypes(),function(e){O(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},h.internalField=(Yp=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=N(e)},Zp=function(t){},void(jp=function(t,e){t.option={},t.option[ed]=1,t._componentsMap=N({series:[]}),t._componentsCount=N();var n,i,r=e.aria;R(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,O(r,function(t,e){"colorLayer"===e&&i||y.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):S(t):null==n[e]&&(n[e]=t))}),d(e,Pp,!1),t._mergeOption(e,null)})),h);function h(){return null!==td&&td.apply(this,arguments)||this}function id(e,t,n){var i,r;return F(t)?(i=N(),O(t,function(t){null!=t&&null!=Mo(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=Mo(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function rd(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function od(t){var e=N();return t&&O(mo(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(nd,$p);function ad(e){O(sd,function(t){this[t]=pt(e[t],e)},this)}var sd=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ld={},ud=(hd.prototype.create=function(n,i){var r=[];O(ld,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},hd.prototype.update=function(e,n){O(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},hd.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},hd.register=function(t,e){ld[t]=e},hd.get=function(t){return ld[t]},hd);function hd(){this._coordinateSystems=[]}var cd=/^(min|max)?(.+)$/,pd=(dd.prototype.setOption=function(t,e,n){t&&(O(mo(t.series),function(t){t&&t.data&&mt(t.data)&&At(t.data)}),O(mo(t.dataset),function(t){t&&t.source&&mt(t.source)&&At(t.source)})),t=S(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&O(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){O(n,function(t){t(e,i)})}return p(r),O(l,p),O(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},dd.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],S(t?e.baseOption:this._newBaseOption)},dd.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?S(n[t.getCurrentIndex()]):e},dd.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,function(t,e){var n,e=e.match(cd);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=z(o,function(t){return S((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},dd);function dd(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var fd=O,gd=R,yd=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function md(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=yd.length;n<i;n++){var r=yd[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function vd(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,B(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function _d(t){vd(t,"itemStyle"),vd(t,"lineStyle"),vd(t,"areaStyle"),vd(t,"label"),vd(t,"labelLine"),vd(t,"upperLabel"),vd(t,"edgeLabel")}function xd(t,e){var n=gd(t)&&t[e],i=gd(n)&&n.textStyle;if(i)for(var r=0,o=_o.length;r<o;r++){var a=_o[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function wd(t){t&&(_d(t),xd(t,"label"),t.emphasis)&&xd(t.emphasis,"label")}function bd(t){return F(t)?t:t?[t]:[]}function Sd(t){return(F(t)?t[0]:t)||{}}function Md(e,t){fd(bd(e.series),function(t){if(gd(t))if(gd(t)){md(t),_d(t),xd(t,"label"),xd(t,"upperLabel"),xd(t,"edgeLabel"),t.emphasis&&(xd(t.emphasis,"label"),xd(t.emphasis,"upperLabel"),xd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(md(e),wd(e)),t.markLine),i=(n&&(md(n),wd(n)),t.markArea),r=(i&&wd(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!mt(o))for(var a=0;a<o.length;a++)wd(o[a]);O(t.categories,function(t){_d(t)})}if(r&&!mt(r))for(a=0;a<r.length;a++)wd(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)wd(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(wd(l[a][0]),wd(l[a][1])):wd(l[a]);"gauge"===t.type?(xd(t,"axisLabel"),xd(t,"title"),xd(t,"detail")):"treemap"===t.type?(vd(t.breadcrumb,"itemStyle"),O(t.levels,function(t){_d(t)})):"tree"===t.type&&_d(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),fd(n,function(t){fd(bd(e[t]),function(t){t&&(xd(t,"axisLabel"),xd(t.axisPointer,"label"))})}),fd(bd(e.parallel),function(t){t=t&&t.parallelAxisDefault;xd(t,"axisLabel"),xd(t&&t.axisPointer,"label")}),fd(bd(e.calendar),function(t){vd(t,"itemStyle"),xd(t,"dayLabel"),xd(t,"monthLabel"),xd(t,"yearLabel")}),fd(bd(e.radar),function(t){xd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),fd(bd(e.geo),function(t){gd(t)&&(wd(t),fd(bd(t.regions),function(t){wd(t)}))}),fd(bd(e.timeline),function(t){wd(t),vd(t,"label"),vd(t,"itemStyle"),vd(t,"controlStyle",!0);t=t.data;F(t)&&O(t,function(t){R(t)&&(vd(t,"label"),vd(t,"itemStyle"))})}),fd(bd(e.toolbox),function(t){vd(t,"iconStyle"),fd(t.feature,function(t){vd(t,"iconStyle")})}),xd(Sd(e.axisPointer),"label"),xd(Sd(e.tooltip).axisPointer,"label")}function Td(e){e&&O(Cd,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var Cd=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Id=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],kd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Dd(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<kd.length;n++){var i=kd[n][1],r=kd[n][0];null!=e[i]&&(e[r]=e[i])}}function Ad(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Pd(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Ld(e,t){Md(e,t),e.series=mo(e.series),O(e.series,function(t){if(R(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Ad(t.label),(e=t.data)&&!mt(e))for(var i=0;i<e.length;i++)Ad(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Dd(t),Dd(t.backgroundStyle),Dd(t.emphasis),(e=t.data)&&!mt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(Dd(e[i]),Dd(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Pd(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Pd)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&B(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),Td(t)}}),e.dataRange&&(e.visualMap=e.dataRange),O(Id,function(t){t=e[t];t&&O(t=F(t)?t:[t],function(t){Td(t)})})}function Od(_){O(_,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex,v=p.seriesModel.get("stackStrategy")||"samesign";y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=_[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if("all"===v||"positive"===v&&0<c||"negative"===v&&c<0||"samesign"===v&&0<=o&&0<c||"samesign"===v&&o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(eo(a),eo(s)),a+=s,o=Qr<l?a:to(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var Rd,Ed,Nd=function(t){this.data=t.data||(t.sourceFormat===Np?{}:[]),this.sourceFormat=t.sourceFormat||zp,this.seriesLayoutBy=t.seriesLayoutBy||Fp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Xp(this,n)===Hp.Must&&(i.type="ordinal")}};function Bd(t){return t instanceof Nd}function zd(t,e,n){n=n||Vd(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Hd(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===Rp?(s=t,"auto"===i||null==i?Wd(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=gt(i)?i:i?1:0,r||1!==a||(r=[],Wd(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===Vp?s.length:s[0]?s[0].length:null):e===Ep?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return ct(e)}(t):e===Np?r||(r=[],O(t,function(t,e){r.push(e)})):e===Op&&(i=xo(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:Hd(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Nd({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:S(e)})}function Fd(t){return new Nd({data:t,sourceFormat:mt(t)?Bp:Op})}function Vd(t){var e=zp;if(mt(t))e=Bp;else if(F(t)){0===t.length&&(e=Rp);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)||mt(r)){e=Rp;break}if(R(r)){e=Ep;break}}}}else if(R(t))for(var o in t)if(Vt(t,o)&&st(t[o])){e=Np;break}return e}function Hd(t){var i;if(t)return i=N(),z(t,function(t,e){var n,t={name:(t=R(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Wd(t,e,n,i){if(e===Vp)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function Gd(t){t=t.sourceFormat;return t===Ep||t===Np}Zd.prototype.getSource=function(){return this._source},Zd.prototype.count=function(){return 0},Zd.prototype.getItem=function(t,e){},Zd.prototype.appendData=function(t){},Zd.prototype.clean=function(){},Zd.protoInitialize=((Xh=Zd.prototype).pure=!1,void(Xh.persistent=!0)),Zd.internalField=(Ed=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;L(t,Rd[af(r,o)]),r===Bp?(t.getItem=Ud,t.count=qd,t.fillStorage=Xd):(i=Qd(r,o),t.getItem=pt(i,null,e,a,n),i=ef(r,o),t.count=pt(i,null,e,a,n))},Ud=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},Xd=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},qd=function(){return this._data?this._data.length/this._dimSize:0},(Xh={})[Rp+"_"+Fp]={pure:!0,appendData:jd},Xh[Rp+"_"+Vp]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},Xh[Ep]={pure:!0,appendData:jd},Xh[Np]={pure:!0,appendData:function(t){var r=this._data;O(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},Xh[Op]={appendData:jd},Xh[Bp]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Rd=Xh));var Ud,Xd,qd,Yd=Zd;function Zd(t,e){var t=Bd(t)?t:Fd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===Bp&&(this._offset=0,this._dimSize=e,this._data=n),Ed(this,n,t)}function jd(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Kd(t,e,n,i){return t[i]}(Uc={})[Rp+"_"+Fp]=function(t,e,n,i){return t[i+e]},Uc[Rp+"_"+Vp]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},Uc[Ep]=Kd,Uc[Np]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},Uc[Op]=Kd;var $d=Uc;function Qd(t,e){return $d[af(t,e)]}function Jd(t,e,n){return t.length}(wc={})[Rp+"_"+Fp]=function(t,e,n){return Math.max(0,t.length-e)},wc[Rp+"_"+Vp]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},wc[Ep]=Jd,wc[Np]=function(t,e,n){t=t[n[0].name];return t?t.length:0},wc[Op]=Jd;var tf=wc;function ef(t,e){return tf[af(t,e)]}function nf(t,e,n){return t[e]}(Zo={})[Rp]=nf,Zo[Ep]=function(t,e,n){return t[n]},Zo[Np]=nf,Zo[Op]=function(t,e,n){t=xo(t);return t instanceof Array?t[e]:t},Zo[Bp]=nf;var rf=Zo;function of(t){return rf[t]}function af(t,e){return t===Rp?t+"_"+e:t}function sf(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),of(i)(e,t,n)):(r=e,i===Op?xo(e):r)}}var lf=/\{@(.+?)\}/g,Tc=(uf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},uf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),k(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?yp(r,e).replace(lf,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),sf(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},uf.prototype.getRawValue=function(t,e){return sf(this.getData(e),t)},uf.prototype.formatTooltip=function(t,e,n){},uf);function uf(){}function hf(t){return new cf(t)}pf.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},pf.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},pf.prototype._doProgress=function(t,e,n,i,r){_f.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:_f.next},this.context)},pf.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},pf.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},pf.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},pf.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},pf.prototype.getUpstream=function(){return this._upstream},pf.prototype.getDownstream=function(){return this._downstream},pf.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var cf=pf;function pf(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var df,ff,gf,yf,mf,vf,_f=vf={reset:function(t,e,n,i){ff=t,df=e,gf=n,yf=i,mf=Math.ceil(yf/gf),vf.next=1<gf&&0<yf?wf:xf}};function xf(){return ff<df?ff++:null}function wf(){var t=ff%mf*gf+Math.ceil(ff/mf),t=df<=ff?null:t<yf?t:ff;return ff++,t}function bf(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||gt(t)||null==t||"-"===t?t:+so(t))||""===t?NaN:Number(t)}var Sf=N({number:function(t){return parseFloat(t)},time:function(t){return+so(t)},trim:function(t){return V(t)?kt(t):t}});function Mf(t){return Sf.get(t)}var Tf={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},Cf=(If.prototype.evaluate=function(t){return gt(t)?this._opFn(t,this._rvalFloat):this._opFn(co(t),this._rvalFloat)},If);function If(t,e){gt(e)||g(""),this._opFn=Tf[t],this._rvalFloat=co(e)}Df.prototype.evaluate=function(t,e){var n=gt(t)?t:co(t),i=gt(e)?e:co(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var kf=Df;function Df(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}Pf.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=co(t)===this._rvalFloat),this._isEQ?n:!n};var Af=Pf;function Pf(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=co(e)}Of.prototype.getRawData=function(){throw new Error("not supported")},Of.prototype.getRawDataItem=function(t){throw new Error("not supported")},Of.prototype.cloneRawData=function(){},Of.prototype.getDimensionInfo=function(t){},Of.prototype.cloneAllDimensionInfo=function(){},Of.prototype.count=function(){},Of.prototype.retrieveValue=function(t,e){},Of.prototype.retrieveValueFromItem=function(t,e){},Of.prototype.convertValue=bf;var Lf=Of;function Of(){}function Rf(t){return Vf(t.sourceFormat)||g(""),t.data}function Ef(t){var e=t.sourceFormat,n=t.data;if(Vf(e)||g(""),e===Rp){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===Ep){for(i=[],r=0,o=n.length;r<o;r++)i.push(L({},n[r]));return i}}function Nf(t,e,n){if(null!=n)return gt(n)||!isNaN(n)&&!Vt(e,n)?t[n]:Vt(e,n)?e[n]:void 0}function Bf(t){return S(t)}var zf=N();function Ff(t,e){var n=mo(t),t=n.length;t||g("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||g("");R(t)||g("");var e=t.type,d=zf.get(e);d||g("");e=z(i,function(t){var e=t,t=d,n=new Lf,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Fp&&g(""),[]),s={};if(h=e.dimensionsDefine)O(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(Vt(s,n)&&g(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=Qd(r,Fp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=pt(Rf,null,e)),n.cloneRawData=pt(Ef,null,e),ef(r,Fp)),c=(n.count=pt(h,null,i,o,a),of(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=pt(Nf,null,a,s),n.cloneAllDimensionInfo=pt(Bf,null,a),n});return z(mo(d.transform({upstream:e[0],upstreamList:e,config:S(t.config)})),function(t,e){R(t)||g(""),t.data||g("");Vf(Vd(t.data))||g("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Fp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Fp,sourceHeader:0,dimensions:t.dimensions};return zd(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function Vf(t){return t===Rp||t===Ep}var Hf,Xh="undefined",Wf=typeof Uint32Array==Xh?Array:Uint32Array,Gf=typeof Uint16Array==Xh?Array:Uint16Array,Uf=typeof Int32Array==Xh?Array:Int32Array,Uc=typeof Float64Array==Xh?Array:Float64Array,Xf={float:Uc,int:Uf,ordinal:Array,number:Array,time:Uc};function qf(t){return 65535<t?Wf:Gf}function Yf(){return[1/0,-1/0]}function Zf(t,e,n,i,r){n=Xf[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}c.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Hf[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],Gd(i),this._dimensions=z(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},c.prototype.getProvider=function(){return this._provider},c.prototype.getSource=function(){return this._provider.getSource()},c.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new Xf[e||"float"](this._rawCount),this._rawExtent[r]=Yf(),r},c.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=Yf()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},c.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},c.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},c.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},c.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)Zf(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Hf.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},c.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=z(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=Yf()),Zf(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},c.prototype.count=function(){return this._count},c.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},c.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},c.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},c.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},c.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},c.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},c.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},c.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=qf(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},c.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(qf(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},c.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=ct(t),r=i.length;if(!r)return this;var o=e.count(),a=new(qf(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,x=e.getRawIndex(f),b=0;b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},c.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},c.prototype.modify=function(t,e){this._updateDims(this,t,e)},c.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=Yf();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},c.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(qf(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,b=-1,S=0,g=c;g<m;g++){var M,y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,b<0&&(b=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=M)}0<S&&S<m-c&&(u[a++]=Math.min(b,w),w=Math.max(b,w)),l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},c.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,r=Math.floor(1/e),o=i[t],a=this.count(),s=new(qf(this._rawCount))(2*Math.ceil(a/r)),l=0,u=0;u<a;u+=r){var h=u,c=o[this.getRawIndex(h)],p=u,d=o[this.getRawIndex(p)],f=r;a<u+r&&(f=a-u);for(var g=0;g<f;g++){var y=o[this.getRawIndex(u+g)];y<c&&(c=y,h=u+g),d<y&&(d=y,p=u+g)}var m=this.getRawIndex(h),v=this.getRawIndex(p);h<p?(s[l++]=m,s[l++]=v):(s[l++]=v,s[l++]=m)}return n._count=l,n._indices=s,n._updateGetRawIdx(),n},c.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=Yf(),c=new(qf(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},c.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},c.prototype.getDataExtent=function(t){var e=this._chunks[t],n=Yf();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},c.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},c.prototype.clone=function(t,e){var n,i,r=new c,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},c.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=S(this._extent),t._rawExtent=S(this._rawExtent)},c.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},c.prototype._getRawIdxIdentity=function(t){return t},c.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},c.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},c.internalField=void(Hf={arrayRows:Kf,objectRows:function(t,e,n,i){return bf(t[e],this._dimensions[i])},keyedColumns:Kf,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return bf(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var jf=c;function c(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=N()}function Kf(t,e,n,i){return bf(t[i],this._dimensions[i])}Qf.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},Qf.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},Qf.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},Qf.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},Qf.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;tg(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=mt(o=i.get("data",!0))?Bp:Op,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=E(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=E(i.sourceHeader,r.sourceHeader),i=E(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[zd(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[zd(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},Qf.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&eg(""),[]),a=[];return O(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||eg(""),o.push(e),a.push(t._getVersionSign())}),i?e=Ff(i,o,n.componentIndex):null!=r&&(e=[new Nd({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:S(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},Qf.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},Qf.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},Qf.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},Qf.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],tg(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new jf).initData(new Yd(e,t.length),t),o[n]=r),r},Qf.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return tg(e)?(t=Up(e))?[t.getSourceManager()]:[]:z((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?Ro(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Oo).models:[],function(t){return t.getSourceManager()})},Qf.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return tg(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var $f=Qf;function Qf(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function Jf(t){t.option.transform&&At(t.option.transform)}function tg(t){return"series"===t.mainType}function eg(t){throw new Error(t)}function ng(t,e){return e.type=t,e}function ig(t,e){var n,e=t.getData().getItemVisual(e,"style")[t.visualDrawType];return n=n||"transparent",V(t=e)?t:R(t)&&t.colorStops&&(t.colorStops[0]||{}).color||n}function rg(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=ig(p,d);function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(ng("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(i=m,r=d,o=g,a=_,s=p.getData(),l=lt(i,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?O(o,function(t){x(sf(s,r,t),t)}):O(i,x),i=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,e=o.blocks,n=(o=o.inlineValues)[0]):y?(y=f.getDimensionInfo(g[0]),n=o=sf(f,d,g[0]),i=y.type):n=o=v?m[0]:m;g=To(p),y=g&&p.name||"",v=f.getName(d),m=t?y:v;return ng("section",{header:y,noHeader:t||!g,sortParam:n,blocks:[ng("nameValue",{markerType:"item",markerColor:_,name:m,noName:!kt(m),value:o,valueType:i,dataIndex:d})].concat(e||[])})}var og=Do();function ag(t,e){return t.getName(e)||t.getId(e)}u(p,sg=y),p.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=hf({count:hg,reset:cg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(og(this).sourceManager=new $f(this)).prepareSource();t=this.getInitialData(t,n);dg(t,this),this.dataTask.context.data=t,og(this).dataBeforeProcessed=t,ug(this),this._initSelectedMapFromData(t)},p.prototype.mergeDefaultAndTheme=function(t,e){var n=wp(this),i=n?Sp(t):{},r=this.subType;y.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),vo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&bp(t,i,n)},p.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=wp(this),n=(n&&bp(this.option,t,n),og(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));dg(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,og(this).dataBeforeProcessed=n,ug(this),this._initSelectedMapFromData(n)},p.prototype.fillDataTextStyle=function(t){if(t&&!mt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&vo(t[n],"label",e)},p.prototype.getInitialData=function(t,e){},p.prototype.appendData=function(t){this.getRawData().appendData(t.data)},p.prototype.getData=function(t){var e=gg(this);return e?(e=e.context.data,null!=t&&e.getLinkedData?e.getLinkedData(t):e):og(this).data},p.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},p.prototype.setData=function(t){var e,n=gg(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),og(this).data=t},p.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return N(t)},p.prototype.getSourceManager=function(){return og(this).sourceManager},p.prototype.getSource=function(){return this.getSourceManager().getSource()},p.prototype.getRawData=function(){return og(this).dataBeforeProcessed},p.prototype.getColorBy=function(){return this.get("colorBy")||"series"},p.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},p.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},p.prototype.formatTooltip=function(t,e,n){return rg({series:this,dataIndex:t,multipleSeries:e})},p.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!b.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},p.prototype.restoreData=function(){this.dataTask.dirty()},p.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return $p.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},p.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},p.prototype.getProgressive=function(){return this.get("progressive")},p.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},p.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},p.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=ag(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},p.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},p.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=ct(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},p.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[ag(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},p.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},p.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){R(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=ag(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=ag(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},p.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},p.registerClass=function(t){return y.registerClass(t)},p.protoInitialize=((wc=p.prototype).type="series.__base__",wc.seriesIndex=0,wc.ignoreStyleOnData=!1,wc.hasSymbolVisual=!1,wc.defaultSymbol="circle",wc.visualStyleAccessPath="itemStyle",void(wc.visualDrawType="fill"));var sg,lg=p;function p(){var t=null!==sg&&sg.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function ug(t){var e,n,i=t.name;To(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],O(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function hg(t){return t.model.getRawData().count()}function cg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),pg}function pg(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function dg(e,n){O(Bt(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,dt(fg,n))})}function fg(t,e){t=gg(t);return t&&t.setOutputEnd((e||this).count()),e}function gg(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(lg,Tc),at(lg,$p),Ho(lg,y);mg.prototype.init=function(t,e){},mg.prototype.render=function(t,e,n,i){},mg.prototype.dispose=function(t,e){},mg.prototype.updateView=function(t,e,n,i){},mg.prototype.updateLayout=function(t,e,n,i){},mg.prototype.updateVisual=function(t,e,n,i){},mg.prototype.toggleBlurSeries=function(t,e,n){},mg.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var yg=mg;function mg(){this.group=new Wr,this.uid=Pc("viewComponent")}Vo(yg),Xo(yg);var vg,_g=Do(),xg=(vg=Do(),function(t){var e=vg(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}),wg=(bg.prototype.init=function(t,e){},bg.prototype.render=function(t,e,n,i){},bg.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Mg(t,i,"emphasis")},bg.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Mg(t,i,"normal")},bg.prototype.remove=function(t,e){this.group.removeAll()},bg.prototype.dispose=function(t,e){},bg.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},bg.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},bg.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},bg.prototype.eachRendered=function(t){nc(this.group,t)},bg.markUpdateMethod=function(t,e){_g(t).updateMethod=e},bg.protoInitialize=void(bg.prototype.type="chart"),bg);function bg(){this.group=new Wr,this.uid=Pc("viewChart"),this.renderTask=hf({plan:Tg,reset:Cg}),this.renderTask.context={view:this}}function Sg(t,e,n){t&&Bl(t)&&("emphasis"===e?xl:wl)(t,n)}function Mg(e,t,n){var i,r=ko(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=qs[t])&&Xs<=32?qs[t]=Xs++:i):null;null!=r?O(mo(r),function(t){Sg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){Sg(t,n,o)})}function Tg(t){return xg(t.model)}function Cg(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&_g(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),Ig[o]}Vo(wg),Xo(wg);var Ig={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},kg="\0__throttleOriginMethod",Dg="\0__throttleRate",Ag="\0__throttleType";function Pg(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}var Lg=Do(),Og={itemStyle:qo(Sc,!0),lineStyle:qo(_c,!0)},Rg={lineStyle:"stroke",itemStyle:"fill"};function Eg(t,e){t=t.visualStyleMapper||Og[e];return t||(console.warn("Unknown style type '"+e+"'."),Og.itemStyle)}function Ng(t,e){t=t.visualDrawType||Rg[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var Zo={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=Eg(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),Ng(r,n)),i=o[a],s=k(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||k(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||k(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=L({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},Bg=new kc,Xh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Eg(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(Bg.option=n[i],n=r(Bg),L(t.ensureUniqueItemVisual(e,"style"),n),Bg.option.decal&&(t.setItemVisual(e,"decal",Bg.option.decal),Bg.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},Uc={performRawSeries:!0,overallReset:function(e){var i=N();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),Lg(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=Lg(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=Ng(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},zg=Math.PI;Vg.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},Vg.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},Vg.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},Vg.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},Vg.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=N();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},Vg.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;O(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});It(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},Vg.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},Vg.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},Vg.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},Vg.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},Vg.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},Vg.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},Vg.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},Vg.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=N(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||hf({plan:Xg,reset:qg,count:jg}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},Vg.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||hf({reset:Hg}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=N(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,hf({reset:Wg,onDirty:Ug})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}It(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,O(n.getSeries(),c)),h&&o.dirty()},Vg.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},Vg.wrapStageHandler=function(t,e){return(t=k(t)?{overallReset:t,seriesType:function(t){Kg=null;try{t($g,Qg)}catch(t){}return Kg}(t)}:t).uid=Pc("stageHandler"),e&&(t.visualType=e),t};var Fg=Vg;function Vg(t,e,n,i){this._stageTaskMap=N(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function Hg(t){t.overallReset(t.ecModel,t.api,t.payload)}function Wg(t){return t.overallProgress&&Gg}function Gg(){this.agent.dirty(),this.getDownstream().dirty()}function Ug(){this.agent&&this.agent.dirty()}function Xg(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function qg(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=mo(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?z(t,function(t,e){return Zg(e)}):Yg}var Yg=Zg(0);function Zg(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function jg(t){return t.data.count()}var Kg,$g={},Qg={};function Jg(t,e){for(var n in e.prototype)t[n]=Ht}Jg($g,nd),Jg(Qg,ad),$g.eachSeriesByType=$g.eachRawSeriesByType=function(t){Kg=t},$g.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Kg=t.subType)};function ty(){return{axisLine:{lineStyle:{color:ey}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var wc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Tc={color:wc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],wc]},ey="#B9B8CE",Sc="#100C2A",_c=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],wc={darkMode:!0,color:_c,backgroundColor:Sc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:ey},pageTextStyle:{color:ey}},textStyle:{color:ey},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:ey}},dataZoom:{borderColor:"#71708A",textStyle:{color:ey},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:ey}},timeline:{lineStyle:{color:ey},label:{color:ey},controlStyle:{color:ey,borderColor:ey}},calendar:{itemStyle:{color:Sc},dayLabel:{color:ey},monthLabel:{color:ey},yearLabel:{color:ey}},timeAxis:ty(),logAxis:ty(),valueAxis:ty(),categoryAxis:ty(),line:{symbol:"circle"},graph:{color:_c},gauge:{title:{color:ey},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:ey},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},ny=(wc.categoryAxis.splitLine.show=!1,iy.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Fo(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},O(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},iy.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},iy.prototype.afterTrigger=function(){this.eventInfo=null},iy);function iy(){}var ry=["symbol","symbolSize","symbolRotate","symbolOffset"],oy=ry.concat(["symbolKeepAspect"]),Sc={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<ry.length;r++){var o=ry[r],u=a.get(o);k(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(L({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=ct(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},_c={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<oy.length;i++){var r=oy[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function ay(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=ko(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:L({},r)}))})}function sy(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var ly=Math.round(9*Math.random()),uy="function"==typeof Object.defineProperty,hy=(cy.prototype.get=function(t){return this._guard(t)[this._id]},cy.prototype.set=function(t,e){t=this._guard(t);return uy?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},cy.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},cy.prototype.has=function(t){return!!this._guard(t)[this._id]},cy.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},cy);function cy(){this._id="__ec_inner_"+ly++}var py=hs.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),dy=hs.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),fy=hs.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),gy=hs.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),yy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},my={},vy=(O({line:qu,rect:Is,roundRect:Is,square:Is,circle:su,diamond:dy,pin:fy,arrow:gy,triangle:py},function(t,e){my[e]=new t}),hs.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=Dr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=my[r])||my[r="rect"],yy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function _y(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function xy(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Vh(t.slice(8),new X(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Fh(t.slice(7),{},new X(e,n,i,r),a?"center":"cover"):new vy({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=_y,o&&a.setColor(o),a}function wy(t){return isFinite(t)}function by(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=wy(u)?u:.5,h=wy(h)?h:.5,c=0<=c&&wy(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=wy(o)?o:0,l=wy(l)?l:1,i=wy(i)?i:0,u=wy(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function Sy(t){return parseInt(t,10)}function My(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||Sy(n[i])||Sy(t.style[i]))-(Sy(n[o])||0)-(Sy(n[e])||0)|0)}function Ty(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:gt(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=z(i,function(t){return t/e}),r/=e),[i,r]}var Cy=new qa(!0);function Iy(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function ky(t){return"string"==typeof t&&"none"!==t}function Dy(t){t=t.fill;return null!=t&&"none"!==t}function Ay(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function Py(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function Ly(t,e,n){var n=$o(e.image,e.__image,n);if(Jo(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Wt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var Oy=["shadowBlur","shadowOffsetX","shadowOffsetY"],Ry=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Ey(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(Wy(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?da.opacity:o),!i&&e.blend===n.blend||(a||(Wy(t,r),a=!0),t.globalCompositeOperation=e.blend||da.blend);for(var s=0;s<Oy.length;s++){var l=Oy[s];!i&&e[l]===n[l]||(a||(Wy(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(Wy(t,r),a=!0),t.shadowColor=e.shadowColor||da.shadowColor),a}function Ny(t,e,n,i,r){var o=Gy(e,r.inHover),a=i?null:n&&Gy(n,r.inHover)||{};if(o!==a){var s=Ey(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(Wy(t,r),s=!0),ky(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(Wy(t,r),s=!0),ky(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(Wy(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(Wy(t,r),s=!0),t.lineWidth=n);for(var l=0;l<Ry.length;l++){var u=Ry[l],h=u[0];!i&&o[h]===a[h]||(s||(Wy(t,r),s=!0),t[h]=o[h]||u[1])}}}function By(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var zy=1,Fy=2,Vy=3,Hy=4;function Wy(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Gy(t,e){return e&&t.__hoverStyle||t.style}function Uy(t,e){Xy(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Xy(t,e,n,N){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(Wy(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){Wy(t,n),t.save();for(var B=r,l=t,o=n,z=!1,F=0;F<B.length;F++){var V=B[F],z=z||V.isZeroArea();By(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=z,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,w,H,b,S,M,T,C,I,k,D,A,o=n.prevEl,P=(o||(s=a=!0),e instanceof hs&&e.autoBatch&&(r=e.style,P=Dy(r),u=Iy(r),!(r.lineDash||!(+P^+u)||P&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(Wy(t,n),By(t,e)):P||Wy(t,n),Gy(e,n.inHover));if(e instanceof hs)n.lastDrawType!==zy&&(s=!0,n.lastDrawType=zy),Ny(t,e,o,s,n),P&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=P,b=Iy(p=a),S=Dy(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),I=r.path||Cy,k=r.__dirty,R||(d=p.fill,A=p.stroke,f=S&&!!d.colorStops,g=b&&!!A.colorStops,y=S&&!!d.image,m=b&&!!A.image,D=w=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=k?by(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=k?by(i,A,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=k||!r.__canvasFillPattern?Ly(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(w=k||!r.__canvasStrokePattern?Ly(i,A,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(w?i.strokeStyle=w:b=!1)),D=r.getGlobalScale(),I.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=Ty(r))[0],O=d[1]),A=!0,(C||k&xn)&&(I.setDPR(i.dpr),T?I.setContext(null):(I.setContext(i),A=!1),I.reset(),r.buildPath(I,r.shape,R),I.toStatic(),r.pathUpdated()),A&&I.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(b&&Py(i,p),S&&Ay(i,p)):(S&&Ay(i,p),b&&Py(i,p))),H&&i.setLineDash([]),P&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof ds)n.lastDrawType!==Vy&&(s=!0,n.lastDrawType=Vy),Ny(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||j,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=Ty(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(Iy(y)&&f.strokeText(x,y.x,y.y),Dy(y)&&f.fillText(x,y.x,y.y)):(Dy(y)&&f.fillText(x,y.x,y.y),Iy(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof vs)n.lastDrawType!==Fy&&(s=!0,n.lastDrawType=Fy),m=o,w=s,Ey(t,Gy(e,(D=n).inHover),m&&Gy(m,D.inHover),w,D),d=t,C=a,(r=(k=e).__image=$o(C.image,k.__image,k,k.onload))&&Jo(r)&&(A=C.x||0,I=C.y||0,T=k.getWidth(),k=k.getHeight(),M=r.width/r.height,null==T&&null!=k?T=k*M:null==k&&null!=T?k=T/M:null==T&&null==k&&(T=r.width,k=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,A,I,T,k)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,k-c,A,I,T,k)):d.drawImage(r,A,I,T,k));else if(e.getTemporalDisplayables){n.lastDrawType!==Hy&&(s=!0,n.lastDrawType=Hy);var L,W,G=t,O=e,R=n,U=O.getDisplayables(),X=O.getTemporalDisplayables(),q=(G.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(L=O.getCursor(),W=U.length;L<W;L++)(E=U[L]).beforeBrush&&E.beforeBrush(),E.innerBeforeBrush(),Xy(G,E,q,L===W-1),E.innerAfterBrush(),E.afterBrush&&E.afterBrush(),q.prevEl=E;for(var E,Y=0,Z=X.length;Y<Z;Y++)(E=X[Y]).beforeBrush&&E.beforeBrush(),E.innerBeforeBrush(),Xy(G,E,q,Y===Z-1),E.innerAfterBrush(),E.afterBrush&&E.afterBrush(),q.prevEl=E;O.clearTemporalDisplayables(),O.notClear=!0,G.restore()}P&&N&&Wy(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~_n,e.__isRendered=!1}var qy=new hy,Yy=new ni(100),Zy=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function jy(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&qy.delete(t),qy.get(t));if(e)return e;for(var n,u=B(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<Zy.length;++h){var c=u[Zy[h]];if(null!=c&&!F(c)&&!V(c)&&!gt(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=Yy.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(gt(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!gt(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;gt(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=z(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(gt(t))return[e=Math.ceil(t),e];var e=z(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return z(t,Ky)}(d),m=Ky(f),v=!l&&H.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=fo(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=fo(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var w=0,b=0;b<f.length;++b)w+=f[b];if(!(w<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var I=T/2%g.length,k=0,D=0,A=0;k<2*x.width;){for(var P,L,O,R,E,N=0,b=0;b<d[C].length;++b)N+=d[C][b];if(N<=0)break;D%2==0&&(L=.5*(1-u.symbolSize),P=k+d[C][D]*L,L=S+f[M]*L,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,E=A/2%g[I].length,function(t,e,n,i,r){var o=l?1:a,r=xy(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):Uy(p,r)}(P,L,O,R,g[I][E])),k+=d[C][D],++A,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&Yy.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,qy.set(t,e),t.dirty=!1,e}function Ky(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var $y=new he,Qy={};var dy={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},Jy="__flagInMainProcess",t0="__pendingUpdate",e0="__needsUpdateStatus",n0=/^[a-zA-Z0-9_]+$/,i0="__connectUpdateStatus";function r0(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return a0(this,n,t);this.id}}function o0(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return a0(this,n,t)}}function a0(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),he.prototype[e].apply(t,n)}u(u0,s0=he);var s0,l0=u0;function u0(){return null!==s0&&s0.apply(this,arguments)||this}var h0,c0,p0,d0,f0,g0,y0,m0,v0,_0,x0,w0,b0,S0,M0,T0,C0,I0,k0,fy=l0.prototype,D0=(fy.on=o0("on"),fy.off=o0("off"),u(m,k0=he),m.prototype._onframe=function(){if(!this._disposed){I0(this);var t=this._scheduler;if(this[t0]){var e=this[t0].silent;this[Jy]=!0;try{h0(this),d0.update.call(this,null,this[t0].updateParams)}catch(t){throw this[Jy]=!1,this[t0]=null,t}this._zr.flush(),this[Jy]=!1,this[t0]=null,m0.call(this,e),v0.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),g0(this,i),t.performVisualTasks(i),S0(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},m.prototype.getDom=function(){return this._dom},m.prototype.getId=function(){return this.id},m.prototype.getZr=function(){return this._zr},m.prototype.isSSR=function(){return this._ssr},m.prototype.setOption=function(t,e,n){if(!this[Jy])if(this._disposed)this.id;else{R(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[Jy]=!0,this._model&&!e||(e=new pd(this._api),a=this._theme,(s=this._model=new nd).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},F0);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[t0]={silent:i,updateParams:s},this[Jy]=!1,this.getZr().wakeUp();else{try{h0(this),d0.update.call(this,null,s)}catch(t){throw this[t0]=null,this[Jy]=!1,t}this._ssr||this._zr.flush(),this[t0]=null,this[Jy]=!1,m0.call(this,i),v0.call(this,i)}}},m.prototype.setTheme=function(){},m.prototype.getModel=function(){return this._model},m.prototype.getOption=function(){return this._model&&this._model.getOption()},m.prototype.getWidth=function(){return this._zr.getWidth()},m.prototype.getHeight=function(){return this._zr.getHeight()},m.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||b.hasGlobalWindow&&window.devicePixelRatio||1},m.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},m.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},m.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},m.prototype.getSvgDataURL=function(){var t;if(b.svgSupported)return O((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},m.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,O(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),O(n,function(t){t.group.ignore=!1}),r;this.id},m.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,U0[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),O(G0,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(S(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=H.createCanvas(),(f=Zr(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",O(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new Is({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),O(p,function(t){t=new vs({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},m.prototype.convertToPixel=function(t,e){return f0(this,"convertToPixel",t,e)},m.prototype.convertFromPixel=function(t,e){return f0(this,"convertFromPixel",t,e)},m.prototype.containPixel=function(t,i){var r;if(!this._disposed)return O(Po(this._model,t),function(t,n){0<=n.indexOf("Models")&&O(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},m.prototype.getVisual=function(t,e){var t=Po(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},m.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},m.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},m.prototype._initEvents=function(){var t,n,i,s=this;O(E0,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&sy(o,function(t){var e,t=Us(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,o)||{},1):t.eventData&&(n=L({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),O(B0,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),O(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?(ay("map","selectchanged",n,e,t),ay("pie","selectchanged",n,e,t)):"select"===t.fromAction?(ay("map","selected",n,e,t),ay("pie","selected",n,e,t)):"unselect"===t.fromAction&&(ay("map","unselected",n,e,t),ay("pie","unselected",n,e,t))})},m.prototype.isDisposed=function(){return this._disposed},m.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},m.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&Eo(this.getDom(),Y0,""),e=(t=this)._api,n=t._model,O(t._componentsViews,function(t){t.dispose(n,e)}),O(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete G0[t.id])},m.prototype.resize=function(t){if(!this[Jy])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[t0]&&(null==n&&(n=this[t0].silent),e=!0,this[t0]=null),this[Jy]=!0;try{e&&h0(this),d0.update.call(this,{type:"resize",animation:L({duration:0},t&&t.animation)})}catch(t){throw this[Jy]=!1,t}this[Jy]=!1,m0.call(this,n),v0.call(this,n)}}},m.prototype.showLoading=function(t,e){this._disposed?this.id:(R(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),W0[t]&&(t=W0[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},m.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},m.prototype.makeActionFromEvent=function(t){var e=L({},t);return e.type=B0[t.type],e},m.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(R(e)||(e={silent:!!e}),N0[t.type]&&this._model&&(this[Jy]?this._pendingActions.push(t):(n=e.silent,y0.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&b.browser.weChat&&this._throttledZrFlush(),m0.call(this,n),v0.call(this,n))))},m.prototype.updateLabelLayout=function(){$y.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},m.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},m.internalField=(h0=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),c0(t,!0),c0(t,!1),e.plan()},c0=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Fo(t.type),(n=new(r?yg.getClass(e.main,e.sub):wg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},p0=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=N(),O(mo(t),function(t){t=Mo(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!=d.get(t.id);if(!i)if(Fl(p))if(t instanceof lg){if(p.type===tl&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=ko(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?Il(a,(r=Us(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&Il(a,r,i,o))}}}else{a=Dl(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===tl&&r&&!p.notBlur&&kl(t.mainType,t.componentIndex,c._api),i&&O(i,function(t){(p.type===tl?xl:wl)(t)})}else zl(p)&&t instanceof lg&&(o=t,i=p,c._api,zl(i)&&(e=i.dataType,F(n=ko(o.getData(e),i))||(n=[n]),o[i.type===rl?"toggleSelect":i.type===nl?"select":"unselect"](n,e)),Al(t),C0(c))},c),r&&r.eachComponent(i,function(t){d&&null!=d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):O([].concat(c._componentsViews).concat(c._chartsViews),o)},d0={prepareAndUpdate:function(t){h0(this),d0.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),g0(this,n),o.update(n,i),A0(n),a.performVisualTasks(n,t),w0(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),$y.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=N(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),A0(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),S0(this,a,s,n,{},r),$y.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),wg.markUpdateMethod(t,"updateView"),A0(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),w0(this,e,this._api,t,{}),$y.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),wg.markUpdateMethod(n,"updateVisual"),A0(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),$y.trigger("afterupdate",r,this._api))},updateLayout:function(t){d0.update.call(this,t)}},f0=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=Po(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},g0=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},y0=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=N0[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Fo(h[0]),h=(this[Jy]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=z(i.batch,function(t){return(t=B(L({},t),i)).batch=null,t})),[]),g=zl(i),y=Fl(i);if(y&&Cl(this._api),O(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||L({},t)).type=u.event||r.type,f.push(r),y?(e=(n=Lo(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",p0(a,c,t,n),C0(a)):g?(p0(a,c,t,"series"),C0(a)):p&&p0(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[t0]?(h0(this),d0.update.call(this,i),this[t0]=null):d0[c].call(this,i)}catch(t){throw this[Jy]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[Jy]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){O(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},m0=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();y0.call(this,n,t)}},v0=function(t){t||this.trigger("updated")},_0=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[t0]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},x0=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=sy(t.target,Bl);o&&(o=o,e=t,t=a._api,n=Us(o),i=(r=Dl(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&kl(n.componentMainType,n.componentIndex,t),O(i,function(t){return vl(t,e)})):(Il(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&kl(n.componentMainType,n.componentIndex,t),vl(o,e)),C0(a))}).on("mouseout",function(t){var e,n,i=sy(t.target,Bl);i&&(i=i,e=t,Cl(t=a._api),(n=Dl((n=Us(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?O(n,function(t){return _l(t,e)}):_l(i,e),C0(a))}).on("click",function(t){var e,t=sy(t.target,function(t){return null!=Us(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=Us(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},w0=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(vn(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),O(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),b0(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive=!1}),S0(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},b0=function(t,n,i,r,e,o){O(o||t._componentsViews,function(t){var e=t.__model;O0(0,t),t.render(e,n,i,r),L0(e,t),R0(e,t)})},S0=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=L(n||{},{updatedSeries:t.getSeries()}),$y.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),O0(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),Al(t)}),h.unfinished=c||h.unfinished,$y.trigger("series:layoutlabels",t,e,n),$y.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];L0(t,e),R0(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!b.node&&!b.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),$y.trigger("series:afterupdate",t,e,n)},C0=function(t){t[e0]=!0,t.getZr().wakeUp()},I0=function(t){t[e0]&&(t.getZr().storage.traverse(function(t){Ih(t)||P0(t)}),t[e0]=!1)},M0=function(n){return u(t,e=ad),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){xl(t,e),C0(n)},t.prototype.leaveEmphasis=function(t,e){wl(t,e),C0(n)},t.prototype.enterBlur=function(t){fl(t,ul),C0(n)},t.prototype.leaveBlur=function(t){bl(t),C0(n)},t.prototype.enterSelect=function(t){Sl(t),C0(n)},t.prototype.leaveSelect=function(t){Ml(t),C0(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(T0=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][i0]=e}O(B0,function(t,e){i._messageCenter.on(e,function(t){var e,n;!U0[i.group]||0===i[i0]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],O(G0,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),O(n,function(t){1!==t[i0]&&t.dispatchAction(e)}),r(n,2))})})})),m);function m(t,e,n){var i=k0.call(this,new ny)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=H0[e]),i._dom=t,n.ssr&&Kr(function(t){var e,t=Us(t),n=t.dataIndex;if(null!=n)return(e=N()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e}),i._zr=Zr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:E(n.useDirtyRect,!1),useCoarsePointer:E(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize})),n=(i._ssr=n.ssr,i._throttledZrFlush=Pg(pt(t.flush,t),17),(e=S(e))&&Ld(e,!0),i._theme=e,i._locale=V(e=n.locale||Bc)?(n=Ec[e.toUpperCase()]||{},e===Lc||e===Oc?S(n):d(S(n),S(Ec[Rc]),!1)):d(S(e),S(Ec[Rc]),!1),i._coordSysMgr=new ud,i._api=M0(i));function r(t,e){return t.__prio-e.__prio}return vn(V0,r),vn(z0,r),i._scheduler=new Fg(i,n,z0,V0),i._messageCenter=new l0,i._initEvents(),i.resize=pt(i.resize,i),t.animation.on("frame",i._onframe,i),_0(t,i),x0(t,i),At(i),i}function A0(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function P0(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===$s&&t.states.emphasis?e.push("emphasis"):t.hoverState===Ks&&t.states.blur&&e.push("blur"),t.useStates(e)}function L0(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function O0(t,e){e.eachRendered(function(t){var e,n;Ih(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function R0(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(Ih(t)||(t instanceof hs&&((i=Ys(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&P0(t)))})}var gy=D0.prototype,E0=(gy.on=r0("on"),gy.off=r0("off"),gy.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var N0={},B0={},z0=[],F0=[],V0=[],H0={},W0={},G0={},U0={},X0=+new Date,q0=+new Date,Y0="_echarts_instance_";function Z0(t){U0[t]=!1}py=Z0;function j0(t){return G0[e=Y0,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function K0(t,e){H0[t]=e}function $0(t){I(F0,t)<0&&F0.push(t)}function Q0(t,e){sm(z0,t,e,2e3)}function J0(t){em("afterinit",t)}function tm(t){em("afterupdate",t)}function em(t,e){$y.on(t,e)}function nm(t,e,n){k(e)&&(n=e,e="");var i=R(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,B0[e]||(It(n0.test(i)&&n0.test(e)),N0[i]||(N0[i]={action:n,actionInfo:t}),B0[e]=i)}function im(t,e){ud.register(t,e)}function rm(t,e){sm(V0,t,e,1e3,"layout")}function om(t,e){sm(V0,t,e,3e3,"visual")}var am=[];function sm(t,e,n,i,r){(k(e)||R(e))&&(n=e,e=i),0<=I(am,n)||(am.push(n),(i=Fg.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function lm(t,e){W0[t]=e}function um(t,e,n){var i=Qy.registerMap;i&&i(t,e,n)}function hm(t){var e=(t=S(t)).type,n=(e||g(""),e.split(":")),i=(2!==n.length&&g(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,zf.set(e,t)}om(2e3,Zo),om(4500,Xh),om(4500,Uc),om(2e3,Sc),om(4500,_c),om(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=jy(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=jy(t,i))})}),$0(Ld),Q0(900,function(t){var i=N();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(Od)}),lm("default",function(i,r){B(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Wr,a=new Is({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new Ls({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new Is({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new nh({shape:{startAngle:-zg/2,endAngle:-zg/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*zg/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*zg/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),nm({type:tl,event:tl,update:tl},Ht),nm({type:el,event:el,update:el},Ht),nm({type:nl,event:nl,update:nl},Ht),nm({type:il,event:il,update:il},Ht),nm({type:rl,event:rl,update:rl},Ht),K0("light",Tc),K0("dark",wc);function cm(t){return null==t?0:t.length||1}function pm(t){return t}fm.prototype.add=function(t){return this._add=t,this},fm.prototype.update=function(t){return this._update=t,this},fm.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},fm.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},fm.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},fm.prototype.remove=function(t){return this._remove=t,this},fm.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},fm.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=cm(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},fm.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=cm(l),c=cm(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},fm.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=cm(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},fm.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=cm(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var dm=fm;function fm(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||pm,this._newKeyGetter=i||pm,this.context=r,this._diffModeMultiple="multiple"===o}ym.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},ym.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var gm=ym;function ym(t,e){this._encode=t,this._schema=e}function mm(o,t){var e={},a=e.encode={},s=N(),l=[],u=[],h={},i=(O(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,vm(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),vm(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),Lp.each(function(t,e){var n=vm(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=z(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new gm(h,t),e}function vm(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var _m=function(t){this.otherDims={},null!=t&&L(this,t)},xm=Do(),wm={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},bm=(Sm.prototype.isDimensionOmitted=function(){return this._dimOmitted},Sm.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=Cm(this.source))},Sm.prototype.getSourceDimensionIndex=function(t){return E(this._dimNameMap.get(t),-1)},Sm.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},Sm.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Gd(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(wm[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},Sm.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},Sm.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},Sm);function Sm(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function Mm(t){return t instanceof bm}function Tm(t){for(var e=N(),n=0;n<(t||[]).length;n++){var i=t[n],i=R(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function Cm(t){var e=xm(t);return e.dimNameMap||(e.dimNameMap=Tm(t.dimensionsDefine))}var Im,km,Dm,Am,Pm,Lm,Om,Rm=R,Em=z,Nm="undefined"==typeof Int32Array?Array:Int32Array,Bm=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],zm=["_approximateExtent"],Fm=(v.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},v.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},v.prototype._recognizeDimIndex=function(t){if(gt(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},v.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},v.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},v.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},v.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},v.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},v.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},v.prototype.getStore=function(){return this._store},v.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof jf?t:i)||(r=this.dimensions,t=Bd(t)||st(t)?new Yd(t,r.length):t,i=new jf,r=Em(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=mm(this,this._schema),this.userOutput=this._dimSummary.userOutput},v.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},v.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e&&e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&Om(this,o)},v.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},v.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Bp&&!t.fillStorage},v.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Op&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!R(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=Mo(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=Mo(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)Om(this,a);Im(this)}},v.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},v.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},v.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},v.prototype.setCalculationInfo=function(t,e){Rm(t)?L(this._calculationInfo,t):this._calculationInfo[t]=e},v.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?Dm(this,this._nameDimIdx,t):e)?"":e},v.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},v.prototype.getId=function(t){return km(this,this.getRawIndex(t))},v.prototype.count=function(){return this._store.count()},v.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},v.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},v.prototype.getIndices=function(){return this._store.getIndices()},v.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},v.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},v.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},v.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(Em(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},v.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},v.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},v.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},v.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},v.prototype.rawIndexOf=function(t,e){t=t&&this._invertedIndicesMap[t],t=t&&t[e];return null==t||isNaN(t)?-1:t},v.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},v.prototype.each=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=Em(Am(t),this._getStoreDimIndex,this);this._store.each(t,n?pt(e,n):e)},v.prototype.filterSelf=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=Em(Am(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?pt(e,n):e),this},v.prototype.selectRange=function(n){var i=this,r={};return O(ct(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},v.prototype.mapArray=function(t,e,n){k(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},v.prototype.map=function(t,e,n,i){n=n||i||this,i=Em(Am(t),this._getStoreDimIndex,this),t=Lm(this);return t._store=this._store.map(i,n?pt(e,n):e),t},v.prototype.modify=function(t,e,n,i){n=n||i||this,i=Em(Am(t),this._getStoreDimIndex,this);this._store.modify(i,n?pt(e,n):e)},v.prototype.downSample=function(t,e,n,i){var r=Lm(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},v.prototype.minmaxDownSample=function(t,e){var n=Lm(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},v.prototype.lttbDownSample=function(t,e){var n=Lm(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},v.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},v.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new kc(t,e,e&&e.ecModel)},v.prototype.diff=function(e){var n=this;return new dm(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return km(e,t)},function(t){return km(n,t)})},v.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},v.prototype.setVisual=function(t,e){this._visual=this._visual||{},Rm(t)?L(this._visual,t):this._visual[t]=e},v.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},v.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},v.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():Rm(n)&&(n=L({},n)),i[e]=n),n},v.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,Rm(e)?L(i,e):i[e]=n},v.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},v.prototype.setLayout=function(t,e){Rm(t)?L(this._layout,t):this._layout[t]=e},v.prototype.getLayout=function(t){return this._layout[t]},v.prototype.getItemLayout=function(t){return this._itemLayouts[t]},v.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?L(this._itemLayouts[t]||{},e):e},v.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},v.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=Us(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse(function(t){t=Us(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"}),this._graphicEls[t]=e},v.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},v.prototype.eachItemGraphicEl=function(n,i){O(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},v.prototype.cloneShallow=function(t){return t=t||new v(this._schema||Em(this.dimensions,this._getDimInfo,this),this.hostModel),Pm(t,this),t._store=this._store,t},v.prototype.wrapMethod=function(t,e){var n=this[t];k(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(Tt(arguments)))})},v.internalField=(Im=function(a){var s=a._invertedIndicesMap;O(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new Nm(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},Dm=function(t,e,n){return Mo(t._getCategory(e,n),null)},km=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?Dm(t,t._idDimIdx,e):n)?"e\0\0"+e:n},Am=function(t){return t=F(t)?t:null!=t?[t]:[]},Lm=function(t){var e=new v(t._schema||Em(t.dimensions,t._getDimInfo,t),t.hostModel);return Pm(e,t),e},Pm=function(e,n){O(Bm.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,O(zm,function(t){e[t]=S(n[t])}),e._calculationInfo=L({},n._calculationInfo)},void(Om=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=Dm(t,r,e)),null==s&&null!=o&&(i[e]=s=Dm(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),v);function v(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"]),o=(Mm(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new _m({name:c}):c instanceof _m?c:new _m(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=N(),O(a,function(t){i.set(o[t].storeDimIndex,t)}))}function Vm(t,e){Bd(t)||(t=Fd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=N(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),O(n,function(t){R(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?Cm(t):Tm(o),p=e.encodeDefine,d=N(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new Uf(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=R(e=o[t])?e:{name:e},n=new _m,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=mo(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),O(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=Lp.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}O(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=L({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=mo(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}O(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(B(t,r),o,e),null==t.name&&n&&(R(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&B(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)O(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||Xp(t,M)!==Hp.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,I=N(),k=0;k<C.length;k++){var D=C[k],A=D.name,P=I.get(A)||0;0<P&&(D.name=A+(P-1)),P++,I.set(A,P)}return new bm({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var Hm=function(t){this.coordSysDims=[],this.axisMap=N(),this.categoryAxisMap=N(),this.coordSysName=t};var Wm={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Oo).models[0],t=t.getReferringComponents("yAxis",Oo).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),Gm(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),Gm(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",Oo).models[0];e.coordSysDims=["single"],n.set("single",t),Gm(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",Oo).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),Gm(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),Gm(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();O(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),Gm(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function Gm(t){return"category"===t.get("type")}function Um(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(Mm(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return O(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,O(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function Xm(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function qm(t,e){return Xm(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Ym(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Fd(t)):h=(i=u.getSource()).sourceFormat===Op,function(t){var e=t.get("coordinateSystem"),n=new Hm(e);if(e=Wm[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=ud.get(c),p=(p=r&&r.coordSysDims?z(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=k(p)?p:p?dt(Gp,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=Vm(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&O(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=Um(e,{schema:p,store:n}),c=new Fm(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==Op||F(xo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}jm.prototype.getSetting=function(t){return this._setting[t]},jm.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},jm.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},jm.prototype.getExtent=function(){return this._extent.slice()},jm.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},jm.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},jm.prototype.isBlank=function(){return this._isBlank},jm.prototype.setBlank=function(t){this._isBlank=t};var Zm=jm;function jm(t){this._setting=t||{},this._extent=[1/0,-1/0]}Xo(Zm);var Km=0,$m=(Qm.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&z(e,Jm);return new Qm({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},Qm.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},Qm.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},Qm.prototype._getOrCreateMap=function(){return this._map||(this._map=N(this.categories))},Qm);function Qm(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Km}function Jm(t){return R(t)&&null!=t.value?t.value:t+""}function tv(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=ho(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=nv(o)),n=r.niceTickExtent=[to(Math.ceil(t[0]/o)*o,e),to(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),iv(i,0,o),iv(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function ev(t){var e=Math.pow(10,uo(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,to(t*e)}function nv(t){return eo(t)+2}function iv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function rv(t,e){return t>=e[0]&&t<=e[1]}function ov(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function av(t,e){return t*(e[1]-e[0])+e[0]}u(uv,sv=Zm),uv.prototype.parse=function(t){return null==t?NaN:V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},uv.prototype.contain=function(t){return rv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},uv.prototype.normalize=function(t){return ov(t=this._getTickNumber(this.parse(t)),this._extent)},uv.prototype.scale=function(t){return t=Math.round(av(t,this._extent)),this.getRawOrdinalNumber(t)},uv.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},uv.prototype.getMinorTicks=function(t){},uv.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},uv.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},uv.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},uv.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},uv.prototype.count=function(){return this._extent[1]-this._extent[0]+1},uv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},uv.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},uv.prototype.getOrdinalMeta=function(){return this._ordinalMeta},uv.prototype.calcNiceTicks=function(){},uv.prototype.calcNiceExtent=function(){},uv.type="ordinal";var sv,lv=uv;function uv(t){var t=sv.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new $m({}))&&(e=new $m({categories:z(e,function(t){return R(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}Zm.registerClass(lv);var hv,cv=to,pv=(u(dv,hv=Zm),dv.prototype.parse=function(t){return t},dv.prototype.contain=function(t){return rv(t,this._extent)},dv.prototype.normalize=function(t){return ov(t,this._extent)},dv.prototype.scale=function(t){return av(t,this._extent)},dv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},dv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},dv.prototype.getInterval=function(){return this._interval},dv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=nv(t)},dv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:cv(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=cv(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:cv(s+e,r)}:{value:n[1]})}return o},dv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=cv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},dv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=eo(t.value)||0:"auto"===e&&(e=this._intervalPrecision),pp(cv(t.value,e,!0)))},dv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=tv(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},dv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=cv(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=cv(Math.ceil(e[1]/n)*n))},dv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},dv.type="interval",dv);function dv(){var t=null!==hv&&hv.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}Zm.registerClass(pv);function fv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function gv(t){return t.dim+t.index}function yv(t,e){var n=[];return e.eachSeriesByType(t,function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&n.push(t)}),n}function mv(t){var a,d,u=function(t){var e,l={},n=(O(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),h=[];return O(t,function(t){var e,n,i=t.coordinateSystem.getBaseAxis(),r=i.getExtent(),o=(e="category"===i.type?i.getBandWidth():"value"===i.type||"time"===i.type?(e=i.dim+"_"+i.index,e=u[e],o=Math.abs(r[1]-r[0]),n=i.scale.getExtent(),n=Math.abs(n[1]-n[0]),e?o/n*e:o):(n=t.getData(),Math.abs(r[1]-r[0])/n.count()),f(t.get("barWidth"),e)),r=f(t.get("barMaxWidth"),e),a=f(t.get("barMinWidth")||((n=t).pipelineContext&&n.pipelineContext.large?.5:1),e),s=t.get("barGap"),l=t.get("barCategoryGap");h.push({bandWidth:e,barWidth:o,barMaxWidth:r,barMinWidth:a,barGap:s,barCategoryGap:l,axisKey:gv(i),stackId:fv(t)})}),a={},O(h,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},O(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=ct(e).length,o=Math.max(35-4*a,15)+"%"),f(o,r)),s=f(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(O(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(O(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);O(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}u(xv,vv=pv),xv.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Kc(t.value,Xc[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(jc(this._minLevelUnit))]||Xc.second,e,this.getSetting("locale"))},xv.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(k(n))o=n(t.value,e,{level:t.level});else{var a=L({},Gc);if(0<t.level)for(var s=0;s<qc.length;++s)a[qc[s]]="{primary|"+a[qc[s]]+"}";var l=n?!1===n.inherit?n:B(n,a):a,u=$c(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=Yc.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return Kc(new Date(t.value),o,i,r)},xv.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,b,S,M){var e=Yc,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return Qc(c,t,i)===Qc(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=so(e),p=so(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(jc(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(jc(e)){case"year":case"month":i[ap(n)](0);case"day":i[sp(n)](1);case"hour":i[lp(n)](0);case"minute":i[up(n)](0);case"second":i[hp(n)](0),i[cp(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(b/Wc/365)),c=Jc(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=b,h=6<(a/=30*Wc)?6:3<a?3:2<a?2:1,c=tp(S),p=ap(S);break;case"week":case"half-week":case"day":a=b,h=16<(a/=Wc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=ep(S),p=sp(S),0;break;case"half-day":case"quarter-day":case"hour":o=b,h=12<(o/=Hc)?12:6<o?6:3.5<o?4:2<o?2:1,c=np(S),p=lp(S);break;case"minute":h=bv(b,!0),c=ip(S),p=up(S);break;case"second":h=bv(b,!1),c=rp(S),p=hp(S);break;case"millisecond":h=ho(b,!0),c=op(S),p=cp(S)}w=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,w=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](w+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=jc(e[l]);if(function(t){return t===jc(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?jc(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/b;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(z(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},xv.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Wc,n[1]+=Wc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Wc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},xv.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),wv.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(wv,this._approxInterval,0,i),i-1);this._interval=wv[t][1],this._minLevelUnit=wv[Math.max(t-1,0)][0]},xv.prototype.parse=function(t){return gt(t)?t:+so(t)},xv.prototype.contain=function(t){return rv(this.parse(t),this._extent)},xv.prototype.normalize=function(t){return ov(this.parse(t),this._extent)},xv.prototype.scale=function(t){return av(t,this._extent)},xv.type="time";var vv,_v=xv;function xv(t){t=vv.call(this,t)||this;return t.type="time",t}var wv=[["second",Fc],["minute",Vc],["hour",Hc],["quarter-day",6*Hc],["half-day",12*Hc],["day",1.2*Wc],["half-week",3.5*Wc],["week",7*Wc],["month",31*Wc],["quarter",95*Wc],["half-year",Wo/2],["year",Wo]];function bv(t,e){return 30<(t/=e?Vc:Fc)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}Zm.registerClass(_v);var Sv,Mv=Zm.prototype,Tv=pv.prototype,Cv=to,Iv=Math.floor,kv=Math.ceil,Dv=Math.pow,Av=Math.log,Pv=(u(Lv,Sv=Zm),Lv.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return z(Tv.getTicks.call(this,t),function(t){var t=t.value,e=to(Dv(this.base,t)),e=t===n[0]&&this._fixMin?Ov(e,i[0]):e;return{value:t===n[1]&&this._fixMax?Ov(e,i[1]):e}},this)},Lv.prototype.setExtent=function(t,e){var n=Av(this.base);t=Av(Math.max(0,t))/n,e=Av(Math.max(0,e))/n,Tv.setExtent.call(this,t,e)},Lv.prototype.getExtent=function(){var t=this.base,e=Mv.getExtent.call(this);e[0]=Dv(t,e[0]),e[1]=Dv(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=Ov(e[0],t[0])),this._fixMax&&(e[1]=Ov(e[1],t[1])),e},Lv.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Av(t[0])/Av(e),t[1]=Av(t[1])/Av(e),Mv.unionExtent.call(this,t)},Lv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Lv.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=lo(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[to(kv(e[0]/i)*i),to(Iv(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},Lv.prototype.calcNiceExtent=function(t){Tv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},Lv.prototype.parse=function(t){return t},Lv.prototype.contain=function(t){return rv(t=Av(t)/Av(this.base),this._extent)},Lv.prototype.normalize=function(t){return ov(t=Av(t)/Av(this.base),this._extent)},Lv.prototype.scale=function(t){return t=av(t,this._extent),Dv(this.base,t)},Lv.type="log",Lv);function Lv(){var t=null!==Sv&&Sv.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new pv,t._interval=0,t}hy=Pv.prototype;function Ov(t,e){return Cv(t,eo(e))}hy.getMinorTicks=Tv.getMinorTicks,hy.getLabel=Tv.getLabel,Zm.registerClass(Pv);Ev.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),e.get("min",!0)),r=(null==r&&(r=e.get("startValue",!0)),this._modelMinRaw=r),r=(k(r)?this._modelMinNum=zv(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=zv(t,r)),this._modelMaxRaw=e.get("max",!0));k(r)?this._modelMaxNum=zv(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=zv(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[kr(t[0],1),kr(t[1],1)]},Ev.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),bt(a)||bt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},Ev.prototype.modifyDataMinMax=function(t,e){this[Bv[t]]=e},Ev.prototype.setDeterminedMinMax=function(t,e){this[Nv[t]]=e},Ev.prototype.freeze=function(){this.frozen=!0};var Rv=Ev;function Ev(t,e,n){this._prepareParams(t,e,n)}var Nv={min:"_determinedMin",max:"_determinedMax"},Bv={min:"_dataMin",max:"_dataMax"};function zv(t,e){return null==e?null:bt(e)?NaN:t.parse(e)}function Fv(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new Rv(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=yv("bar",c),n=!1,O(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=mv(t),c=u,t=h,s=s,a=(i=e).axis.getExtent(),a=Math.abs(a[1]-a[0]),void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[gv(e)])&&null!=n?t[fv(n)]:t}(s,i.axis))&&(r=1/0,O(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,O(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function Vv(t,e){var n=Fv(t,e),i=n.extent,r=e.get("splitNumber"),o=(t instanceof Pv&&(t.base=e.get("logBase")),t.type),a=e.get("interval"),o="interval"===o||"time"===o;t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?e.get("minInterval"):null,maxInterval:o?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function Hv(t,e){if(e=e||t.get("type"))switch(e){case"category":return new lv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new _v({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(Zm.getClass(e)||pv)}}function Wv(n){var i,e,r,t=n.getLabelModel().get("formatter"),o="category"===n.type?n.scale.getExtent()[0]:null;return"time"===n.scale.type?(r=t,function(t,e){return n.scale.getFormattedLabel(t,e,r)}):V(t)?(e=t,function(t){t=n.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):k(t)?(i=t,function(t,e){return null!=o&&(e=t.value-o),i(Gv(n,t),e,null!=t.level?{level:t.level}:null)}):function(t){return n.scale.getLabel(t)}}function Gv(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function Uv(t){t=t.get("interval");return null==t?"auto":t}function Xv(t){return"category"===t.type&&0===Uv(t.getLabelModel())}function qv(e,t){var n={};return O(e.mapDimensionsAll(t),function(t){n[qm(e,t)]=!0}),ct(n)}Zv.prototype.getNeedCrossZero=function(){return!this.option.scale},Zv.prototype.getCoordSysModel=function(){};var Yv=Zv;function Zv(){}var fy=Object.freeze({__proto__:null,createDimensions:function(t,e){return Vm(t,e).dimensions},createList:function(t){return Ym(null,t)},createScale:function(t,e){var n=e;return(e=Hv(n=e instanceof kc?n:new kc(e))).setExtent(t[0],t[1]),Vv(e,n),e},createSymbol:xy,createTextStyle:function(t,e){return sc(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:Xm,enableDataStack:Um,getStackedDimension:qm},enableHoverEmphasis:Pl,getECData:Us,getLayoutRect:function(t,e,n){n=fp(n||0);var i=e.width,r=e.height,o=f(t.left,i),a=f(t.top,r),e=f(t.right,i),s=f(t.bottom,r),l=f(t.width,i),u=f(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}return o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0)),(p=new X(o+n[3],a+n[0],l,u)).margin=n,p},mixinAxisModelCommonMethods:function(t){at(t,Yv)}}),jv=[],Kv={registerPreprocessor:$0,registerProcessor:Q0,registerPostInit:J0,registerPostUpdate:tm,registerUpdateLifecycle:em,registerAction:nm,registerCoordinateSystem:im,registerLayout:rm,registerVisual:om,registerTransform:hm,registerLoading:lm,registerMap:um,registerImpl:function(t,e){Qy[t]=e},PRIORITY:dy,ComponentModel:y,ComponentView:yg,SeriesModel:lg,ChartView:wg,registerComponentModel:function(t){y.registerClass(t)},registerComponentView:function(t){yg.registerClass(t)},registerSeriesModel:function(t){lg.registerClass(t)},registerChartView:function(t){wg.registerClass(t)},registerSubTypeDefaulter:function(t,e){y.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){jr(t,e)}};function $v(t){F(t)?O(t,function(t){$v(t)}):0<=I(jv,t)||(jv.push(t),(t=k(t)?{install:t}:t).install(Kv))}var Qv=1e-8;function Jv(t,e){return Math.abs(t-e)<Qv}function t_(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=$a(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return Jv(r[0],s[0])&&Jv(r[1],s[1])||(i+=$a(r[0],r[1],s[0],s[1],e,n)),0!==i}}var e_=[];function n_(t,e){for(var n=0;n<t.length;n++)ie(t[n],t[n],e)}function i_(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(re(e,e,o),oe(n,n,o))}}r_.prototype.setCenter=function(t){this._center=t},r_.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};gy=r_;function r_(t){this.name=t}var o_,a_,s_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},l_=function(t){this.type="linestring",this.points=t},u_=(u(h_,o_=gy),h_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},h_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],O(this.geometries,function(t){"polygon"===t.type?i_(t.exterior,n,i,e):O(t.points,function(t){i_(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new X(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},h_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(t_(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(t_(s[l],t[0],t[1]))continue t;return!0}}}return!1},h_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new X(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(n_(u.exterior,a),O(u.interiors,function(t){n_(t,a)})):O(u.points,function(t){n_(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},h_.prototype.cloneShallow=function(t){t=new h_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},h_);function h_(t,e,n){t=o_.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function c_(t,e){t=a_.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function p_(t,e,n){for(var i=0;i<t.length;i++)t[i]=d_(t[i],e[i],n)}function d_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function f_(t,o){var e,n,r;return z(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),O(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=d_(i,n,r);break;case"Polygon":case"MultiLineString":p_(i,n,r);break;case"MultiPolygon":O(i,function(t,e){return p_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new s_(r[0],r.slice(1)));break;case"MultiPolygon":O(n.coordinates,function(t){t[0]&&i.push(new s_(t[0],t.slice(1)))});break;case"LineString":i.push(new l_([n.coordinates]));break;case"MultiLineString":i.push(new l_(n.coordinates))}t=new u_(e[o||"name"],i,e.cp);return t.properties=e,t})}u(c_,a_=gy),c_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Re(e_),i=t;i&&!i.isGeoSVGGraphicRoot;)Ne(n,i.getLocalTransform(),n),i=i.parent;return Ve(n,n),ie(e,e,n),e};var Zo=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){return t[e]&&function(t,e){var n=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];var i=Math.pow(10,e),e=z(t,function(t){return(isNaN(t)?0:t)/n*i*100}),r=100*i,o=z(e,function(t){return Math.floor(t)}),a=lt(o,function(t,e){return t+e},0),s=z(e,function(t,e){return t-o[e]});for(;a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return z(o,function(t){return t/i})}(t,n)[e]||0},getPixelPrecision:io,getPrecision:eo,getPrecisionSafe:no,isNumeric:po,isRadianAroundZero:oo,linearMap:Jr,nice:ho,numericToNumber:co,parseDate:so,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:lo,quantityExponent:uo,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:ro,round:to}),Xh=Object.freeze({__proto__:null,format:Kc,parse:so}),Uc=Object.freeze({__proto__:null,Arc:nh,BezierCurve:Qu,BoundingRect:X,Circle:su,CompoundPath:oh,Ellipse:cu,Group:Wr,Image:vs,IncrementalDisplayable:n,Line:qu,LinearGradient:hh,Polygon:Bu,Polyline:Hu,RadialGradient:sh,Rect:Is,Ring:Lu,Sector:ku,Text:Ls,clipPointsByRect:Kh,clipRectByRect:$h,createIcon:Qh,extendPath:Nh,extendShape:Rh,getShapeClass:zh,getTransform:qh,initProps:Ch,makeImage:Vh,makePath:Fh,mergePath:Wh,registerShape:Bh,resizePath:Gh,updateProps:Th}),Sc=Object.freeze({__proto__:null,addCommas:pp,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:xe,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=so(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",Zc(r,2)).replace("M",r).replace("yyyy",i).replace("yy",Zc(i%100+"",2)).replace("dd",Zc(o,2)).replace("d",o).replace("hh",Zc(a,2)).replace("h",a).replace("mm",Zc(s,2)).replace("m",s).replace("ss",Zc(l,2)).replace("s",l).replace("SSS",Zc(e,3))},formatTpl:yp,getTextRect:function(t,e,n,i,r,o,a,s){return new Ls({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:function(t,e){var n=(t=V(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+xe(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+xe(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""},normalizeCssArray:fp,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t},truncateText:function(t,e,n,i,r){var o={};return ea(o,t,e,n,i,r),o.text}}),_c=Object.freeze({__proto__:null,bind:pt,clone:S,curry:dt,defaults:B,each:O,extend:L,filter:ut,indexOf:I,inherits:ot,isArray:F,isFunction:k,isObject:R,isString:V,map:z,merge:d,reduce:lt}),g_=Do();function y_(e,t){t=z(t,function(t){return e.scale.parse(t)});return"time"===e.type&&0<t.length&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function m_(n){var i,e,r,o,t,a,s=n.getLabelModel().get("customValues");return s?(i=Wv(n),e=n.scale.getExtent(),{labels:z(ut(y_(n,s),function(t){return t>=e[0]&&t<=e[1]}),function(t){var e={value:t};return{formattedLabel:i(e),rawLabel:n.scale.getLabel(e),tickValue:t}})}):"category"===n.type?(t=(s=n).getLabelModel(),a=__(s,t),!t.get("show")||s.scale.isBlank()?{labels:[],labelCategoryInterval:a.labelCategoryInterval}:a):(t=(r=n).scale.getTicks(),o=Wv(r),{labels:z(t,function(t,e){return{level:t.level,formattedLabel:o(t,e),rawLabel:r.scale.getLabel(t),tickValue:t.value}})})}function v_(t,e){var n,i,r,o,a,s,l=t.getTickModel().get("customValues");return l?(n=t.scale.getExtent(),{ticks:ut(y_(t,l),function(t){return t>=n[0]&&t<=n[1]})}):"category"===t.type?(l=e,o=x_(e=t,"ticks"),a=Uv(l),(s=w_(o,a))||(l.get("show")&&!e.scale.isBlank()||(i=[]),i=k(a)?M_(e,a,!0):"auto"===a?(s=__(e,e.getLabelModel()),r=s.labelCategoryInterval,z(s.labels,function(t){return t.tickValue})):S_(e,r=a,!0),b_(o,a,{ticks:i,tickCategoryInterval:r}))):{ticks:z(t.scale.getTicks(),function(t){return t.value})}}function __(t,e){var n,i=x_(t,"labels"),e=Uv(e),r=w_(i,e);return r||b_(i,e,{labels:k(e)?M_(t,e):S_(t,n="auto"===e?null!=(i=g_(r=t).autoInterval)?i:g_(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function x_(t,e){return g_(t)[e]||(g_(t)[e]=[])}function w_(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function b_(t,e,n){return t.push({key:e,value:n}),n}function S_(t,e,n){for(var i=Wv(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),Xv(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function M_(t,i,r){var o=t.scale,a=Wv(t),s=[];return O(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var T_=[0,1],Tc=(C_.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},C_.prototype.containData=function(t){return this.scale.contain(t)},C_.prototype.getExtent=function(){return this._extent.slice()},C_.prototype.getPixelPrecision=function(t){return io(t||this.scale.getExtent(),this._extent)},C_.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},C_.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&I_(n=n.slice(),i.count()),Jr(t,T_,n,e)},C_.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&I_(n=n.slice(),i.count()),Jr(t,n,T_,e));return this.scale.scale(i)},C_.prototype.pointToData=function(t,e){},C_.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=z(v_(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=to(t),e=to(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1],tickValue:n[0].tickValue}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,O(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e,tickValue:o[1]+1},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},C_.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),z(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return z(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},C_.prototype.getViewLabels=function(){return m_(this).labels},C_.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},C_.prototype.getTickModel=function(){return this.model.getModel("axisTick")},C_.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},C_.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=Wv(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=Mr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=g_(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},C_);function C_(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function I_(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var k_=2*Math.PI,D_=qa.CMD,A_=["top","right","bottom","left"];function P_(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function L_(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var O_=[];function R_(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case D_.M:p=f=m[x++],d=g=m[x++];break;case D_.L:b=P_(p,d,m[x],m[x+1],v,_,O_,!0),p=m[x++],d=m[x++];break;case D_.C:b=Gn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,O_),p=m[x++],d=m[x++];break;case D_.Q:b=Zn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,O_),p=m[x++],d=m[x++];break;case D_.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],I=m[x++],k=m[x++],D=(x+=1,!!(1-m[x++])),A=Math.cos(I)*T+S,P=Math.sin(I)*C+M;x<=1&&(f=A,g=P),P=(A=I)+k,D=D,a=(v-S)*(o=C)/T+S,s=_,l=O_,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(A-P)%k_<1e-4||((P=D?(D=A,A=ja(P),ja(D)):(A=ja(A),ja(P)))<A&&(P+=k_),(D=Math.atan2(s,a))<0&&(D+=k_),A<=D&&D<=P)||A<=D+k_&&D+k_<=P?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(A)+i)-a)*(D-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(P)+i)-a)*(u-a)+((A=o*Math.sin(P)+r)-s)*(A-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(I+k)*T+S,d=Math.sin(I+k)*C+M;break;case D_.R:b=L_(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,O_);break;case D_.Z:b=P_(p,d,f,g,v,_,O_,!0),p=f,d=g}b<y&&(y=b,n.set(O_[0],O_[1]))}return y}var E_=new M,N_=new M,B_=new M,z_=new M,F_=new M;function V_(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||A_,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Ve([],h),p=e.get("length2")||0;u&&B_.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=b=w=x=_=v=m=void 0,y),v=0,_=s,x=E_,w=z_,b=_.width,S=_.height;switch(m){case"top":x.set(_.x+b/2,_.y-v),w.set(0,-1);break;case"bottom":x.set(_.x+b/2,_.y+S+v),w.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),w.set(-1,0);break;case"right":x.set(_.x+b+v,_.y+S/2),w.set(1,0)}M.scaleAndAdd(N_,E_,z_,p),N_.transform(c);y=t.getBoundingRect(),y=u?u.distance(N_):t instanceof hs?R_(N_,t.path,B_):(m=B_,d=L_((d=y).x,y.y,y.width,y.height,N_.x,N_.y,O_),m.set(O_[0],O_[1]),d);y<l&&(l=y,N_.transform(h),B_.transform(h),B_.toArray(o[0]),N_.toArray(o[1]),E_.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,E_.fromArray(i[0]),N_.fromArray(i[1]),B_.fromArray(i[2]),M.sub(z_,E_,N_),M.sub(F_,B_,N_),e=z_.len(),f=F_.len(),e<.001||f<.001||(z_.scale(1/e),F_.scale(1/f),e=z_.dot(F_),Math.cos(r)<e&&(f=P_(N_.x,N_.y,B_.x,B_.y,E_.x,E_.y,H_,!1),W_.fromArray(H_),W_.scaleAndAdd(F_,f/Math.tan(Math.PI-r)),e=B_.x!==N_.x?(W_.x-N_.x)/(B_.x-N_.x):(W_.y-N_.y)/(B_.y-N_.y),isNaN(e)||(e<0?M.copy(W_,N_):1<e&&M.copy(W_,B_),W_.toArray(i[1]))))),n.setShape({points:o})}}}var H_=[],W_=new M;function G_(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function U_(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=Jt(i[0],i[1]),r=Jt(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=ne([],i[1],i[0],n/e),n=ne([],i[1],i[2],n/r),r=ne([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function X_(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new vh(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function q_(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&w(-e*n,1)):w(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function w(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function Y_(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new X(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new vh(f.localRect,f.transform)),(c=c||new vh(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function Z_(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var j_=["align","verticalAlign","width","height","fontSize"],K_=new vr,$_=Do(),Q_=Do();function J_(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var t1=["x","y","rotation"],e1=(n1.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},n1.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(X.applyTransform(u,u,l),l?K_.setLocalTransform(l):(K_.x=K_.y=K_.rotation=K_.originX=K_.originY=0,K_.scaleX=K_.scaleY=1),K_.rotation=ja(K_.rotation),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),X.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:K_.x,y:K_.y,scaleX:K_.scaleX,scaleY:K_.scaleY,rotation:K_.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},n1.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(k(r)||ct(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=Us(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},n1.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=k(r.layoutOption)?r.layoutOption(Z_(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=f(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=f(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),$_(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<j_.length;p++){var d=j_[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){V_(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},n1.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=X_(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});q_(o,"x","width",0,i,e),q_(a,"y","height",0,t,n),Y_(ut(r,function(t){return t.layoutOption.hideOverlap}))},n1.prototype.processLabelsOverall=function(){var a=this;O(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?$_(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},n1.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=Us(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),r=(r&&(n=n.getVisual("drawType"),i.stroke=r[n]),e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<Qs.length;i++){var r=Qs[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<Js.length;c++){var p,d=Js[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:E(l.states[d]&&l.states[d].ignore,h))||!E(p,u)?((p=g?s:s&&s.states[d])&&(p.ignore=!0),s&&G_(s,!0,d,f)):(s||(s=new Hu,o.setTextGuideLine(s),g||!h&&u||G_(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),G_(s,!1,d,f)))}s&&(B(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=U_)}else s&&o.removeTextGuideLine();V_(t,r)}},n1.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Ih(t))||(o=(r=$_(s)).oldLayout,n=(i=Us(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=I(t,"select")&&s.attr(r.oldLayoutSelect),0<=I(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),Th(s,a,e,n)):(s.attr(a),dc(s).valueAnimation||(t=E(s.style.opacity,1),s.style.opacity=0,Ch(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(J_(t=r.oldLayoutSelect={},a,t1),J_(t,s.states.select,t1)),s.states.emphasis&&(J_(t=r.oldLayoutEmphasis={},a,t1),J_(t,s.states.emphasis,t1)),fc(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=Q_(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),Th(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,Ch(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},n1);function n1(){this._labelList=[],this._chartViewList=[]}var i1=Do();function r1(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(i1(e).labelManager||(i1(e).labelManager=new e1)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=i1(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function o1(t,e,n){var i=H.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}$v(r1);u(l1,a1=he),l1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},l1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},l1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},l1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},l1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=o1("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},l1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new X(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new X(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new X(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new X(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&_n||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&_n||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a])&&s.shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},l1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},l1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},l1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,_t(o)?(r=(o.global||o.__width===n&&o.__height===i)&&o.__canvasGradient||by(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r,o.__width=n,o.__height=i):xt(o)&&(o.scaleX=o.scaleX||u,o.scaleY=o.scaleY||u,r=Ly(a,o,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&O(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var a1,s1=l1;function l1(t,e,n){var i,r=a1.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||ur,"string"==typeof t?i=o1(t,e,n):R(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(Ft(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var u1=314159;_.prototype.getType=function(){return"canvas"},_.prototype.isSingleCanvas=function(){return this._singleCanvas},_.prototype.getViewportRoot=function(){return this._domRoot},_.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},_.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},_.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},_.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),Xy(i,a,r,o===e-1))}i&&i.restore()}},_.prototype.getHoverLayer=function(){return this.getLayer(1e5)},_.prototype.paintOne=function(t,e){Uy(t,e)},_.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Cn(function(){a._paintList(t,e,n,i)})))},_.prototype._compositeManually=function(){var e=this.getLayer(u1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},_.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,w._width,w._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===w._zlevelList[0]?w._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=w.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},w=this,i=0;i<m.length;i++)n(i);return b.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},_.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(Xy(e,t,r,o),t.setPrevPaintRect(n))):Xy(e,t,r,o)},_.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=u1);var n=this._layers[t];return n||((n=new s1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},_.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},_.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},_.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},_.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},_.prototype.getLayers=function(){return this._layers},_.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&_n&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},_.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},_.prototype._clearLayer=function(t){t.clear()},_.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},_.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},_.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(I(n,t),1))},_.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=My(r,0,i),e=My(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(u1).resize(t,e)}return this},_.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},_.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},_.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[u1].dom;var e=new s1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];Xy(n,u,o,s===l-1)}return e.dom},_.prototype.getWidth=function(){return this._width},_.prototype.getHeight=function(){return this._height};var h1=_;function _(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=L({},n||{}),this.dpr=n.devicePixelRatio||ur,this._singleCanvas=r;(this.root=t).style&&(Ft(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new s1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[u1]=o).zlevel=u1,e.push(u1),this._domRoot=t):(this._width=My(t,0,n),this._height=My(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}u(d1,c1=y),d1.prototype.init=function(t,e,n){c1.prototype.init.call(this,t,e,n),this._sourceManager=new $f(this),Jf(this)},d1.prototype.mergeOption=function(t,e){c1.prototype.mergeOption.call(this,t,e),Jf(this)},d1.prototype.optionUpdated=function(){this._sourceManager.dirty()},d1.prototype.getSourceManager=function(){return this._sourceManager},d1.type="dataset",d1.defaultOption={seriesLayoutBy:Fp};var c1,p1=d1;function d1(){var t=null!==c1&&c1.apply(this,arguments)||this;return t.type="dataset",t}u(y1,f1=yg),y1.type="dataset";var f1,g1=y1;function y1(){var t=null!==f1&&f1.apply(this,arguments)||this;return t.type="dataset",t}function m1(t){t.registerComponentModel(p1),t.registerComponentView(g1)}function v1(t){t.eachSeriesByType("radar",function(t){var e,i=t.getData(),r=[],o=t.coordinateSystem;o&&(O(e=o.getIndicatorAxes(),function(t,n){i.each(i.mapDimension(e[n].dim),function(t,e){r[e]=r[e]||[];t=o.dataToPoint(t,n);r[e][n]=_1(t)?t:x1(o)})}),i.each(function(t){var e=ht(r[t],_1)||x1(o);r[t].push(e.slice()),i.setItemLayout(t,r[t])}))})}function _1(t){return!isNaN(t[0])&&!isNaN(t[1])}function x1(t){return[t.cx,t.cy]}function w1(n){var i,t=n.polar;t&&(F(t)||(t=[t]),i=[],O(t,function(t,e){(t.indicator?(t.type&&!t.shape&&(t.shape=t.type),n.radar=n.radar||[],F(n.radar)||(n.radar=[n.radar]),n.radar):i).push(t)}),n.polar=i),O(n.series,function(t){t&&"radar"===t.type&&t.polarIndex&&(t.radarIndex=t.polarIndex)})}$v([function(t){t.registerPainter("canvas",h1)},m1]),$v(r1);u(M1,b1=wg),M1.prototype.render=function(l,t,e){var n=l.coordinateSystem,o=this.group,w=l.getData(),a=this._data;function u(t,e){var n,i=t.getItemVisual(e,"symbol")||"circle";if("none"!==i)return n=[(n=F(n=t.getItemVisual(e,"symbolSize"))?n:[+n,+n])[0]||0,n[1]||0],i=xy(i,-1,-1,2,2),t=t.getItemVisual(e,"symbolRotate")||0,i.attr({style:{strokeNoScale:!0},z2:100,scaleX:n[0]/2,scaleY:n[1]/2,rotation:t*Math.PI/180||0}),i}function s(t,e,n,i,r,o){n.removeAll();for(var a=0;a<e.length-1;a++){var s=u(i,r);s&&(t[s.__dimIdx=a]?(s.setPosition(t[a]),ic[o?"initProps":"updateProps"](s,{x:e[a][0],y:e[a][1]},l,r)):s.setPosition(e[a]),n.add(s))}}function h(t){return z(t,function(t){return[n.cx,n.cy]})}w.diff(a).add(function(t){var e,n,i,r,o=w.getItemLayout(t);o&&(e=new Bu,n=new Hu,i={shape:{points:o}},e.shape.points=h(o),n.shape.points=h(o),Ch(e,i,l,t),Ch(n,i,l,t),i=new Wr,r=new Wr,i.add(n),i.add(e),i.add(r),s(n.shape.points,o,r,w,t,!0),w.setItemGraphicEl(t,i))}).update(function(t,e){var e=a.getItemGraphicEl(e),n=e.childAt(0),i=e.childAt(1),r=e.childAt(2),o={shape:{points:w.getItemLayout(t)}};o.shape.points&&(s(n.shape.points,o.shape.points,r,w,t,!1),Ah(i),Ah(n),Th(n,o,l),Th(i,o,l),w.setItemGraphicEl(t,e))}).remove(function(t){o.remove(a.getItemGraphicEl(t))}).execute(),w.eachItemGraphicEl(function(t,y){var m=w.getItemModel(y),e=t.childAt(0),n=t.childAt(1),i=t.childAt(2),v=w.getItemVisual(y,"style"),_=v.fill,e=(o.add(t),e.useStyle(B(m.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:_})),El(e,m,"lineStyle"),El(n,m,"areaStyle"),m.getModel("areaStyle")),r=e.isEmpty()&&e.parentModel.isEmpty(),e=(n.ignore=r,O(["emphasis","select","blur"],function(t){var e=m.getModel([t,"areaStyle"]),e=e.isEmpty()&&e.parentModel.isEmpty();n.ensureState(t).ignore=e&&r}),n.useStyle(B(e.getAreaStyle(),{fill:_,opacity:.7,decal:v.decal})),m.getModel("emphasis")),x=e.getModel("itemStyle").getItemStyle();i.eachChild(function(t){t instanceof vs?(e=t.style,t.useStyle(L({image:e.image,x:e.x,y:e.y,width:e.width,height:e.height},v))):(t.useStyle(v),t.setColor(_),t.style.strokeNoScale=!0);t.ensureState("emphasis").style=S(x);var e=w.getStore().get(w.getDimensionIndex(t.__dimIdx),y),n=(null!=e&&!isNaN(e)||(e=""),t),i=function(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<Qs.length;i++){var r=Qs[i];n[r]=t.getModel([r,e])}return n}(m),r={labelFetcher:w.hostModel,labelDataIndex:y,labelDimIndex:t.__dimIdx,defaultText:e,inheritColor:_,defaultOpacity:v.opacity},o=void 0;r=r||rc;for(var a=n instanceof Ls,s=!1,l=0;l<Js.length;l++)if((p=i[Js[l]])&&p.getShallow("show")){s=!0;break}var u=a?n:n.getTextContent();if(s){a||(u||(u=new Ls,n.setTextContent(u)),n.stateProxy&&(u.stateProxy=n.stateProxy));var h=ac(r,i),t=i.normal,c=!!t.getShallow("show"),e=sc(t,o&&o.normal,r,!1,!a);e.text=h.normal,a||n.setTextConfig(lc(t,r,!1));for(l=0;l<Qs.length;l++){var p,d,f,g=Qs[l];(p=i[g])&&(d=u.ensureState(g),(f=!!E(p.getShallow("show"),c))!=c&&(d.ignore=!f),d.style=sc(p,o&&o[g],r,!0,!a),d.style.text=h[g],a||(n.ensureState(g).textConfig=lc(p,r,!0)))}u.silent=!!t.getShallow("silent"),null!=u.style.x&&(e.x=u.style.x),null!=u.style.y&&(e.y=u.style.y),u.ignore=!c,u.useStyle(e),u.dirty(),r.enableTextSetter&&(dc(u).setLabelText=function(t){t=ac(r,i,t);oc(u,t)})}else u&&(u.ignore=!0);n.dirty()}),Ll(t,e.get("focus"),e.get("blurScope"),e.get("disabled"))}),this._data=w},M1.prototype.remove=function(){this.group.removeAll(),this._data=null},M1.type="radar";var b1,S1=M1;function M1(){var t=null!==b1&&b1.apply(this,arguments)||this;return t.type=M1.type,t}C1.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},C1.prototype.containName=function(t){return 0<=this._getRawData().indexOfName(t)},C1.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},C1.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)};var T1=C1;function C1(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}u(D1,I1=lg),D1.prototype.init=function(t){I1.prototype.init.apply(this,arguments),this.legendVisualProvider=new T1(pt(this.getData,this),pt(this.getRawData,this))},D1.prototype.getInitialData=function(t,e){return n=this,i=F(i={generateCoord:"indicator_",generateCoordCount:1/0})?{coordDimensions:i}:L({encodeDefine:n.getEncode()},i),o=n.getSource(),i=Vm(o,i).dimensions,(i=new Fm(i,n)).initData(o,r),i;var n,i,r,o},D1.prototype.formatTooltip=function(n,t,e){var i=this.getData(),r=this.coordinateSystem.getIndicatorAxes(),o=this.getData().getName(n),o=""===o?this.name:o,a=ig(this,n);return ng("section",{header:o,sortBlocks:!0,blocks:z(r,function(t){var e=i.get(i.mapDimension(t.dim),n);return ng("nameValue",{markerType:"subItem",markerColor:a,name:t.name,value:e,sortParam:e})})})},D1.prototype.getTooltipPosition=function(t){if(null!=t)for(var e,n=this.getData(),i=this.coordinateSystem,r=n.getValues(z(i.dimensions,function(t){return n.mapDimension(t)}),t),o=0,a=r.length;o<a;o++)if(!isNaN(r[o]))return e=i.getIndicatorAxes(),i.coordToPoint(e[o].dataToCoord(r[o]),o)},D1.type="series.radar",D1.dependencies=["radar"],D1.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8};var I1,k1=D1;function D1(){var t=null!==I1&&I1.apply(this,arguments)||this;return t.type=D1.type,t.hasSymbolVisual=!0,t}var wc={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Wo=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},wc),hy=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},wc),A1={category:Wo,value:hy,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},hy),log:B({logBase:10},hy)},gy=A1.value;function P1(t,e){return B({show:e},t)}u(R1,L1=y),R1.prototype.optionUpdated=function(){var n=this.get("boundaryGap"),i=this.get("splitNumber"),r=this.get("scale"),o=this.get("axisLine"),a=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),h=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),p=this.get("triggerEvent"),t=z(this.get("indicator")||[],function(t){null!=t.max&&0<t.max&&!t.min?t.min=0:null!=t.min&&t.min<0&&!t.max&&(t.max=0);var e=l,t=(null!=t.color&&(e=B({color:t.color},l)),d(S(t),{boundaryGap:n,splitNumber:i,scale:r,axisLine:o,axisTick:a,axisLabel:s,name:t.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:e,triggerEvent:p},!1)),e=(V(h)?(e=t.name,t.name=h.replace("{value}",null!=e?e:"")):k(h)&&(t.name=h(t.name,t)),new kc(t,null,this.ecModel));return at(e,Yv.prototype),e.mainType="radar",e.componentIndex=this.componentIndex,e},this);this._indicatorModels=t},R1.prototype.getIndicatorModels=function(){return this._indicatorModels},R1.type="radar",R1.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:d({lineStyle:{color:"#bbb"}},gy.axisLine),axisLabel:P1(gy.axisLabel,!1),axisTick:P1(gy.axisTick,!1),splitLine:P1(gy.splitLine,!0),splitArea:P1(gy.splitArea,!0),indicator:[]};var L1,O1=R1;function R1(){var t=null!==L1&&L1.apply(this,arguments)||this;return t.type=R1.type,t}var E1=Math.PI,N1=(B1.prototype.hasBuilder=function(t){return!!z1[t]},B1.prototype.add=function(t){z1[t](this.opt,this.axisModel,this.group,this._transformGroup)},B1.prototype.getGroup=function(){return this.group},B1.innerTextLayout=function(t,e,n){var i,e=ro(e-t),t=oo(e)?(i=0<n?"top":"bottom","center"):oo(e-E1)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<E1?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},B1.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},B1.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},B1);function B1(t,e){this.group=new Wr,this.opt=e,this.axisModel=t,B(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Wr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var z1={axisLine:function(i,t,r,e){var o,a,s,l,u,h,c,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],a=[n[1],0],s=a[0]<o[0],e&&(ie(o,o,e),ie(a,a,e)),l=L({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),Uh((n=new qu({shape:{x1:o[0],y1:o[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).shape,n.style.lineWidth),n.anid="line",r.add(n),null!=(u=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(u)&&(u=[u,u]),(V(e)||gt(e))&&(e=[e,e]),n=function(t,e){if(null!=t)return[f((t=F(t)?t:[t,t])[0],e[0])||0,f(E(t[1],t[0]),e[1])||0]}(t.get(["axisLine","symbolOffset"])||0,e),h=e[0],c=e[1],O([{rotate:i.rotation+Math.PI/2,offset:n[0],r:0},{rotate:i.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((o[0]-a[0])*(o[0]-a[0])+(o[1]-a[1])*(o[1]-a[1]))}],function(t,e){var n;"none"!==u[e]&&null!=u[e]&&(e=xy(u[e],-h/2,-c/2,h,c,l.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:(t=s?a:o)[0]+n*Math.cos(i.rotation),y:t[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=W1(r.getTicksCoords(),e.transform,i,B(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(g,y,m,v){var _,x,w,b,S,M,T,C,I=m.axis,t=St(v.axisLabelShow,m.get(["axisLabel","show"]));if(t&&!I.scale.isBlank())return _=m.getModel("axisLabel"),x=_.get("margin"),w=I.getViewLabels(),t=(St(v.labelRotate,_.get("rotate"))||0)*E1/180,b=N1.innerTextLayout(v.rotation,t,v.labelDirection),S=m.getCategories&&m.getCategories(!0),M=[],T=N1.isLabelSilent(m),C=m.get("triggerEvent"),O(w,function(t,e){var n="ordinal"===I.scale.type?I.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=_,a=(o=S&&S[n]&&R(a=S[n])&&a.textStyle?new kc(a.textStyle,_,m.ecModel):o).getTextColor()||m.get(["axisLine","lineStyle","color"]),s=I.dataToCoord(n),l=o.getShallow("align",!0)||b.textAlign,u=E(o.getShallow("alignMinLabel",!0),l),h=E(o.getShallow("alignMaxLabel",!0),l),c=o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||b.textVerticalAlign,p=E(o.getShallow("verticalAlignMinLabel",!0),c),d=E(o.getShallow("verticalAlignMaxLabel",!0),c),f=new Ls({x:s,y:v.labelOffset+v.labelDirection*x,rotation:b.rotation,silent:T,z2:10+(t.level||0),style:sc(o,{text:i,align:0===e?u:e===w.length-1?h:l,verticalAlign:0===e?p:e===w.length-1?d:c,fill:k(a)?a("category"===I.type?r:"value"===I.type?n+"":n,e):a})});f.anid="label_"+n,tc({el:f,componentModel:m,itemName:i,formatterParamsExtra:{isTruncated:function(){return f.isTruncated},value:r,tickIndex:e}}),C&&((s=N1.makeAxisEventDataBase(m)).targetType="axisLabel",s.value=r,s.tickIndex=e,"category"===I.type&&(s.dataIndex=n),Us(f).eventData=s),y.add(f),f.updateTransform(),M.push(f),g.add(f),f.decomposeTransform()}),M}(n,i,e,t),c=(o=h,u=u,Xv((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(F1(y),F1(s)):V1(y,f)&&(d?(F1(f),F1(g)):(F1(y),F1(s))),!1===r?(F1(a),F1(l)):V1(o,a)&&(r?(F1(o),F1(u)):(F1(a),F1(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=B(g.getLineStyle(),B(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var w=W1(m[x],p.transform,v,_,"minorticks_"+x),b=0;b<w.length;b++)c.add(w[b])}e.get(["axisLabel","hideOverlap"])&&Y_(X_(z(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p=St(t.axisName,e.get("name"));p&&(c=e.get("nameLocation"),l=t.nameDirection,r=e.getModel("nameTextStyle"),u=e.get("nameGap")||0,o=(h=e.axis.getExtent())[0]>h[1]?-1:1,o=["start"===c?h[0]-o*u:"end"===c?h[1]+o*u:(h[0]+h[1])/2,H1(c)?t.labelOffset+l*u:0],null!=(u=e.get("nameRotate"))&&(u=u*E1/180),H1(c)?a=N1.innerTextLayout(t.rotation,null!=u?u:t.rotation,l):(a=function(t,e,n,i){var r,n=ro(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=oo(n-E1/2)?(r=i?"bottom":"top","center"):oo(n-1.5*E1)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*E1&&E1/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,c,u||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(a.rotation)),isFinite(s)||(s=null))),l=r.getFont(),u=(c=e.get("nameTruncate",!0)||{}).ellipsis,h=St(t.nameTruncateMaxWidth,c.maxWidth,s),tc({el:t=new Ls({x:o[0],y:o[1],rotation:a.rotation,silent:N1.isLabelSilent(e),style:sc(r,{text:p,font:l,overflow:"truncate",width:h,ellipsis:u,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||a.textAlign,verticalAlign:r.get("verticalAlign")||a.textVerticalAlign}),z2:1}),componentModel:e,itemName:p}),t.__fullText=p,t.anid="name",e.get("triggerEvent")&&((c=N1.makeAxisEventDataBase(e)).targetType="axisName",c.name=p,Us(t).eventData=c),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function F1(t){t&&(t.ignore=!0)}function V1(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return ze(n=Re([]),n,-t.rotation),i.applyTransform(Ne([],n,t.getLocalTransform())),r.applyTransform(Ne([],n,e.getLocalTransform())),i.intersect(r)}function H1(t){return"middle"===t||"center"===t}function W1(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(ie(a,a,e),ie(s,s,e)),new qu({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));Uh(u.shape,u.style.lineWidth),u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}var G1,U1=["axisLine","axisTickLabel","axisName"],X1=(u(q1,G1=yg),q1.prototype.render=function(t,e,n){this.group.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},q1.prototype._buildAxes=function(t){var n=t.coordinateSystem;O(z(n.getIndicatorAxes(),function(t){var e=t.model.get("showName")?t.name:"";return new N1(t.model,{axisName:e,position:[n.cx,n.cy],rotation:t.angle,labelDirection:-1,tickDirection:-1,nameDirection:1})}),function(t){O(U1,t.add,t),this.group.add(t.getGroup())},this)},q1.prototype._buildSplitLineAndArea=function(t){var n=t.coordinateSystem,e=n.getIndicatorAxes();if(e.length){var i=t.get("shape"),r=t.getModel("splitLine"),t=t.getModel("splitArea"),o=r.getModel("lineStyle"),a=t.getModel("areaStyle"),s=r.get("show"),l=t.get("show"),r=o.get("color"),t=a.get("color"),u=F(r)?r:[r],h=F(t)?t:[t],c=[],p=[];if("circle"===i)for(var d=e[0].getTicksCoords(),f=n.cx,g=n.cy,y=0;y<d.length;y++)s&&c[M(c,u,y)].push(new su({shape:{cx:f,cy:g,r:d[y].coord}})),l&&y<d.length-1&&p[M(p,h,y)].push(new Lu({shape:{cx:f,cy:g,r0:d[y].coord,r:d[y+1].coord}}));else for(var m,v=z(e,function(t,e){t=t.getTicksCoords();return m=null==m?t.length-1:Math.min(t.length-1,m),z(t,function(t){return n.coordToPoint(t.coord,e)})}),_=[],y=0;y<=m;y++){for(var x=[],w=0;w<e.length;w++)x.push(v[w][y]);x[0]&&x.push(x[0].slice()),s&&c[M(c,u,y)].push(new Hu({shape:{points:x}})),l&&_&&p[M(p,h,y-1)].push(new Bu({shape:{points:x.concat(_)}})),_=x.slice().reverse()}var b=o.getLineStyle(),S=a.getAreaStyle();O(p,function(t,e){this.group.add(Wh(t,{style:B({stroke:"none",fill:h[e%h.length]},S),silent:!0}))},this),O(c,function(t,e){this.group.add(Wh(t,{style:B({fill:"none",stroke:u[e%u.length]},b),silent:!0}))},this)}function M(t,e,n){n%=e.length;return t[n]=t[n]||[],n}},q1.type="radar",q1);function q1(){var t=null!==G1&&G1.apply(this,arguments)||this;return t.type=q1.type,t}u(j1,Y1=Tc);var Y1,Z1=j1;function j1(t,e,n){t=Y1.call(this,t,e,n)||this;return t.type="value",t.angle=0,t.name="",t}var K1=Math.log;Q1.prototype.getIndicatorAxes=function(){return this._indicatorAxes},Q1.prototype.dataToPoint=function(t,e){var n=this._indicatorAxes[e];return this.coordToPoint(n.dataToCoord(t),e)},Q1.prototype.coordToPoint=function(t,e){e=this._indicatorAxes[e].angle;return[this.cx+t*Math.cos(e),this.cy-t*Math.sin(e)]},Q1.prototype.pointToData=function(t){for(var e,n=t[0]-this.cx,t=t[1]-this.cy,i=Math.sqrt(n*n+t*t),r=(n/=i,t/=i,Math.atan2(-t,n)),o=1/0,a=-1,s=0;s<this._indicatorAxes.length;s++){var l=this._indicatorAxes[s],u=Math.abs(r-l.angle);u<o&&(e=l,a=s,o=u)}return[a,+(e&&e.coordToData(i))]},Q1.prototype.resize=function(t,e){var n=t.get("center"),i=e.getWidth(),e=e.getHeight(),r=Math.min(i,e)/2,i=(this.cx=f(n[0],i),this.cy=f(n[1],e),this.startAngle=t.get("startAngle")*Math.PI/180,t.get("radius"));(V(i)||gt(i))&&(i=[0,i]),this.r0=f(i[0],r),this.r=f(i[1],r),O(this._indicatorAxes,function(t,e){t.setExtent(this.r0,this.r);e=this.startAngle+e*Math.PI*2/this._indicatorAxes.length,e=Math.atan2(Math.sin(e),Math.cos(e));t.angle=e},this)},Q1.prototype.update=function(i,t){var r=this._indicatorAxes,o=this._model,e=(O(r,function(t){t.scale.setExtent(1/0,-1/0)}),i.eachSeriesByType("radar",function(t,e){var n;"radar"===t.get("coordinateSystem")&&i.getComponent("radar",t.get("radarIndex"))===o&&(n=t.getData(),O(r,function(t){t.scale.unionExtentFromData(n,n.mapDimension(t.dim))}))},this),o.get("splitNumber")),f=new pv;f.setExtent(0,e),f.setInterval(1),O(r,function(t,e){var n=t.scale,t=t.model,i=f,r=pv.prototype,o=r.getTicks.call(i),a=r.getTicks.call(i,!0),s=o.length-1,i=r.getInterval.call(i),l=(t=Fv(n,t)).extent,u=t.fixMin,t=t.fixMax,h=("log"===n.type&&(h=K1(n.base),l=[K1(l[0])/h,K1(l[1])/h]),n.setExtent(l[0],l[1]),n.calcNiceExtent({splitNumber:s,fixMin:u,fixMax:t}),r.getExtent.call(n)),c=(u&&(l[0]=h[0]),t&&(l[1]=h[1]),r.getInterval.call(n)),p=l[0],d=l[1];if(u&&t)c=(d-p)/s;else if(u)for(d=l[0]+c*s;d<l[1]&&isFinite(d)&&isFinite(l[1]);)c=ev(c),d=l[0]+c*s;else if(t)for(p=l[1]-c*s;p>l[0]&&isFinite(p)&&isFinite(l[0]);)c=ev(c),p=l[1]-c*s;else{h=(c=s<n.getTicks().length-1?ev(c):c)*s;(p=to((d=Math.ceil(l[1]/c)*c)-h))<0&&0<=l[0]?(p=0,d=to(h)):0<d&&l[1]<=0&&(d=0,p=-to(h))}u=(o[0].value-a[0].value)/i,t=(o[s].value-a[s].value)/i,r.setExtent.call(n,p+c*u,d+c*t),r.setInterval.call(n,c),(u||t)&&r.setNiceExtent.call(n,p+c,d-c)})},Q1.prototype.convertToPixel=function(t,e,n){return console.warn("Not implemented."),null},Q1.prototype.convertFromPixel=function(t,e,n){return console.warn("Not implemented."),null},Q1.prototype.containPoint=function(t){return console.warn("Not implemented."),!1},Q1.create=function(t,n){var i=[];return t.eachComponent("radar",function(t){var e=new Q1(t,0,n);i.push(e),t.coordinateSystem=e}),t.eachSeriesByType("radar",function(t){"radar"===t.get("coordinateSystem")&&(t.coordinateSystem=i[t.get("radarIndex")||0])}),i},Q1.dimensions=[];var $1=Q1;function Q1(t,e,n){this.dimensions=[],this._model=t,this._indicatorAxes=z(t.getIndicatorModels(),function(t,e){var e="indicator_"+e,n=new Z1(e,new pv);return n.name=t.get("name"),(n.model=t).axis=n,this.dimensions.push(e),n},this),this.resize(t,n)}function J1(t){t.registerCoordinateSystem("radar",$1),t.registerComponentModel(O1),t.registerComponentView(X1),t.registerVisual({seriesType:"radar",reset:function(t){var e=t.getData();e.each(function(t){e.setItemVisual(t,"legendIcon","roundRect")}),e.setVisual("legendIcon","roundRect")}})}function tx(t,e){var h,c,r,p,d,f,o,n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return h=n,e=e,r=(c=t).getComponent("tooltip"),p=c.getComponent("axisPointer"),d=p.get("link",!0)||[],f=[],O(e.getCoordinateSystems(),function(s){var l,u,t,e,n;function i(t,e,n){var i,r,o=n.model.getModel("axisPointer",p),a=o.get("show");a&&("auto"!==a||t||ix(o))&&(null==e&&(e=o.get("triggerTooltip")),a=(o=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};O(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=S(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");e=s.label||(s.label={});null==e.show&&(e.show=!1),"cross"===r&&(r=a.get(["label","show"]),e.show=null==r||r,o||(r=s.lineStyle=a.get("crossStyle"))&&B(e,r.textStyle));return t.model.getModel("axisPointer",new kc(s,n,i))}(n,u,p,c,t,e):o).get("snap"),t=o.get("triggerEmphasis"),i=rx(n.model),r=e||a||"category"===n.type,e=h.axesInfo[i]={key:i,axis:n,coordSys:s,axisPointerModel:o,triggerTooltip:e,triggerEmphasis:t,involveSeries:r,snap:a,useHandle:ix(o),seriesModels:[],linkGroup:null},l[i]=e,h.seriesInvolved=h.seriesInvolved||r,null!=(t=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(ex(o[i+"AxisId"],n.id)||ex(o[i+"AxisIndex"],n.componentIndex)||ex(o[i+"AxisName"],n.name))return r}}(d,n)))&&((a=f[t]||(f[t]={axesInfo:{}})).axesInfo[i]=e,a.mapper=d[t].mapper,e.linkGroup=a)}s.axisPointerEnabled&&(t=rx(s.model),l=h.coordSysAxesInfo[t]={},u=(h.coordSysMap[t]=s).model.getModel("tooltip",r),O(s.getAxes(),dt(i,!1,null)),s.getTooltipAxes)&&r&&u.get("show")&&(t="axis"===u.get("trigger"),e="cross"===u.get(["axisPointer","type"]),n=s.getTooltipAxes(u.get(["axisPointer","axis"])),(t||e)&&O(n.baseAxes,dt(i,!e||"cross",t)),e)&&O(n.otherAxes,dt(i,"cross",!1))}),n.seriesInvolved&&(o=n,t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get(["tooltip","trigger"],!0),e=n.get(["tooltip","show"],!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get(["axisPointer","show"],!0)&&O(o.coordSysAxesInfo[rx(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})})),n}function ex(t,e){return"all"===t||F(t)&&0<=I(t,e)||t===e}function nx(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[rx(t)]}function ix(t){return!!t.get(["handle","show"])}function rx(t){return t.type+"||"+t.id}$v(function(t){$v(J1),t.registerChartView(S1),t.registerSeriesModel(k1),t.registerLayout(v1),t.registerProcessor({seriesType:"radar",reset:function(t,e){var i,r=e.findComponents({mainType:"legend"});r&&r.length&&(i=t.getData()).filterSelf(function(t){for(var e=i.getName(t),n=0;n<r.length;n++)if(!r[n].isSelected(e))return!1;return!0})}}),t.registerPreprocessor(w1)});var ox,ax={},sx=(u(lx,ox=yg),lx.prototype.render=function(t,e,n,i){var r,o,a,s,l,u;this.axisPointerClass&&(r=nx(r=t))&&(l=r.axisPointerModel,o=r.axis.scale,a=l.option,u=l.get("status"),null!=(s=l.get("value"))&&(s=o.parse(s)),l=ix(l),null==u&&(a.status=l?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(s=null==s||s>u[1]?u[1]:s)<u[0]&&(s=u[0]),a.value=s,l)&&(a.status=r.axis.scale.isBlank()?"hide":"show"),ox.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,n,!0)},lx.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},lx.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},lx.prototype.dispose=function(t,e){this._disposeAxisPointer(e),ox.prototype.dispose.apply(this,arguments)},lx.prototype._doUpdateAxisPointerClass=function(t,e,n){var i,r=lx.getAxisPointerClass(this.axisPointerClass);r&&((i=(i=nx(i=t))&&i.axisPointerModel)?(this._axisPointer||(this._axisPointer=new r)).render(t,i,e,n):this._disposeAxisPointer(e))},lx.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},lx.registerAxisPointerClass=function(t,e){ax[t]=e},lx.getAxisPointerClass=function(t){return t&&ax[t]},lx.type="axis",lx);function lx(){var t=null!==ox&&ox.apply(this,arguments)||this;return t.type=lx.type,t}var ux=Do(),hx=S,cx=pt,Qu=(px.prototype.render=function(t,e,n,i){var r,o,a=e.get("value"),s=e.get("status");this._axisModel=t,this._axisPointerModel=e,this._api=n,!i&&this._lastValue===a&&this._lastStatus===s||(this._lastValue=a,this._lastStatus=s,i=this._group,r=this._handle,s&&"hide"!==s?(i&&i.show(),r&&r.show(),this.makeElOption(s={},a,t,e,n),(o=s.graphicKey)!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=o,o=this._moveAnimation=this.determineAnimation(t,e),i?(o=dt(dx,e,o),this.updatePointerEl(i,s,o),this.updateLabelEl(i,s,o,e)):(i=this._group=new Wr,this.createPointerEl(i,s,t,e),this.createLabelEl(i,s,t,e),n.getZr().add(i)),yx(i,e,!0),this._renderHandle(a)):(i&&i.hide(),r&&r.hide()))},px.prototype.remove=function(t){this.clear(t)},px.prototype.dispose=function(t){this.clear(t)},px.prototype.determineAnimation=function(t,e){var n,i=e.get("animation"),r=t.axis,o="category"===r.type,e=e.get("snap");return!(!e&&!o)&&("auto"===i||null==i?(n=this.animationThreshold,o&&r.getBandWidth()>n||!!e&&(o=nx(t).seriesDataCount,e=r.getExtent(),Math.abs(e[0]-e[1])/o>n)):!0===i)},px.prototype.makeElOption=function(t,e,n,i,r){},px.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;r&&(r=ux(t).pointerEl=new ic[r.type](hx(e.pointer)),t.add(r))},px.prototype.createLabelEl=function(t,e,n,i){e.label&&(e=ux(t).labelEl=new Ls(hx(e.label)),t.add(e),fx(e,i))},px.prototype.updatePointerEl=function(t,e,n){t=ux(t).pointerEl;t&&e.pointer&&(t.setStyle(e.pointer.style),n(t,{shape:e.pointer.shape}))},px.prototype.updateLabelEl=function(t,e,n,i){t=ux(t).labelEl;t&&(t.setStyle(e.label.style),n(t,{x:e.label.x,y:e.label.y}),fx(t,i))},px.prototype._renderHandle=function(t){var e,n,i,r,o,a;!this._dragging&&this.updateHandleTransform&&(e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),a=e.get("status"),r.get("show")&&a&&"hide"!==a?(this._handle||(o=!0,i=this._handle=Qh(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){ke(t.event)},onmousedown:cx(this._onHandleDragMove,this,0,0),drift:cx(this._onHandleDragMove,this),ondragend:cx(this._onHandleDragEnd,this)}),n.add(i)),yx(i,e,!1),i.setStyle(r.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"])),F(a=r.get("size"))||(a=[a,a]),i.scaleX=a[0]/2,i.scaleY=a[1]/2,function(t,e,n,i){var r=t[e];if(r){var o=r[kg]||r,a=r[Ag];if(r[Dg]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=Pg(o,n,"debounce"===i))[kg]=o,r[Ag]=i,r[Dg]=n}}}(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)):(i&&n.remove(i),this._handle=null))},px.prototype._moveHandleToValue=function(t,e){dx(this._axisPointerModel,!e&&this._moveAnimation,this._handle,gx(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},px.prototype._onHandleDragMove=function(t,e){var n=this._handle;n&&(this._dragging=!0,t=this.updateHandleTransform(gx(n),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=t,n.stopAnimation(),n.attr(gx(t)),ux(n).lastProp=null,this._doDispatchAxisPointer())},px.prototype._doDispatchAxisPointer=function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},px.prototype._onHandleDragEnd=function(){var t;this._dragging=!1,this._handle&&(t=this._axisPointerModel.get("value"),this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"}))},px.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var t=t.getZr(),e=this._group,n=this._handle;t&&e&&(this._lastGraphicKey=null,e&&t.remove(e),n&&t.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),(n=(e=this)[t="_doDispatchAxisPointer"])&&n[kg]&&(n.clear&&n.clear(),e[t]=n[kg])},px.prototype.doClear=function(){},px.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}},px);function px(){this._dragging=!1,this.animationThreshold=15}function dx(t,e,n,i){!function n(i,t){{var r;return R(i)&&R(t)?(r=!0,O(t,function(t,e){r=r&&n(i[e],t)}),!!r):i===t}}(ux(n).lastProp,i)&&(ux(n).lastProp=i,e?Th(n,i,t):(n.stopAnimation(),n.attr(i)))}function fx(t,e){t[e.get(["label","show"])?"show":"hide"]()}function gx(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function yx(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function mx(t){var e,n=t.get("type"),t=t.getModel(n+"Style");return"line"===n?(e=t.getLineStyle()).fill=null:"shadow"===n&&((e=t.getAreaStyle()).stroke=null),e}function vx(t,e,n,i,r){var o=function(t,e,n,i,r){t=e.scale.parse(t);var o=e.scale.getLabel({value:t},{precision:r.precision}),r=r.formatter;{var a;r&&(a={value:Gv(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},O(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,e=e&&e.getDataParams(t);e&&a.seriesData.push(e)}),V(r)?o=r.replace("{value}",o):k(r)&&(o=r(a)))}return o}(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),n=n.getModel("label"),a=fp(n.get("padding")||0),s=n.getFont(),l=Mr(o,s),u=r.position,h=l.width+a[1]+a[3],l=l.height+a[0]+a[2],c=r.align,c=("right"===c&&(u[0]-=h),"center"===c&&(u[0]-=h/2),r.verticalAlign),i=("bottom"===c&&(u[1]-=l),"middle"===c&&(u[1]-=l/2),r=u,c=h,h=l,i=(l=i).getWidth(),l=l.getHeight(),r[0]=Math.min(r[0]+c,i)-c,r[1]=Math.min(r[1]+h,l)-h,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0),n.get("backgroundColor"));i&&"auto"!==i||(i=e.get(["axisLine","lineStyle","color"])),t.label={x:u[0],y:u[1],style:sc(n,{text:o,font:s,fill:n.getTextColor(),padding:a,backgroundColor:i}),z2:10}}function _x(t,e,n){var i=Oe();return ze(i,i,n.rotation),Be(i,i,n.position),Yh([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function xx(t,e,n){return{x1:t[n=n||0],y1:t[1-n],x2:e[n],y2:e[1-n]}}function bx(t,e,n,i,r,o){return{cx:t,cy:e,r0:n,r:i,startAngle:r,endAngle:o,clockwise:!0}}u(Tx,Sx=Qu),Tx.prototype.makeElOption=function(t,e,n,i,r){var o,a=n.axis,s=("angle"===a.dim&&(this.animationThreshold=Math.PI/18),a.polar),l=s.getOtherAxis(a).getExtent(),u=a.dataToCoord(e),h=i.get("type"),a=(h&&"none"!==h&&(o=mx(i),(h=Cx[h](a,s,u,l)).style=o,t.graphicKey=h.type,t.pointer=h),i.get(["label","margin"]));vx(t,n,i,r,function(t,e,n,i){var r,o,a=e.axis,t=a.dataToCoord(t),s=(l=(l=n.getAngleAxis().getExtent()[0])/180*Math.PI,n.getRadiusAxis().getExtent());{var l;s="radius"===a.dim?(a=Oe(),ze(a,a,l),Be(a,a,[n.cx,n.cy]),r=Yh([t,-i],a),a=e.getModel("axisLabel").get("rotate")||0,e=N1.innerTextLayout(l,a*Math.PI/180,-1),o=e.textAlign,e.textVerticalAlign):(l=s[1],r=n.coordToPoint([l+i,t]),a=n.cx,e=n.cy,o=Math.abs(r[0]-a)/l<.3?"center":r[0]>a?"left":"right",Math.abs(r[1]-e)/l<.3?"middle":r[1]>e?"top":"bottom")}return{position:r,align:o,verticalAlign:s}}(e,n,s,a))};var Sx,Mx=Tx;function Tx(){return null!==Sx&&Sx.apply(this,arguments)||this}var Cx={line:function(t,e,n,i){return"angle"===t.dim?{type:"Line",shape:xx(e.coordToPoint([i[0],n]),e.coordToPoint([i[1],n]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:n}}},shadow:function(t,e,n,i){var r=Math.max(1,t.getBandWidth()),o=Math.PI/180;return"angle"===t.dim?{type:"Sector",shape:bx(e.cx,e.cy,i[0],i[1],(-n-r/2)*o,(r/2-n)*o)}:{type:"Sector",shape:bx(e.cx,e.cy,n-r/2,n+r/2,0,2*Math.PI)}}};function Ix(t,e,n){n=n||{};var t=t.coordinateSystem,i=e.axis,r={},o=i.getAxesOnZeroOf()[0],a=i.position,s=o?"onZero":a,i=i.dim,t=t.getRect(),t=[t.x,t.x+t.width,t.y,t.y+t.height],l={left:0,right:1,top:0,bottom:1,onZero:2},u=e.get("offset")||0,u="x"===i?[t[2]-u,t[3]+u]:[t[0]-u,t[1]+u],h=(o&&(h=o.toGlobalCoord(o.dataToCoord(0)),u[l.onZero]=Math.max(Math.min(h,u[1]),u[0])),r.position=["y"===i?u[l[s]]:t[0],"x"===i?u[l[s]]:t[3]],r.rotation=Math.PI/2*("x"===i?0:1),r.labelDirection=r.tickDirection=r.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],r.labelOffset=o?u[l[a]]-u[l.onZero]:0,e.get(["axisTick","inside"])&&(r.tickDirection=-r.tickDirection),St(n.labelInside,e.get(["axisLabel","inside"]))&&(r.labelDirection=-r.labelDirection),e.get(["axisLabel","rotate"]));return r.labelRotate="top"===s?-h:h,r.z2=1,r}u(Ax,kx=Qu),Ax.prototype.makeElOption=function(t,e,n,i,r){var o,a=n.axis,s=a.grid,l=i.get("type"),u=Px(s,a).getOtherAxis(a).getGlobalExtent(),h=a.toGlobalCoord(a.dataToCoord(e,!0)),a=(l&&"none"!==l&&(o=mx(i),(l=Lx[l](a,h,u)).style=o,t.graphicKey=l.type,t.pointer=l),Ix(s.model,n));h=e,u=t,o=a,l=n,s=i,e=r,t=N1.innerTextLayout(o.rotation,0,o.labelDirection),o.labelMargin=s.get(["label","margin"]),vx(u,l,s,e,{position:_x(l.axis,h,o),align:t.textAlign,verticalAlign:t.textVerticalAlign})},Ax.prototype.getHandleTransform=function(t,e,n){var i=Ix(e.axis.grid.model,e,{labelInside:!1}),n=(i.labelMargin=n.get(["handle","margin"]),_x(e.axis,t,i));return{x:n[0],y:n[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},Ax.prototype.updateHandleTransform=function(t,e,n,i){var n=n.axis,r=n.grid,o=n.getGlobalExtent(!0),r=Px(r,n).getOtherAxis(n).getGlobalExtent(),n="x"===n.dim?0:1,a=[t.x,t.y],e=(a[n]+=e[n],a[n]=Math.min(o[1],a[n]),a[n]=Math.max(o[0],a[n]),(r[1]+r[0])/2),o=[e,e];o[n]=a[n];return{x:a[0],y:a[1],rotation:t.rotation,cursorPoint:o,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][n]}};var kx,Dx=Ax;function Ax(){return null!==kx&&kx.apply(this,arguments)||this}function Px(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var Lx={line:function(t,e,n){return{type:"Line",subPixelOptimize:!0,shape:xx([e,n[0]],[e,n[1]],Ox(t))}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:(e=[e-i/2,n[0]],n=[i,r],i=Ox(t),{x:e[i=i||0],y:e[1-i],width:n[i],height:n[1-i]})}}};function Ox(t){return"x"===t.dim?0:1}u(Nx,Rx=y),Nx.type="axisPointer",Nx.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}};var Rx,Ex=Nx;function Nx(){var t=null!==Rx&&Rx.apply(this,arguments)||this;return t.type=Nx.type,t}var Bx=Do(),zx=O;function Fx(t,e,n){var i,c,p;function r(t,h){c.on(t,function(e){n=p;var n,i,r={dispatchAction:o,pendings:i={showTip:[],hideTip:[]}};function o(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=o,n.dispatchAction(t))}zx(Bx(c).records,function(t){t&&h(t,e,r.dispatchAction)});var t,a=r.pendings,s=p,l=a.showTip.length,u=a.hideTip.length;l?t=a.showTip[l-1]:u&&(t=a.hideTip[u-1]),t&&(t.dispatchAction=null,s.dispatchAction(t))})}b.node||(i=e.getZr(),Bx(i).records||(Bx(i).records={}),p=e,Bx(c=i).initialized||(Bx(c).initialized=!0,r("click",dt(Hx,"click")),r("mousemove",dt(Hx,"mousemove")),r("globalout",Vx)),(Bx(i).records[t]||(Bx(i).records[t]={})).handler=n)}function Vx(t,e,n){t.handler("leave",null,n)}function Hx(t,e,n,i){e.handler(t,n,i)}function Wx(t,e){b.node||(e=e.getZr(),(Bx(e).records||{})[t]&&(Bx(e).records[t]=null))}u(Xx,Gx=yg),Xx.prototype.render=function(t,e,n){var e=e.getComponent("tooltip"),i=t.get("triggerOn")||e&&e.get("triggerOn")||"mousemove|click";Fx("axisPointer",n,function(t,e,n){"none"!==i&&("leave"===t||0<=i.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},Xx.prototype.remove=function(t,e){Wx("axisPointer",e)},Xx.prototype.dispose=function(t,e){Wx("axisPointer",e)},Xx.type="axisPointer";var Gx,Ux=Xx;function Xx(){var t=null!==Gx&&Gx.apply(this,arguments)||this;return t.type=Xx.type,t}var qx=Do();function Yx(t,e,n){var i,o,a,r,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M=t.currTrigger,T=[t.x,t.y],C=t,I=t.dispatchAction||pt(n.dispatchAction,n),k=e.getComponent("axisPointer").coordSysAxesInfo;if(k)return Qx(T)&&(h={seriesIndex:C.seriesIndex,dataIndex:C.dataIndex},e=e,m=[],v=h.seriesIndex,T=(null==v||!(e=e.getSeriesByIndex(v))||null==(v=ko(i=e.getData(),h))||v<0||F(v)?{point:[]}:(_=i.getItemGraphicEl(v),y=e.coordinateSystem,e.getTooltipPosition?m=e.getTooltipPosition(v)||[]:y&&y.dataToPoint?m=h.isStacked?(e=y.getBaseAxis(),h=y.getOtherAxis(e).dim,e=e.dim,h="x"===h||"radius"===h?1:0,e=i.mapDimension(e),(f=[])[h]=i.get(e,v),f[1-h]=i.get(i.getCalculationInfo("stackResultDimension"),v),y.dataToPoint(f)||[]):y.dataToPoint(i.getValues(z(y.dimensions,function(t){return i.mapDimension(t)}),v))||[]:_&&((e=_.getBoundingRect().clone()).applyTransform(_.transform),m=[e.x+e.width/2,e.y+e.height/2]),{point:m,el:_})).point),o=Qx(T),a=C.axesInfo,r=k.axesInfo,s="leave"===M||Qx(T),l={},h={list:[],map:{}},c={showPointer:dt(jx,u={}),showTooltip:dt(Kx,h)},O(k.coordSysMap,function(t,e){var r=o||t.containPoint(T);O(k.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(a,t);s||!r||a&&!i||null!=(i=null!=(i=i&&i.value)||o?i:n.pointToData(T))&&Zx(t,i,c,!1,l)})}),p={},O(r,function(n,t){var i=n.linkGroup;i&&!u[t]&&O(i.axesInfo,function(t,e){var e=u[e];t!==n&&e&&(e=e.value,i.mapper&&(e=n.axis.scale.parse(i.mapper(e,$x(t),$x(n)))),p[n.key]=e)})}),O(p,function(t,e){Zx(r[e],t,c,!0,l)}),d=u,f=r,g=l.axesInfo=[],O(f,function(t,e){var n=t.axisPointerModel.option,e=d[e];e?(t.useHandle||(n.status="show"),n.value=e.value,n.seriesDataIndices=(e.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&g.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),y=h,v=t,e=I,Qx(m=T)||!y.list.length?e({type:"hideTip"}):(_=((y.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{},e({type:"showTip",escapeConnect:!0,x:m[0],y:m[1],tooltipOption:v.tooltipOption,position:v.position,dataIndexInside:_.dataIndexInside,dataIndex:_.dataIndex,seriesIndex:_.seriesIndex,dataByCoordSys:y.list})),C=r,t=(M=n).getZr(),I="axisPointerLastHighlights",x=qx(t)[I]||{},w=qx(t)[I]={},O(C,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&t.triggerEmphasis&&O(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;w[e]=t})}),b=[],S=[],O(x,function(t,e){w[e]||S.push(t)}),O(w,function(t,e){x[e]||b.push(t)}),S.length&&M.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:S}),b.length&&M.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:b}),l}function Zx(t,e,n,i,r){var o,a,s,l,u,h,c,p,d,f,g=t.axis;!g.scale.isBlank()&&g.containData(e)&&(t.involveSeries?(a=e,s=t.axis,l=s.dim,u=a,h=[],c=Number.MAX_VALUE,p=-1,O(t.seriesModels,function(e,t){var n,i=e.getData().mapDimensionsAll(l);if(e.getAxisTooltipData)var r=e.getAxisTooltipData(i,a,s),o=r.dataIndices,r=r.nestestValue;else{if(!(o=e.getData().indicesOfNearest(i[0],a,"category"===s.type?.5:null)).length)return;r=e.getData().get(i[0],o[0])}null!=r&&isFinite(r)&&(i=a-r,(n=Math.abs(i))<=c)&&((n<c||0<=i&&p<0)&&(c=n,p=i,u=r,h.length=0),O(o,function(t){h.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}),f=(o={payloadBatch:h,snapToValue:u}).snapToValue,(d=o.payloadBatch)[0]&&null==r.seriesIndex&&L(r,d[0]),!i&&t.snap&&g.containData(f)&&null!=f&&(e=f),n.showPointer(t,e,d),n.showTooltip(t,o,f)):n.showPointer(t,e))}function jx(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Kx(t,e,n,i){var r,o,n=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;e.triggerTooltip&&n.length&&(r=rx(e=e.coordSys.model),(o=t.map[r])||(o=t.map[r]={coordSysId:e.id,coordSysIndex:e.componentIndex,coordSysType:e.type,coordSysMainType:e.mainType,dataByAxis:[]},t.list.push(o)),o.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get(["label","precision"]),formatter:l.get(["label","formatter"])},seriesDataIndices:n.slice()}))}function $x(t){var e=t.axis.model,n={},t=n.axisDim=t.axis.dim;return n.axisIndex=n[t+"AxisIndex"]=e.componentIndex,n.axisName=n[t+"AxisName"]=e.name,n.axisId=n[t+"AxisId"]=e.id,n}function Qx(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Jx(t){sx.registerAxisPointerClass("CartesianAxisPointer",Dx),t.registerComponentModel(Ex),t.registerComponentView(Ux),t.registerPreprocessor(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),e=t.axisPointer.link)&&!F(e)&&(t.axisPointer.link=[e])}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=tx(t,e)}),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},Yx)}u(nw,tw=y),nw.prototype.findAxisModel=function(t){var e;return this.ecModel.eachComponent(t,function(t){t.getCoordSysModel()===this&&(e=t)},this),e},nw.type="polar",nw.dependencies=["radiusAxis","angleAxis"],nw.defaultOption={z:0,center:["50%","50%"],radius:"80%"};var tw,ew=nw;function nw(){var t=null!==tw&&tw.apply(this,arguments)||this;return t.type=nw.type,t}var iw={value:1,category:1,time:1,log:1};function rw(o,a,s,l){O(iw,function(t,r){var e,n=d(d({},A1[r],!0),l,!0),n=(u(i,e=s),i.prototype.mergeDefaultAndTheme=function(t,e){var n=wp(this),i=n?Sp(t):{};d(t,e.getTheme().get(r+"Axis")),d(t,this.getDefaultOption()),t.type=ow(t),n&&bp(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=$m.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=a+"Axis."+r,i.defaultOption=n,i);function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a+"Axis."+r,t}o.registerComponentModel(n)}),o.registerSubTypeDefaulter(a+"Axis",ow)}function ow(t){return t.type||(t.data?"category":"value")}u(sw,aw=y),sw.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",Oo).models[0]},sw.type="polarAxis";var aw,oh=sw;function sw(){return null!==aw&&aw.apply(this,arguments)||this}at(oh,Yv);u(hw,lw=oh),hw.type="angleAxis";var lw,uw=hw;function hw(){var t=null!==lw&&lw.apply(this,arguments)||this;return t.type=hw.type,t}u(dw,cw=oh),dw.type="radiusAxis";var cw,pw=dw;function dw(){var t=null!==cw&&cw.apply(this,arguments)||this;return t.type=dw.type,t}u(yw,fw=Tc),yw.prototype.pointToData=function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]};var fw,gw=yw;function yw(t,e){return fw.call(this,"radius",t,e)||this}gw.prototype.dataToRadius=Tc.prototype.dataToCoord,gw.prototype.radiusToData=Tc.prototype.coordToData;var mw,vw=Do(),_w=(u(xw,mw=Tc),xw.prototype.pointToData=function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},xw.prototype.calculateCategoryInterval=function(){var t,e,n=this.getLabelModel(),i=this.scale,r=i.getExtent(),i=i.count();return r[1]-r[0]<1?0:(r=r[0],t=this.dataToCoord(r+1)-this.dataToCoord(r),t=Math.abs(t),r=Mr(null==r?"":r+"",n.getFont(),"center","top"),n=Math.max(r.height,7)/t,isNaN(n)&&(n=1/0),r=Math.max(0,Math.floor(n)),n=(t=vw(this.model)).lastAutoInterval,e=t.lastTickCount,null!=n&&null!=e&&Math.abs(n-r)<=1&&Math.abs(e-i)<=1&&r<n?r=n:(t.lastTickCount=i,t.lastAutoInterval=r),r)},xw);function xw(t,e){return mw.call(this,"angle",t,e||[0,360])||this}_w.prototype.dataToAngle=Tc.prototype.dataToCoord,_w.prototype.angleToData=Tc.prototype.coordToData;var ww=["radius","angle"],bw=(Sw.prototype.containPoint=function(t){t=this.pointToCoord(t);return this._radiusAxis.contain(t[0])&&this._angleAxis.contain(t[1])},Sw.prototype.containData=function(t){return this._radiusAxis.containData(t[0])&&this._angleAxis.containData(t[1])},Sw.prototype.getAxis=function(t){return this["_"+t+"Axis"]},Sw.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},Sw.prototype.getAxesByScale=function(t){var e=[],n=this._angleAxis,i=this._radiusAxis;return n.scale.type===t&&e.push(n),i.scale.type===t&&e.push(i),e},Sw.prototype.getAngleAxis=function(){return this._angleAxis},Sw.prototype.getRadiusAxis=function(){return this._radiusAxis},Sw.prototype.getOtherAxis=function(t){var e=this._angleAxis;return t===e?this._radiusAxis:e},Sw.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},Sw.prototype.getTooltipAxes=function(t){t=null!=t&&"auto"!==t?this.getAxis(t):this.getBaseAxis();return{baseAxes:[t],otherAxes:[this.getOtherAxis(t)]}},Sw.prototype.dataToPoint=function(t,e){return this.coordToPoint([this._radiusAxis.dataToRadius(t[0],e),this._angleAxis.dataToAngle(t[1],e)])},Sw.prototype.pointToData=function(t,e){t=this.pointToCoord(t);return[this._radiusAxis.radiusToData(t[0],e),this._angleAxis.angleToData(t[1],e)]},Sw.prototype.pointToCoord=function(t){for(var e=t[0]-this.cx,t=t[1]-this.cy,n=this.getAngleAxis(),i=n.getExtent(),r=Math.min(i[0],i[1]),o=Math.max(i[0],i[1]),i=(n.inverse?r=o-360:o=r+360,Math.sqrt(e*e+t*t)),a=(e/=i,t/=i,Math.atan2(-t,e)/Math.PI*180),s=a<r?1:-1;a<r||o<a;)a+=360*s;return[i,a]},Sw.prototype.coordToPoint=function(t){var e=t[0],t=t[1]/180*Math.PI;return[Math.cos(t)*e+this.cx,-Math.sin(t)*e+this.cy]},Sw.prototype.getArea=function(){var t=this.getAngleAxis(),e=this.getRadiusAxis().getExtent().slice(),n=(e[0]>e[1]&&e.reverse(),t.getExtent()),i=Math.PI/180;return{cx:this.cx,cy:this.cy,r0:e[0],r:e[1],startAngle:-n[0]*i,endAngle:-n[1]*i,clockwise:t.inverse,contain:function(t,e){var t=t-this.cx,e=e-this.cy,t=t*t+e*e,e=this.r,n=this.r0;return e!==n&&t-1e-4<=e*e&&n*n<=1e-4+t}}},Sw.prototype.convertToPixel=function(t,e,n){return Mw(e)===this?this.dataToPoint(n):null},Sw.prototype.convertFromPixel=function(t,e,n){return Mw(e)===this?this.pointToData(n):null},Sw);function Sw(t){this.dimensions=ww,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new gw,this._angleAxis=new _w,this.axisPointerEnabled=!0,this.name=t||"",this._radiusAxis.polar=this._angleAxis.polar=this}function Mw(t){var e=t.seriesModel,t=t.polarModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function Tw(t,e){var n,i=this,r=i.getAngleAxis(),o=i.getRadiusAxis();r.scale.setExtent(1/0,-1/0),o.scale.setExtent(1/0,-1/0),t.eachSeries(function(t){var e;t.coordinateSystem===i&&(O(qv(e=t.getData(),"radius"),function(t){o.scale.unionExtentFromData(e,t)}),O(qv(e,"angle"),function(t){r.scale.unionExtentFromData(e,t)}))}),Vv(r.scale,r.model),Vv(o.scale,o.model),"category"!==r.type||r.onBand||(t=r.getExtent(),n=360/r.scale.count(),r.inverse?t[1]+=n:t[1]-=n,r.setExtent(t[0],t[1]))}function Cw(t,e){var n,i;t.type=e.get("type"),t.scale=Hv(e),t.onBand=e.get("boundaryGap")&&"category"===t.type,t.inverse=e.get("inverse"),"angleAxis"===e.mainType&&(t.inverse=t.inverse!==e.get("clockwise"),n=e.get("startAngle"),i=null!=(i=e.get("endAngle"))?i:n+(t.inverse?-360:360),t.setExtent(n,i)),(e.axis=t).model=e}var Iw={dimensions:ww,create:function(t,s){var l=[];return t.eachComponent("polar",function(t,e){var n,e=new bw(e+""),i=(e.update=Tw,e.getRadiusAxis()),r=e.getAngleAxis(),o=t.findAxisModel("radiusAxis"),a=t.findAxisModel("angleAxis");Cw(i,o),Cw(r,a),i=e,o=s,a=(r=t).get("center"),n=o.getWidth(),o=o.getHeight(),i.cx=f(a[0],n),i.cy=f(a[1],o),a=i.getRadiusAxis(),i=Math.min(n,o)/2,null==(n=r.get("radius"))?n=[0,"100%"]:F(n)||(n=[0,n]),o=[f(n[0],i),f(n[1],i)],a.inverse?a.setExtent(o[1],o[0]):a.setExtent(o[0],o[1]),l.push(e),(t.coordinateSystem=e).model=t}),t.eachSeries(function(t){var e;"polar"===t.get("coordinateSystem")&&(e=t.getReferringComponents("polar",Oo).models[0],t.coordinateSystem=e.coordinateSystem)}),l}},kw=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function Dw(t,e,n){e[1]>e[0]&&(e=e.slice().reverse());var i=t.coordToPoint([e[0],n]),t=t.coordToPoint([e[1],n]);return{x1:i[0],y1:i[1],x2:t[0],y2:t[1]}}function Aw(t){return t.getRadiusAxis().inverse?0:1}function Pw(t){var e=t[0],n=t[t.length-1];e&&n&&Math.abs(Math.abs(e.coord-n.coord)-360)<1e-4&&t.pop()}u(Rw,Lw=sx),Rw.prototype.render=function(e,t){var n,i,r,o,a,s;this.group.removeAll(),e.get("show")&&(n=e.axis,i=n.polar,r=i.getRadiusAxis().getExtent(),o=n.getTicksCoords(),a=n.getMinorTicksCoords(),Pw(s=z(n.getViewLabels(),function(t){t=S(t);var e=n.scale,e="ordinal"===e.type?e.getRawOrdinalNumber(t.tickValue):t.tickValue;return t.coord=n.dataToCoord(e),t})),Pw(o),O(kw,function(t){!e.get([t,"show"])||n.scale.isBlank()&&"axisLine"!==t||Nw[t](this.group,e,i,o,a,r,s)},this))},Rw.type="angleAxis";var Lw,Ow=Rw;function Rw(){var t=null!==Lw&&Lw.apply(this,arguments)||this;return t.type=Rw.type,t.axisPointerClass="PolarAxisPointer",t}var Ew,Nw={axisLine:function(t,e,n,i,r,o){var e=e.getModel(["axisLine","lineStyle"]),a=n.getAngleAxis(),s=Math.PI/180,l=a.getExtent(),u=Aw(n),h=u?0:1,c=360===Math.abs(l[1]-l[0])?"Circle":"Arc",c=0===o[h]?new ic[c]({shape:{cx:n.cx,cy:n.cy,r:o[u],startAngle:-l[0]*s,endAngle:-l[1]*s,clockwise:a.inverse},style:e.getLineStyle(),z2:1,silent:!0}):new Lu({shape:{cx:n.cx,cy:n.cy,r:o[u],r0:o[h]},style:e.getLineStyle(),z2:1,silent:!0});c.style.fill=null,t.add(c)},axisTick:function(t,e,n,i,r,o){var a=e.getModel("axisTick"),s=(a.get("inside")?-1:1)*a.get("length"),l=o[Aw(n)],o=z(i,function(t){return new qu({shape:Dw(n,[l,l+s],t.coord)})});t.add(Wh(o,{style:B(a.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(t,e,n,i,r,o){if(r.length){for(var a=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(a.get("inside")?-1:1)*s.get("length"),u=o[Aw(n)],h=[],c=0;c<r.length;c++)for(var p=0;p<r[c].length;p++)h.push(new qu({shape:Dw(n,[u,u+l],r[c][p].coord)}));t.add(Wh(h,{style:B(s.getModel("lineStyle").getLineStyle(),B(a.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(l,u,h,t,e,c,n){var p=u.getCategories(!0),d=u.getModel("axisLabel"),f=d.get("margin"),g=u.get("triggerEvent");O(n,function(t,e){var n=d,i=t.tickValue,r=c[Aw(h)],o=h.coordToPoint([r+f,t.coord]),a=h.cx,s=h.cy,a=Math.abs(o[0]-a)/r<.3?"center":o[0]>a?"left":"right",r=Math.abs(o[1]-s)/r<.3?"middle":o[1]>s?"top":"bottom",i=(p&&p[i]&&R(s=p[i])&&s.textStyle&&(n=new kc(s.textStyle,d,d.ecModel)),new Ls({silent:N1.isLabelSilent(u),style:sc(n,{x:o[0],y:o[1],fill:n.getTextColor()||u.get(["axisLine","lineStyle","color"]),text:t.formattedLabel,align:a,verticalAlign:r})}));l.add(i),g&&((s=N1.makeAxisEventDataBase(u)).targetType="axisLabel",s.value=t.rawLabel,Us(i).eventData=s)},this)},splitLine:function(t,e,n,i,r,o){for(var a=e.getModel("splitLine").getModel("lineStyle"),s=0,l=(l=a.get("color"))instanceof Array?l:[l],u=[],h=0;h<i.length;h++){var c=s++%l.length;u[c]=u[c]||[],u[c].push(new qu({shape:Dw(n,o,i[h].coord)}))}for(h=0;h<u.length;h++)t.add(Wh(u[h],{style:B({stroke:l[h%l.length]},a.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(t,e,n,i,r,o){if(r.length){for(var a=e.getModel("minorSplitLine").getModel("lineStyle"),s=[],l=0;l<r.length;l++)for(var u=0;u<r[l].length;u++)s.push(new qu({shape:Dw(n,o,r[l][u].coord)}));t.add(Wh(s,{style:a.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(t,e,n,i,r,o){if(i.length){for(var a=e.getModel("splitArea").getModel("areaStyle"),s=0,l=(l=a.get("color"))instanceof Array?l:[l],u=[],h=Math.PI/180,c=-i[0].coord*h,p=Math.min(o[0],o[1]),d=Math.max(o[0],o[1]),f=e.get("clockwise"),g=1,y=i.length;g<=y;g++){var m=(g===y?i[0]:i[g]).coord,v=s++%l.length;u[v]=u[v]||[],u[v].push(new ku({shape:{cx:n.cx,cy:n.cy,r0:p,r:d,startAngle:c,endAngle:-m*h,clockwise:f},silent:!0})),c=-m*h}for(g=0;g<u.length;g++)t.add(Wh(u[g],{style:B({fill:l[g%l.length]},a.getAreaStyle()),silent:!0}))}}},Bw=["axisLine","axisTickLabel","axisName"],zw=["splitLine","splitArea","minorSplitLine"],Fw=(u(Vw,Ew=sx),Vw.prototype.render=function(e,t){var n,i,r,o,a,s,l,u,h;this.group.removeAll(),e.get("show")&&(n=this._axisGroup,i=this._axisGroup=new Wr,this.group.add(i),r=e.axis,h=(o=r.polar).getAngleAxis(),a=r.getTicksCoords(),s=r.getMinorTicksCoords(),l=h.getExtent()[0],u=r.getExtent(),h={position:[o.cx,o.cy],rotation:l/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1},h=new N1(e,h),O(Bw,h.add,h),i.add(h.getGroup()),jh(n,i,e),O(zw,function(t){e.get([t,"show"])&&!r.scale.isBlank()&&Hw[t](this.group,e,o,l,u,a,s)},this))},Vw.type="radiusAxis",Vw);function Vw(){var t=null!==Ew&&Ew.apply(this,arguments)||this;return t.type=Vw.type,t.axisPointerClass="PolarAxisPointer",t}var Hw={splitLine:function(t,e,n,i,r,o){for(var a=e.getModel("splitLine").getModel("lineStyle"),s=a.get("color"),l=0,u=n.getAngleAxis(),h=Math.PI/180,c=u.getExtent(),p=360===Math.abs(c[1]-c[0])?"Circle":"Arc",s=s instanceof Array?s:[s],d=[],f=0;f<o.length;f++){var g=l++%s.length;d[g]=d[g]||[],d[g].push(new ic[p]({shape:{cx:n.cx,cy:n.cy,r:Math.max(o[f].coord,0),startAngle:-c[0]*h,endAngle:-c[1]*h,clockwise:u.inverse}}))}for(f=0;f<d.length;f++)t.add(Wh(d[f],{style:B({stroke:s[f%s.length],fill:null},a.getLineStyle()),silent:!0}))},minorSplitLine:function(t,e,n,i,r,o,a){if(a.length){for(var e=e.getModel("minorSplitLine").getModel("lineStyle"),s=[],l=0;l<a.length;l++)for(var u=0;u<a[l].length;u++)s.push(new su({shape:{cx:n.cx,cy:n.cy,r:a[l][u].coord}}));t.add(Wh(s,{style:B({fill:null},e.getLineStyle()),silent:!0}))}},splitArea:function(t,e,n,i,r,o){if(o.length){for(var a=e.getModel("splitArea").getModel("areaStyle"),s=0,l=(l=a.get("color"))instanceof Array?l:[l],u=[],h=o[0].coord,c=1;c<o.length;c++){var p=s++%l.length;u[p]=u[p]||[],u[p].push(new ku({shape:{cx:n.cx,cy:n.cy,r0:h,r:o[c].coord,startAngle:0,endAngle:2*Math.PI},silent:!0})),h=o[c].coord}for(c=0;c<u.length;c++)t.add(Wh(u[c],{style:B({fill:l[c%l.length]},a.getAreaStyle()),silent:!0}))}}};function Ww(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function Gw(t,e){return e.dim+t.model.componentIndex}function Uw(t,e,n){var i,l,c,A={},P=(i=ut(e.getSeriesByType(t),function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"polar"===t.coordinateSystem.type}),l={},O(i,function(t,e){var n=t.getData(),i=t.coordinateSystem,r=i.getBaseAxis(),i=Gw(i,r),o=r.getExtent(),r="category"===r.type?r.getBandWidth():Math.abs(o[1]-o[0])/n.count(),o=l[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},n=o.stacks,i=(l[i]=o,Ww(t)),a=(n[i]||o.autoWidthCount++,n[i]=n[i]||{width:0,maxWidth:0},f(t.get("barWidth"),r)),r=f(t.get("barMaxWidth"),r),s=t.get("barGap"),t=t.get("barCategoryGap");a&&!n[i].width&&(a=Math.min(o.remainedWidth,a),n[i].width=a,o.remainedWidth-=a),r&&(n[i].maxWidth=r),null!=s&&(o.gap=s),null!=t&&(o.categoryGap=t)}),c={},O(l,function(t,n){c[n]={};var i,e=t.stacks,r=t.bandWidth,r=f(t.categoryGap,r),o=f(t.gap,1),a=t.remainedWidth,s=t.autoWidthCount,l=(a-r)/(s+(s-1)*o),l=Math.max(l,0),u=(O(e,function(t,e){var n=t.maxWidth;n&&n<l&&(n=Math.min(n,a),t.width&&(n=Math.min(n,t.width)),a-=n,t.width=n,s--)}),l=(a-r)/(s+(s-1)*o),l=Math.max(l,0),0),h=(O(e,function(t,e){t.width||(t.width=l),u+=(i=t).width*(1+o)}),i&&(u-=i.width*o),-u/2);O(e,function(t,e){c[n][e]=c[n][e]||{offset:h,width:t.width},h+=t.width*(1+o)})}),c);e.eachSeriesByType(t,function(t){if("polar"===t.coordinateSystem.type)for(var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=Gw(n,i),o=Ww(t),r=P[r][o],a=r.offset,s=r.width,l=n.getOtherAxis(i),u=t.coordinateSystem.cx,h=t.coordinateSystem.cy,c=t.get("barMinHeight")||0,p=t.get("barMinAngle")||0,d=(A[o]=A[o]||[],e.mapDimension(l.dim)),f=e.mapDimension(i.dim),g=Xm(e,d),y="radius"!==i.dim||!t.get("roundCap",!0),r=l.model.get("startValue"),m=l.dataToCoord(r||0),v=0,_=e.count();v<_;v++){var x,w,b=e.get(d,v),S=e.get(f,v),M=0<=b?"p":"n",T=m,C=(g&&(A[o][S]||(A[o][S]={p:m,n:m}),T=A[o][S][M]),void 0),I=void 0,k=void 0,D=void 0;"radius"===l.dim?(w=l.dataToCoord(b)-m,x=i.dataToCoord(S),I=(C=T)+(w=Math.abs(w)<c?(w<0?-1:1)*c:w),D=(k=x-a)-s,g&&(A[o][S][M]=I)):(w=l.dataToCoord(b,y)-m,I=(C=i.dataToCoord(S)+a)+s,D=(k=T)+(w=Math.abs(w)<p?(w<0?-1:1)*p:w),g&&(A[o][S][M]=D)),e.setItemLayout(v,{cx:u,cy:h,r0:C,r:I,startAngle:-k*Math.PI/180,endAngle:-D*Math.PI/180,clockwise:D<=k})}})}var Xw,qw={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},Yw={splitNumber:5},Zw=(u(jw,Xw=yg),jw.type="polar",jw);function jw(){var t=null!==Xw&&Xw.apply(this,arguments)||this;return t.type=jw.type,t}$v(function(t){$v(Jx),sx.registerAxisPointerClass("PolarAxisPointer",Mx),t.registerCoordinateSystem("polar",Iw),t.registerComponentModel(ew),t.registerComponentView(Zw),rw(t,"angle",uw,qw),rw(t,"radius",pw,Yw),t.registerComponentView(Ow),t.registerComponentView(Fw),t.registerLayout(dt(Uw,"bar"))}),$v(m1);var Kw={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},$w=(Qw.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!gt(e)&&this._condVal.test(t+"")},Qw);function Qw(t){null==(this._condVal=V(t)?new RegExp(t):wt(t)?t:null)&&g("")}tb.prototype.evaluate=function(){return this.value};var Jw=tb;function tb(){}nb.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var eb=nb;function nb(){}rb.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var ib=rb;function rb(){}ab.prototype.evaluate=function(){return!this.child.evaluate()};var ob=ab;function ab(){}lb.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var sb=lb;function lb(){}function ub(t,e){if(!0===t||!1===t)return(n=new Jw).value=t,n;var n;if(cb(t)||g(""),t.and)return hb("and",t,e);if(t.or)return hb("or",t,e);if(t.not)return n=e,cb(o=(o=t).not)||g(""),(l=new ob).child=ub(o,n),l.child||g(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=ct(i),l=i.parser,u=l?Mf(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=Vt(Kw,p)?Kw[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Af("eq"===t,e):Vt(Tf,t)?new Cf(t,e):null}(c,p)||"reg"===c&&new $w(p))||g(""),a.push(c))}return a.length||g(""),(l=new sb).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function hb(t,e,n){e=e[t],F(e)||g(""),e.length||g(""),t=new("and"===t?eb:ib);return t.children=z(e,function(t){return ub(t,n)}),t.children.length||g(""),t}function cb(t){return R(t)&&!st(t)}db.prototype.evaluate=function(){return this._cond.evaluate()};var pb=db;function db(t,e){this._cond=ub(t,e)}var fb={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:N({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(Vt(t,"dimension")||g(""),i.getDimensionInfo(e));return t||g(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new pb(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},gb={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=mo(t),s=(t.length||g(""),[]),t=(O(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&g(""),"asc"!==n&&"desc"!==n&&g(""),t&&"min"!==t&&"max"!==t&&g(""),"asc"!==n&&"desc"!==n&&g(""),a.getDimensionInfo(e)),r=(e||g(""),i?Mf(i):null);i&&!r&&g(""),s.push({dimIdx:e.index,parser:r,comparator:new kf(n,t)})}),a.sourceFormat),e=(t!==Rp&&t!==Ep&&g(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};$v(function(t){t.registerTransform(fb),t.registerTransform(gb)}),t.Axis=Tc,t.ChartView=wg,t.ComponentModel=y,t.ComponentView=yg,t.List=Fm,t.Model=kc,t.PRIORITY=dy,t.SeriesModel=lg,t.color=xi,t.connect=function(e){var t;return F(e)&&(t=e,e=null,O(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+q0++,O(t,function(t){t.group=e})),U0[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.6.1"},t.disConnect=py,t.disconnect=Z0,t.dispose=function(t){V(t)?t=G0[t]:t instanceof D0||(t=j0(t)),t instanceof D0&&!t.isDisposed()&&t.dispose()},t.env=b,t.extendChartView=function(t){return t=wg.extend(t),wg.registerClass(t),t},t.extendComponentModel=function(t){return t=y.extend(t),y.registerClass(t),t},t.extendComponentView=function(t){return t=yg.extend(t),yg.registerClass(t),t},t.extendSeriesModel=function(t){return t=lg.extend(t),lg.registerClass(t),t},t.format=Sc,t.getCoordinateSystemDimensions=function(t){if(t=ud.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=j0,t.getInstanceById=function(t){return G0[t]},t.getMap=function(t){var e=Qy.getMap;return e&&e(t)},t.graphic=Uc,t.helper=fy,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=j0(t);if(r)return r}return(r=new D0(t,e,n)).id="ec_"+X0++,G0[r.id]=r,i&&Eo(t,Y0,r.id),T0(r),$y.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=Uy,t.matrix=He,t.number=Zo,t.parseGeoJSON=f_,t.parseGeoJson=f_,t.registerAction=nm,t.registerCoordinateSystem=im,t.registerLayout=rm,t.registerLoading=lm,t.registerLocale=zc,t.registerMap=um,t.registerPostInit=J0,t.registerPostUpdate=tm,t.registerPreprocessor=$0,t.registerProcessor=Q0,t.registerTheme=K0,t.registerTransform=hm,t.registerUpdateLifecycle=em,t.registerVisual=om,t.setCanvasCreator=function(t){A({createCanvas:t})},t.setPlatformAPI=A,t.throttle=Pg,t.time=Xh,t.use=$v,t.util=_c,t.vector=ae,t.version="5.6.0",t.zrUtil=Gt,t.zrender=$r});