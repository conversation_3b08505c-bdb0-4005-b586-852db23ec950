.weui-actionsheet__action {
  padding-bottom: 0;
}

.weui-actionsheet__cell_cancel {
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.weui-actionsheet__title:focus {
  outline: none;
}

.weui-transition.weui-mask {
  transition: opacity 0.3s, visibility 0.3s;
  opacity: 0;
  visibility: hidden;
}

.weui-transition.weui-half-screen-dialog {
  transition: transform 0.3s;
  transform: translateY(100%);
}

.weui-transition_show.weui-mask {
  opacity: 1;
  visibility: visible;
}

.weui-transition_show.weui-half-screen-dialog {
  transform: translateY(0);
}

.weui-mask {
  background: rgba(0, 0, 0, 0.6);
}

.weui-mask,
.weui-mask_transparent {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.weui-actionsheet {
  position: fixed;
  left: 0;
  bottom: 0;
  transform: translateY(100%);
  backface-visibility: hidden;
  z-index: 5000;
  width: 100%;
  transition: all 0.3s;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  overflow: hidden;
  background: #fff;
}

.weui-actionsheet__title {
  z-index: 100;
  position: relative;
  height: 56px;
  padding: 0 24px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  text-align: center;
  font-size: 12px;
  color: var(--weui-FG-1);
  line-height: 1.4;
  background: var(--weui-BG-2);
}

.weui-actionsheet__title .weui-actionsheet__title-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.weui-actionsheet__menu {
  color: var(--weui-FG-0);
  background-color: var(--weui-BG-2);
}

.weui-actionsheet__action {
  margin-top: 8px;
  background-color: var(--weui-BG-2);
  /* padding-bottom: constant(safe-area-inset-bottom); */
  /* padding-bottom: env(safe-area-inset-bottom); */
  padding-bottom: 60rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

@keyframes weuiSlideUp {
  from {
    transform: translate3d(0, 100%, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

.weui-animate_slide-up,
.weui-animate-slide-up {
  /* animation: weuiSlideUp ease 0.3s forwards; */
  transform: translateY(-50%) !important;
}

@keyframes weuiSlideDown {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, 100%, 0);
  }
}

.weui-animate_slide-down,
.weui-animate-slide-down {
  animation: weuiSlideDown ease 0.3s forwards;
}

@keyframes weuiFadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.weui-animate_fade-in,
.weui-animate-fade-in {
  animation: weuiFadeIn ease 0.3s forwards;
}

@keyframes weuiFadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.weui-animate_fade-out,
.weui-animate-fade-out {
  animation: weuiFadeOut ease 0.3s forwards;
}

.title-box {
  position: relative;
  z-index: 999;
  transform: translateY(0);

}