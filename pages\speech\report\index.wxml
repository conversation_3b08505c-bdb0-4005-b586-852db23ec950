<wxs module="utils">
  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };
  function getArrNum(num) {
    var str = num.toString();
    var result = [];
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');

    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点，仅保留整数部分
      if (lastNonZeroIndex === dotIndex) {
        result[0] = str.substring(0, dotIndex);
      } else {
        result[0] = str.substring(0, dotIndex); // 整数部分
        result[1] = str.substring(dotIndex + 1, lastNonZeroIndex + 1); // 小数部分
      }
    } else {
      // 如果没有小数点，直接返回原字符串作为整数部分
      result[0] = str;
    }

    return result;
  }
  function formatSeconds(seconds) {
    // 将输入转换为整数
    var totalSeconds = parseInt(seconds, 10);

    // 计算分钟数和剩余秒数
    var minutes = Math.floor(totalSeconds / 60);
    var remainingSeconds = totalSeconds % 60;

    // 确保分钟和秒数都是两位数，不足两位补零
    var formattedMinutes = (minutes < 10 ? '0' : '') + minutes;
    var formattedSeconds = (remainingSeconds < 10 ? '0' : '') + remainingSeconds;

    // 返回格式化后的字符串
    return formattedMinutes + ':' + formattedSeconds;
  }
  module.exports = {
    getNum: getNum,
    formatSeconds: formatSeconds,
    getArrNum: getArrNum
  };
</wxs>
<view class="result">
  <navigation-bar back="{{ true }}" isResult="{{ true }}" isSticky="{{ true }}" showWhite="{{show_white}}">
    <view slot="center" class="tab-title {{show_white?'black-color':''}}">
      <view class="uavatar" wx:if="{{ is_self_record == 0}}">
        <image class="img" src="{{questionInfo.user_info.portrait}}" mode="" />
      </view>
      <view class="text-ellipsis-1">{{resultTitle}}</view>
    </view>
  </navigation-bar>
  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/question_result_top_bg.png" class="banner" mode="widthFix"></image>
  <block wx:if="{{ isComplete == 1 }}">
    <block wx:if="{{ question_type === 'single' }}">
      <view class="report-area">
        <view class="top-area" wx:if="{{ is_self_record === 1}}">
          <image wx:if="{{ type === 'drill'}}" class="report-multiple-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_mutiple_bg_drill.png" mode="" />
          <image wx:else class="report-multiple-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_mutiple_bg.png" mode="" />
          <view class="top-title">{{questionInfo.title}}</view>
        </view>
        <view class="multiple-report-area">
          <view class="question-card">
            <view class="top-area-question">
              <view class="left">
                <view class="left-item">作答时长：{{ utils.formatSeconds(questionData[0].answer_time) }}</view>
                <view class="left-item">交卷时间：{{ questionData[0].answer.created_time }}</view>
              </view>
              <view class="right">
                <view class="title">
                  <text class="num">{{utils.getArrNum(questionData[0].answer_score)[0]}}</text>
                  <text class="small-num" wx:if="{{utils.getArrNum(questionData[0].answer_score)[1]}}">.{{utils.getArrNum(questionData[0].answer_score)[1]}}</text>
                  <text style="padding-bottom: 8rpx;margin-left: 8rpx;">分</text>
                </view>
                <view class="content" style="justify-content: center;">满分100分</view>
              </view>
            </view>
            <view class="multiple-line"></view>
            <view class="score-box">
              <view class="score-center-box">
                <view class="score-item">
                  <view class="score-item-t">最高分</view>
                  <view class="score-item-b">{{utils.getNum(questionData[0].max_score)}}</view>
                </view>
                <view class="score-item">
                  <view class="score-item-t">平均分</view>
                  <view class="score-item-b">{{ utils.getNum(questionData[0].avg_score) }}</view>
                </view>
                <view class="score-item">
                  <view class="score-item-t">击败同学</view>
                  <view class="score-item-b score-item-h">{{questionData[0].percentage}}</view>
                </view>
              </view>
            </view>
            <view class="multiple-line"></view>
            <view class="echarts-area">
              <echarts scoreMap="{{ questionData[0].score_map }}" wx:if="{{questionData[0].score_map.length>0}}"></echarts>
              <!-- <view class="tuli">
                <view class="red-cube"></view>
                <view class="cube-text">个人得分</view>
                <view class="gray-cube"></view>
                <view class="cube-text">平均得分</view>
              </view> -->
            </view>
          </view>
          <report-card wx:if="{{ type !== 'drill' && questionData[0].question_content_delta}}" isCollapse="{{true}}" title="题目">
            <rich-text-box strText="{{questionData[0].question_content_delta}}"></rich-text-box>
          </report-card>
          <report-card title="{{is_self_record ==1?'我的作答':'TA的作答'}}" isCollapse="{{questionData[0].answer.show_status == 1?true:false}}" isHaveVideo="{{ questionData[0].answer.show_status == 1?true:false }}">
            <block wx:if="{{questionData[0].answer.show_status != 1}}">
              <view class="gray-box" style="background-image:url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_other_bg.png)">
                <view class="gray-box-text">{{questionData[0].answer.show_status == '-1'?'仅线下课学员可查看该内容':'参与演练后可查看同学的作答内容'}}</view>
                <view class="gray-box-btn" catch:tap="goOther" data-type="{{questionData[0].answer.show_status}}">{{questionData[0].answer.show_status == '-1'?'去了解':'去演练'}}</view>
              </view>
            </block>
            <block wx:else>
              <my-video id="video-{{0}}-answer" videoId="video-{{0}}-answer" bind:videoOnPlay="onAudioPlay" wx:if="{{ questionData[0].answer.video }}" videoUrl="{{ questionData[0].answer.video }}"></my-video>
              <my-audio isMultiple="{{ true }}" id="audio-{{0}}-answer" audioId="audio-{{0}}-answer" isPlaying="{{mediaStatus['audio-'+0+'-answer'].isPlaying}}" bind:playAudio="onAudioPlay" bind:pauseAudio="onAudioPause" wx:else audioUrl="{{ questionData[0].answer.audio }}"></my-audio>
              <rich-text class="my-answer-text" nodes="{{questionData[0].answer.text}}" />
            </block>
          </report-card>
          <report-card title="AI点评" wx:if="{{ questionData[0].correct.text}}">
            <view class="ai-audio-player" wx:if="{{questionData[0].correct.audio}}">
              <view class="progress-mask" style="width: {{(aiAudioInfo.currentTime/aiAudioInfo.duration)*100}}%;"></view>
              <view class="content" catch:tap="playAiAudio">
                <image class="teacher" src="{{questionData[0].correct.avatar}}" mode="" />
                <image class="progress-content" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_ai_bolang.png" mode="" />
                <image class="play-button" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/{{aiAudioInfo.isPlaying?'video_play':'video_pause'}}.png" mode="" />
              </view>
            </view>
            <rich-text wx:if="{{ questionData[0].correct.original_correct.info }}" class="report-card-text" nodes="{{questionData[0].correct.original_correct.info}}" />
            <view wx:else class="point-area">
              <view class="point-area-item" wx:if="{{ questionData[0].correct.original_correct.adv }}">
                <view class="title-area">
                  <view>优点</view>
                  <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                </view>
                <rich-text style="white-space: pre-wrap;" class="content-area" nodes="{{questionData[0].correct.original_correct.adv}}" />
              </view>
              <view class="point-area-item" wx:if="{{ questionData[0].correct.original_correct.problem && questionData[0].correct.original_correct.problem.length }}">
                <view class="title-area">
                  <view>优化建议</view>
                  <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                </view>
                <view class="suggestion-area-item" wx:for="{{questionData[0].correct.original_correct.problem}}" wx:key="index">
                  <text class="title">{{index + 1}}.</text>
                  <text>{{item}}</text>
                </view>
                <view class="suggestion-area-item" wx:if="{{questionData[0].correct.original_correct.incentive}}">
                  <text>{{questionData[0].correct.original_correct.incentive}}</text>
                </view>
              </view>
            </view>
          </report-card>
          <view class="advertisement" wx:if="{{adv_info && adv_info.img}}" bind:tap="goAdvDetail" data-item="{{adv_info}}">
            <image class="advertisement-img" src="{{adv_info.img}}" mode="widthFix" />
          </view>
          <report-card title="思路点拨" wx:if="{{ questionData[0].question_analyze}}" isCollapse="{{true}}">
            <rich-text class="report-card-text" nodes="{{questionData[0].question_analyze}}" />
          </report-card>
          <report-card title="思维导图" wx:if="{{ questionData[0].mind_mapping_img}}">
            <view class="mind-mapping-container" catch:tap="checkImg" data-url="{{questionData[0].mind_mapping_img}}">
              <image class="mind-mapping-img" src="{{questionData[0].mind_mapping_img}}" mode="widthFix" />
            </view>
          </report-card>
          <report-card title="参考答案" wx:if="{{questionData[0].reference_answer.content_text}}">
            <!-- <rich-text class="report-card-text" nodes="{{questionData[0].reference_answer}}" /> -->
            <rich-text-box strText="{{questionData[0].reference_answer.dalta}}"></rich-text-box>
          </report-card>
          <report-card title="讲解视频" wx:if="{{ questionData[0].video_data && questionData[0].video_data.url }}">
            <my-video id="video-{{0}}-jiexi" videoId="video-{{0}}-jiexi" bind:videoOnPlay="onAudioPlay" videoUrl="{{ questionData[0].video_data.url }}" poster="{{ questionData[0].video_data.cover}}"></my-video>
          </report-card>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="report-area">
        <view class="top-area {{ show_white?'bg-white':''}}" wx:if="{{ is_self_record === 1}}">
          <image wx:if="{{ type === 'drill'}}" class="report-multiple-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_mutiple_bg_drill.png" mode="" />
          <image wx:else class="report-multiple-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_mutiple_bg.png" mode="" />
          <view class="top-title">{{questionInfo.title}}</view>
        </view>
        <view class="tab-area-all {{ show_white?'bg-white':''}}" style="{{stickyHeightText}}">
          <scroll-view scroll-x="true" class="tab-area" show-scrollbar="{{false}}" enhanced="{{true}}" scroll-into-view="{{scrollIntoViewId}}">
            <view id="tab-{{item.key}}" class="tab-area-list {{ activeTabIndex === item.key?'active':''}}" wx:for="{{ tabList }}" wx:key="index" catch:tap="changeQuestion" data-key="{{item.key}}">
              {{item.name}}
            </view>
          </scroll-view>
        </view>
        <view class="multiple-report-area">
          <view hidden="{{ activeTabIndex !== 0}}">
            <view class="content-box">
              <!-- <image class="good-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_good_paper.png" mode="" /> -->
              <view class="middle-area">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/left_score.png" mode="widthFix" class="score_icon"></image>
                <view class="num-area">
                  <view class="number-box">
                    <view class="number-all">{{ utils.getArrNum(questionInfo.score)[0] }}</view>
                    <view class="small-number" wx:if="{{utils.getArrNum(questionInfo.score)[1]}}">.{{ utils.getArrNum(questionInfo.score)[1] }}</view>
                    <view class="number-box-text">分</view>
                  </view>
                  <text class="full-score">面试总分{{ utils.getNum(questionInfo.total_score) }}</text>
                </view>
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/right_score.png" mode="widthFix" class="score_icon"></image>
              </view>
              <block wx:if="{{is_self_record == 0}}">
                <view class="score-center-box no-line">
                  <view class="score-item">
                    <view class="class-box-item">
                      <image class="image-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_report_icon1.png" mode="" />
                      <view class="class-text">总用时</view>
                      <view class="black-text">{{utils.formatSeconds(questionInfo.record_answer_time)}}</view>
                    </view>
                  </view>
                  <view class="score-item">
                    <view class="class-box-item">
                      <image class="image-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_report_icon4.png" mode="" />
                      <view class="class-text">总排名</view>
                      <view class="rank-text">
                        <text class="red-text">{{questionInfo.drill_rank}}</text>/{{questionInfo.drill_participants}}
                      </view>
                    </view>
                  </view>
                </view>
                <view class="multiple-line"></view>
                <!-- <view class="data-box">
                  <view class="data-box-list-area">
                    <view class="list-item">
                      <image class="img-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_report_icon1.png" mode="" />
                      <view class="text">总用时</view>
                      <view class="content-text">{{utils.formatSeconds(questionInfo.record_answer_time)}}</view>
                    </view>
                    <view class="list-item mr0">
                      <image class="img-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_report_icon2.png" mode="" />
                      <view class="text">最高分</view>
                      <view class="content-text">{{ utils.getNum(questionInfo.max_score) }}</view>
                    </view>
                  </view>
                  <view class="data-box-list-area">
                    <view class="list-item">
                      <image class="img-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_report_icon3.png" mode="" />
                      <view class="text">平均分</view>
                      <view class="content-text">{{ utils.getNum(questionInfo.avg_score) }}</view>
                    </view>
                    <view class="list-item mr0">
                      <image class="img-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/speech/speech_report_icon4.png" mode="" />
                      <view class="text">总排名</view>
                      <view class="rank-text">
                        <text class="red-text">{{questionInfo.drill_rank}}</text>/{{questionInfo.drill_participants}}
                      </view>
                    </view>
                  </view>
                </view> -->
              </block>
              <block wx:else>
                <view class="multiple-line"></view>
                <view class="score-box">
                  <view class="score-center-box">
                    <view class="score-item">
                      <view class="score-item-t">总用时</view>
                      <view class="score-item-b">{{ utils.formatSeconds(questionInfo.record_answer_time) }}</view>
                    </view>
                    <view class="score-item">
                      <view class="score-item-t">最高分</view>
                      <view class="score-item-b">{{ utils.getNum(questionInfo.max_score) }}</view>
                    </view>
                    <view class="score-item">
                      <view class="score-item-t">平均分</view>
                      <view class="score-item-b">{{ utils.getNum(questionInfo.avg_score) }}</view>
                    </view>
                    <view class="score-item">
                      <view class="score-item-t">总排名</view>
                      <view class="rank-text"><text class="red-text">{{questionInfo.drill_rank}}</text>/{{questionInfo.drill_participants}}</view>
                    </view>
                  </view>
                </view>
                <view class="multiple-line"></view>
              </block>
              <view class="rank-table-box">
                <view class="rank-list header-list">
                  <view class="rank-list-item">题号</view>
                  <view class="rank-list-item">单题用时</view>
                  <view class="rank-list-item">得分</view>
                  <view class="rank-list-item" wx:if="{{is_self_record == 0}}">击败同学</view>
                </view>
                <view class="rank-list {{ index == questionData.length - 1?'mb0':''}}" wx:for="{{questionData}}" wx:key="index" catch:tap="changeQuestion" data-key="{{index + 1}}">
                  <view class="rank-list-item">第{{index + 1}}题</view>
                  <view class="rank-list-item">{{utils.formatSeconds(item.answer_time) }}</view>
                  <view class="rank-list-item">{{utils.getNum(item.answer_score)}}分</view>
                  <view class="rank-list-item" wx:if="{{is_self_record == 0}}">{{item.percentage}}</view>
                </view>
              </view>
              <view class="text-box" wx:if="{{is_self_record == 0}}">
                <view class="exam-name mb24">
                  <view class="label">演练试卷：</view>
                  <text class="text-ellipsis-2">{{ questionInfo.qv_title }}</text>
                </view>
                <view class="exam-name mb0">
                  <view class="label text-ellipsis-2">作答时间：</view>
                  <text class="text-ellipsis-2">{{ questionInfo.created_time }}</text>
                </view>
              </view>
            </view>
            <block wx:if="{{is_self_record == 1}}">
              <report-card title="AI点评">
                <rich-text wx:if="{{ questionInfo.original_correct.info }}" class="report-card-text" nodes="{{questionInfo.original_correct.info}}" />
                <view wx:else class="point-area">
                  <view class="point-area-item" wx:if="{{ questionInfo.original_correct.adv }}">
                    <view class="title-area">
                      <view>优点</view>
                      <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                    </view>
                    <rich-text style="white-space: pre-wrap;" class="content-area" nodes="{{questionInfo.original_correct.adv}}" />
                  </view>
                  <view class="point-area-item" wx:if="{{questionInfo.original_correct.problem}}">
                    <view class="title-area">
                      <view>优化建议</view>
                      <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                    </view>
                    <rich-text style="white-space: pre-wrap;" class="content-area" nodes="{{questionInfo.original_correct.problem}}" />
                    <view class="content-area">{{questionInfo.original_correct.incentive}}</view>
                  </view>
                </view>
              </report-card>
              <report-card title="备考建议" wx:if="{{ questionInfo.original_preparation.length}}">
                <view class="suggestion-area">
                  <view class="suggestion-area-item" wx:for="{{questionInfo.original_preparation}}" wx:key="index">
                    <text class="title">{{index + 1}}.</text>
                    <text>{{item}}</text>
                  </view>
                </view>
              </report-card>
              <report-card wx:if="{{questionInfo.rank_list && questionInfo.rank_list.list && questionInfo.rank_list.list.length}}" title="演练排行" isHaveRight="{{true}}" bind:goRank="goRank">
                <view class="rank-area-box">
                  <view class="title-name">
                    <view class="text mt36">排名</view>
                    <view class="text">同学</view>
                    <view class="right-text text">得分</view>
                  </view>
                  <view class="rank-list">
                    <view class="rank-list-item {{(index == questionInfo.rank_list.list.length - 1 || questionInfo.rank_list.list[index + 1]&&questionInfo.rank_list.list[index + 1].is_my)?'no-border':''}}" wx:for="{{questionInfo.rank_list.list}}" wx:key="index" catch:tap="goReport" data-data="{{item}}">
                      <view wx:if="{{item.is_my}}" class="is-my-bg"></view>
                      <view class="left">
                        <view class="rank-num {{item.is_my?'cred':''}}">
                          <image class="rank-img" wx:if="{{index < 3}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_rank_{{item.rank}}.png" mode="widthFix" />
                          <text wx:else>{{item.rank}}</text>
                        </view>
                        <image class="user-avatar" src="{{item.portrait}}" mode="" />
                        <view class="name-text text-ellipsis-1 {{item.is_my?'font-bold':''}}">{{item.nickname}}</view>
                        <image wx:if="{{item.is_my}}" class="img-my" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_rank_me.png" mode="" />
                      </view>
                      <view class="right">
                        <view class="score-num">{{utils.getNum(item.score)}}</view>
                        <view class="right-text">分</view>
                        <image wx:if="{{!item.is_my}}" class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
                        <view wx:else class="arrow-box"></view>
                      </view>
                    </view>
                    <view class="rank-list-item no-border" wx:if="{{questionInfo.rank_list.my.rank > 5}}">
                      <view class="is-my-bg"></view>
                      <view class="left">
                        <view class="rank-num cred">
                          <text>{{questionInfo.rank_list.my.rank}}</text>
                        </view>
                        <image class="user-avatar" src="{{questionInfo.rank_list.my.portrait}}" mode="" />
                        <view class="name-text text-ellipsis-1 font-bold">{{questionInfo.rank_list.my.nickname}}</view>
                        <image class="img-my" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_rank_me.png" mode="" />
                      </view>
                      <view class="right">
                        <view class="score-num">{{utils.getNum(questionInfo.rank_list.my.score)}}</view>
                        <view class="right-text">分</view>
                        <view class="arrow-box"></view>
                      </view>
                    </view>
                  </view>
                </view>
              </report-card>
            </block>
          </view>
          <view hidden="{{ activeTabIndex === 0}}">
            <view wx:for="{{questionData}}" wx:for-index="zindex">
              <view hidden="{{ zindex !== activeTabIndex - 1}}">
                <view class="question-card">
                  <view class="top-area-question">
                    <view class="left">
                      <view class="left-item">作答时长：{{ utils.formatSeconds(item.answer_time) }}</view>
                      <view class="left-item">交卷时间：{{ item.answer.created_time }}</view>
                    </view>
                    <view class="right">
                      <view class="title">
                        <text class="num">{{utils.getArrNum(item.answer_score)[0]}}</text>
                        <text class="small-num" wx:if="{{utils.getArrNum(item.answer_score)[1]}}">.{{utils.getArrNum(item.answer_score)[1]}}</text>
                        <text style="padding-bottom: 8rpx;margin-left: 8rpx;">分</text>
                      </view>
                      <view class="content" style="justify-content: center;">满分100分</view>
                    </view>
                  </view>
                  <view class="multiple-line"></view>
                  <view class="score-box">
                    <view class="score-center-box">
                      <view class="score-item">
                        <view class="score-item-t">最高分</view>
                        <view class="score-item-b">{{utils.getNum(item.max_score)}}</view>
                      </view>
                      <view class="score-item">
                        <view class="score-item-t">平均分</view>
                        <view class="score-item-b">{{ utils.getNum(item.avg_score) }}</view>
                      </view>
                      <view class="score-item">
                        <view class="score-item-t">击败同学</view>
                        <view class="score-item-b score-item-h">{{item.percentage}}</view>
                      </view>
                    </view>
                  </view>
                  <view class="multiple-line"></view>
                  <view class="echarts-area">
                    <echarts scoreMap="{{ item.score_map }}" wx:if="{{item.score_map.length>0 && zindex === activeTabIndex - 1}}"></echarts>
                    <!-- <view class="tuli">
                      <view class="red-cube"></view>
                      <view class="cube-text">个人得分</view>
                      <view class="gray-cube"></view>
                      <view class="cube-text">平均得分</view>
                    </view> -->
                  </view>
                </view>
                <report-card wx:if="{{ type !== 'drill' && item.question_content_delta }}" isCollapse="{{true}}" title="题目" activeIndex="{{activeTabIndex}}">
                  <rich-text-box strText="{{item.question_content_delta}}"></rich-text-box>
                </report-card>
                <report-card title="{{is_self_record ==1?'我的作答':'TA的作答'}}" activeIndex="{{activeTabIndex}}" isCollapse="{{item.answer.show_status ==1?true:false}}" isHaveVideo="{{ item.answer.show_status ==1?true:false }}">
                  <block wx:if="{{item.answer.show_status != 1}}">
                    <view class="gray-box" style="background-image:url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_other_bg.png)">
                      <view class="gray-box-text">{{item.answer.show_status == '-1'?'仅线下课学员可查看该内容':'参与演练后可查看同学的作答内容'}}</view>
                      <view class="gray-box-btn" catch:tap="goOther" data-type="{{item.answer.show_status}}">{{item.answer.show_status == '-1'?'去了解':'去演练'}}</view>
                    </view>
                  </block>
                  <block wx:else>
                    <my-video bind:videoOnPlay="onAudioPlay" id="video-{{zindex}}-answer" videoId="video-{{zindex}}-answer" wx:if="{{ item.answer.video }}" videoUrl="{{ item.answer.video }}"></my-video>
                    <my-audio isMultiple="{{ true }}" id="audio-{{zindex}}-answer" audioId="audio-{{zindex}}-answer" isPlaying="{{mediaStatus['audio-'+zindex+'-answer'].isPlaying}}" wx:else audioUrl="{{ item.answer.audio }}" bind:playAudio="onAudioPlay" bind:pauseAudio="onAudioPause"></my-audio>
                    <rich-text class="my-answer-text" nodes="{{item.answer.text}}" />
                  </block>
                </report-card>
                <report-card title="AI点评" wx:if="{{item.correct.text}}">
                  <rich-text wx:if="{{ item.correct.original_correct.info }}" class="report-card-text" nodes="{{item.correct.original_correct.info}}" />
                  <view wx:else class="point-area">
                    <view class="point-area-item" wx:if="{{ item.correct.original_correct.adv }}">
                      <view class="title-area">
                        <view>优点</view>
                        <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                      </view>
                      <rich-text class="content-area" style="white-space: pre-wrap;" nodes="{{item.correct.original_correct.adv}}" />
                    </view>
                    <view class="point-area-item" wx:if="{{item.correct.original_correct.problem}}">
                      <view class="title-area">
                        <view>优化建议</view>
                        <image class="report-star" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_star.png" mode="" />
                      </view>
                      <view class="suggestion-area-item" wx:for-index="index1" wx:for-item="item1" wx:for="{{item.correct.original_correct.problem}}" wx:key="index">
                        <text class="title">{{index1 + 1}}.</text>
                        <text>{{item1}}</text>
                      </view>
                      <view class="suggestion-area-item" wx:if="{{item.correct.original_correct.incentive}}">
                        <text>{{item.correct.original_correct.incentive}}</text>
                      </view>
                    </view>
                  </view>
                </report-card>
                <view class="advertisement" wx:if="{{adv_info && adv_info.img}}" bind:tap="goAdvDetail" data-item="{{adv_info}}">
                  <image class="advertisement-img" src="{{adv_info.img}}" mode="widthFix" />
                </view>
                <report-card title="思路点拨" wx:if="{{ item.question_analyze}}" isCollapse="{{true}}" activeIndex="{{activeTabIndex}}">
                  <rich-text class="report-card-text" nodes="{{item.question_analyze}}" />
                </report-card>
                <report-card title="思维导图" wx:if="{{ item.mind_mapping_img}}">
                  <view class="mind-mapping-container" catch:tap="checkImg" data-url="{{item.mind_mapping_img}}">
                    <image class="mind-mapping-img" src="{{item.mind_mapping_img}}" mode="widthFix" />
                  </view>
                </report-card>
                <report-card title=" 参考答案" wx:if="{{item.reference_answer.content_text}}">
                  <!-- <rich-text class="report-card-text" nodes="{{item.reference_answer}}" /> -->
                  <rich-text-box strText="{{item.reference_answer.dalta}}"></rich-text-box>
                </report-card>
                <report-card title="讲解视频" wx:if="{{ item.video_data && item.video_data.url }}">
                  <my-video bind:videoOnPlay="onAudioPlay" id="video-{{zindex}}-jiexi" videoId="video-{{zindex}}-jiexi" videoUrl="{{ item.video_data.url }}" poster="{{ item.video_data.cover}}"></my-video>
                </report-card>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
  </block>
  <block wx:else>
    <view class="no-data-box">
      <image class="no-data-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_no_data_bg.png" mode="widthFix" />
      <block wx:if="{{isComplete == 7007 || isComplete == 0 || isComplete == 7020}}">
        <image class="gif-bg-img" src="{{record_data_map[isComplete].img}}" mode="" />
        <view class="blue-text">{{record_data_map[isComplete].title}}</view>
        <view class="gray-text">{{waitMessage || record_data_map[isComplete].desc}}</view>
        <view wx:if="{{isComplete == 7020}}" class="error-btn" bind:tap="goBack">返回</view>
      </block>
      <!-- <block wx:elif="{{isComplete == 0}}">
        <image class="gif-bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/gif_bg_img.gif" mode="" />
        <view class="blue-text">正在解析你的作答</view>
        <view class="gray-text">{{questionInfo.message}}</view>
      </block>
      <block wx:elif="{{isComplete == 7020 }}">
        <image class="gif-bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/gif_bg_img.gif" mode="" />
        <view class="blue-text">等待生成作答报告</view>
        <view class="gray-text">{{waitMessage}}</view>
        <view class="error-btn" bind:tap="goBack">返回</view>
      </block> -->
      <block wx:else>
        <image class="gif-bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_error_icon.png" mode="" />
        <view class="orange-text">很抱歉，报告生成出了点小状况</view>
        <view class="error-btn" bind:tap="goBack">返回</view>
      </block>
    </view>
  </block>
</view>

<!-- 弹窗客服 -->
<wechat-popup weichatShow="{{weichatShow}}" info="{{weichatCustomerService}}" bindonClickHide="closeWeichatCustomerService" />