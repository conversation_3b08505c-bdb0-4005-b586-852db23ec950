<van-overlay z-index="999" show="{{ show }}" bind:click="onClickHide">
  <view class="wrapper">
    <view class="block">
      <view class="title">答题<text>规则</text>
        <image class="start" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_star.png"></image>
      </view>
      <view class="content">
        <!-- <view>1. 点击“开始”后进入作答页面同时开始计时，看题思考时长<text>3分钟</text>，答题时长<text>5分钟</text>，计时结束将自动停止录音。</view>
        <view>2. 作答完毕AI会进行<text>智能点评</text>。</view>
        <view> 3. 请确保使用期间网络环境良好及周围环境安静。，答题时长5分钟，计时结束将自动停止录音。</view> -->
        <rich-text nodes="{{text}}" class="text"></rich-text>
      </view>
      <view class="know-btn" catch:tap="confirm">我知道了</view>
      <!-- <view class="title">{{title}}</view>
      <view class="bottom-box">
        <view class="item" catch:tap="cancel">{{cancel}}</view>
        <view class="item" catch:tap="confirm">{{confirm}}</view>
      </view> -->
    </view>
  </view>
</van-overlay>