/* components/image-preview/image-preview.wxss */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  display: block;
  height: auto;
  width: 100%;
}

.close-btn {
  position: absolute;
  top: 110px;
  right: 20px;
  width: 64rpx;
  height: 64rpx;
  background-color: rgba(0, 0, 0, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100rpx;
  .icon {
    width: 16rpx;
    display: block;
  }
}
