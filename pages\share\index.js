const ROUTER = require("@/services/routerManager")
const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  data: {
    shareSuccess: false,
    hasClickShare: false, // 记录是否点击了分享按钮
    showPopup: false,
    shareData: null,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    this.getShareData()
  },
  onShow() {
    this.getShareData()
  },
  // 获取分享数据
  getShareData() {
    UTIL.request(API.getShareData).then((res) => {
      this.setData({
        shareData: res.data,
      })
    })
  },
  // 分享数据
  submitShareData() {
    UTIL.request(API.getHandleGuide)
  },
  onClickHide() {
    this.setData({
      showPopup: false,
    })
    wx.navigateBack()
  },

  addKefu() {
    let customerService = this.data.shareData.customer_config
    this.setData({
      showPopup: false,
    })
    ROUTER.navigateTo({
      path: "/pages/webview/web/index",
      query: {
        url: customerService.url,
      },
    })
  },
  async onShareBtnTap() {
    console.log("用户点击了分享按钮")
    if (this.data.shareData.is_guide_share_donate) {
      wx.showToast({
        title: this.data.shareData.share_error_text,
        duration: 2000,
        icon: "none",
      })
      return
    }
    this.setData({
      showPopup: true,
    })
    await this.submitShareData()
    this.getShareData()
  },

  getPageShareParams() {
    let query = {}
    return APP.createShareParams({
      title: this.data?.shareData?.share_info?.title || "",
      imageUrl: this.data?.shareData?.share_info?.image || "",
      path: "/pages/practice/home/<USER>",
      query: query,
    })
  },
  onShareAppMessage() {
    return this.getPageShareParams()
  },
})
