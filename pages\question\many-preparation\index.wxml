<rule-header pageTitle="{{pageTitle}}" show_white="{{show_white}}" bindbackPage="backPage"></rule-header>
<view class="main-content">
  <view class="rule-box">
    <image class="rule-comma" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_comma.png"></image>
    <view class="title">{{questionData.question_volume_info.name}}</view>
    <view class="sub-title" wx:if="{{questionData.desc}}">{{questionData.desc}}</view>

    <view class="face-time">
      <view class="text">面试时长</view>
      <view class="time"><text>{{questionData.question_volume_info.answer_time}}</text>分钟</view>
    </view>
    <view class="face-time mt24">
      <view class="text">出题模式</view>
      <view class="time"><text>{{questionData.question_volume_info.question_mod == 1?'听题':'看题'}}模式</text></view>
    </view>
    <!-- <view class="question-setting">
      <view class="titles">选择出题模式</view>
      <view class="question-setting-box">
        <view class="question-setting-item {{activeIndex==0?'active':''}}" bindtap="changeSetting" data-item="{{0}}">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_listen.png" class="icon"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_listen_select.png" class="icon-select"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_select.png" class="select"></image>

          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_listen_bg.png" class="select-bg"></image>
          <text>听题模式</text>
        </view>
        <view class="question-setting-item  {{activeIndex==1?'active':''}}" bindtap="changeSetting" data-item="{{1}}">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_see.png" class="icon"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_see_select.png" class="icon-select"></image>
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_select.png" class="select"></image>

          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_select_bg.png" class="select-bg" style="top: 15rpx;"></image>
          <text>看题模式</text>
        </view>
      </view>
    </view> -->
    <view class="answering-process">
      <view class="answering-title">作答流程</view>
      <view class="answering-process-box">
        <block wx:if="{{questionData.question_volume_info.question_mod == 1}}">
          <view class="process-item" wx:for="{{questionData.process_list_listen}}">
            <view class="yuan"></view>
            <view class="right-box">
              <view class="titles">{{item.title}}</view>
              <view class="label">{{item.desc}}</view>
            </view>
          </view>
        </block>
        <block wx:else>
          <view class="process-item" wx:for="{{questionData.process_list}}">
            <view class="yuan"></view>
            <view class="right-box">
              <view class="titles">{{item.title}}</view>
              <view class="label">{{item.desc}}</view>
            </view>
          </view>
        </block>

      </view>
    </view>
    <view class="text-box" wx:if="{{questionData.question_volume_info.explanation}}">
      <view class="text-title">作答规则</view>
      <text>{{questionData.question_volume_info.explanation}} </text>
    </view>
  </view>
  <view class="action-bar-box">
    <view class="action-bar container flex-justify_between">
      <!-- <view class="left-collect" catchtap="goHistory">
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/rule_history.png"></image>
        <text>历史作答</text>
      </view> -->

      <authorize-phone class="button" customClass="button" isBindPhone="{{isLogin}}" bind:onAuthorize="goQuestion">
        开始作答
      </authorize-phone>
    </view>
  </view>
</view>

<!--  -->