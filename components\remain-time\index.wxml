<view class="remain-time {{elementTop?'subject-list-remain':''}}  custom-class" style="top:{{elementTop}}px !important">
  <!-- 无权限 -->
  <block wx:if="{{verifyRecord.error_code == '7010'}}">
    <view class="remain-time-box error " style="justify-content: space-between;" catchtap="openWeichatCustomerService">
      <!-- <view class="text">没有{{verifyRecord.question_type!='single'?'本套题':'本题'}}练习权限，可咨询老师获取</view> -->
      <view class="text">
        <rich-text nodes="{{verifyRecord.message_html}}"></rich-text>
      </view>
      <view class="right-btn" wx:if="{{verifyRecord.customer_config}}">{{verifyRecord.tips_text}}
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_red_arrow.png?v1"></image>
      </view>
    </view>
  </block>
  <!-- 答题次数不足 -->
  <block wx:elif="{{verifyRecord.error_code == '7009'}}">
    <view class="remain-time-box error " wx:if="{{verifyRecord.message}}" catchtap="openWeichatCustomerService">
      <!-- <view class="text">今日{{isMultiple?'套卷':'单题'}}作答次数已用尽，明日再来体验</view> -->
      <view class="text">{{verifyRecord.message}}
      </view>
      <image class="right-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_red_arrow.png?v1"></image>
    </view>
  </block>
  <!-- 出题关闭 -->
  <block wx:elif="{{verifyRecord.error_code == '7011'}}">
    <view class="remain-time-box error ">
      <view class="text">
        <rich-text nodes="{{verifyRecord.message_html}}"></rich-text>
      </view>
    </view>
  </block>
  <!-- 出题正常 -->
  <block wx:else>
    <view class="remain-time-box" catchtap="openWeichatCustomerService" style="justify-content: space-between;">
      <view class="text">
        <rich-text nodes="{{verifyRecord.message_html}}"></rich-text>
      </view>
      <!-- <view class="text" wx:if="{{verifyRecord.donate_type=='daily'}}">
        今日{{verifyRecord.question_type!='single'?'套卷':'单题'}}作答次数剩余：<text>{{verifyRecord.donate_num}}</text>次
      </view>
      <view class="text" wx:elif="{{verifyRecord.donate_type!='daily'}}">
        {{verifyRecord.question_type!='single'?'套卷':'单题'}}作答次数剩余：<text>{{verifyRecord.donate_num}}</text>次
      </view> -->
      <!-- 可以根据需要取消注释下面的按钮部分 -->
      <view class="right-btn" wx:if="{{verifyRecord.customer_config}}">{{verifyRecord.tips_text}}
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_blue_arrow.png?v1"></image>
      </view>
    </view>
  </block>
</view>

<!-- 弹窗客服 -->
<wechat-popup weichatShow="{{weichatShow}}" info="{{weichatCustomerService}}" bindonClickHide="closeWeichatCustomerService" />