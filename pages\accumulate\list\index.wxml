<wxs module="utils2">
  function formatTime(input) {
    // 尝试将输入转换为数字
    var seconds = input;

    // 如果秒数为0，直接返回空字符串
    if (seconds == 0) {
      return "";
    }

    var hours = Math.floor(seconds / 3600);
    var minutes = Math.floor((seconds % 3600) / 60);
    seconds = Math.floor(seconds % 60);

    var formattedTime = "";

    if (hours > 0) {
      // 如果有时，则格式化为 "X小时Y分Z秒"
      formattedTime += hours + "小时" + padZero(minutes) + "分";
      if (seconds > 0) {
        formattedTime += padZero(seconds) + "秒";
      }
    } else if (minutes > 0) {
      // 如果没有小时但有分钟，则格式化为 "X分Y秒" 或 "X分"
      formattedTime += minutes + "分";
      if (seconds > 0) {
        formattedTime += padZero(seconds) + "秒";
      }
    } else {
      // 如果没有小时也没有分钟，则仅格式化为 "X秒"
      formattedTime += seconds + "秒";
    }

    return formattedTime;
  }

  function padZero(number) {
    return number < 10 ? "0" + number : "" + number;
  }

  function getIsshowWihte(id, arr) {
    for (var i = 0; i < arr.length; i++) {
      if (arr[i].id == id) {
        return arr[i].show_white;
      }
    }
    return ''
  }
  module.exports = {
    formatTime: formatTime,
    getIsshowWihte: getIsshowWihte,
  };
</wxs>
<view class="fixed-box">
  <view class="{{hideenShow?'oh-hideen':''}}">
    <tabbar-header show_white="{{utils2.getIsshowWihte(activeId,accumulateTab)}}" back="{{true}}"></tabbar-header>
    <scroll-view scroll-x enhanced="{{true}}" bounces="{{true}}" class="accumulate-tab {{ utils2.getIsshowWihte(activeId,accumulateTab)?'show-white':''}}" style="{{stickyHeight}}">
      <view class="accumulate-tab-item {{activeId == item.id?'active':''}}" wx:for="{{accumulateTab}}" wx:key="index" bind:tap="changeIndex" data-id="{{item.id}}">
        <view class="icon-box  {{tagId == item.id?'pl40':''}}">
          <image wx:if="{{tagId === item.id &&isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif" mode="widthFix"></image>
          <image wx:if="{{tagId === item.id &&!isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.png"></image>
          <text> {{item.title}}</text>
        </view>
      </view>
    </scroll-view>
    <block wx:if="{{ isComplete }}">
      <view wx:for="{{accumulateTab}}" wx:key="index" class="main-content">
        <scroll-view hidden="{{activeId != item.id}}" enable-passive="{{true}}" scroll-y style="{{listHeight}}" bindscroll="onScroll" bindscrolltoupper="onScrolltoupper" binddragend="onbinddragend" upper-threshold="{{10}}">
          <view class="accumulate-list" wx:if="{{item.dataList.length}}">
            <view class="accumulate-list-item {{id === citem.id? 'active':''}}" catchtap="goDetail" wx:for="{{item.dataList}}" wx:for-item="citem" wx:key="cindex" data-id="{{ citem.id }}">
              <view class="font">
                <view class="title text-ellipsis-2">
                  <!-- <image wx:if="{{id === item.id &&isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif"></image>
          <image wx:if="{{id === item.id &&!isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.png"></image> -->
                  {{citem.title}}
                </view>
                <view class="fonts">
                  <!-- <view class="time" wx:if="{{utils2.formatTime(item.video_time)}}">时长：{{utils2.formatTime(item.video_time)}}</view> -->
                  <block wx:if="{{citem.progress || id === citem.id}}">
                    <view class="progress">
                      <block wx:if="{{citem.progress == 100}}">
                        已听完
                      </block>
                      <block wx:else>
                        已听<text class="{{citem.progress > 9?'double-width':'single-width'}}" wx:if="{{citem.progress}}">{{citem.progress}}</text><text wx:else>0</text>%
                      </block>
                    </view>
                    <view class="lines">｜</view>
                  </block>
                  <view class="text-content-box text-ellipsis-1">{{citem.summary}}</view>
                </view>
              </view>
              <view class="right" wx:if="{{citem.video_url}}" catchtap="changePlay" data-item="{{citem}}">
                <view class="play-box" wx:if="{{id === citem.id && isPlaying}}">
                  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif"></image>
                </view>
                <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_accumulate_play.png"></image>
              </view>
              <!-- <view class="right" wx:if="{{item.video_url}}" catchtap="changePlay" data-item="{{item}}">
        <image wx:if="{{defaulId === item.id && defaulPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_accumulate_puase.png"></image>
        <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_accumulate_play.png"></image>
      </view> -->
            </view>
          </view>
          <view class="no-data-box" wx:if="{{!item.dataList.length && !item.isRequest}}">
            <tips-default text="内容筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
          </view>
        </scroll-view>
      </view>
    </block>
  </view>
  <!-- 底部导航栏 -->
  <home-tabbar class="home-tabbar" active="accumulate" bind:closeHidden="closeHidden" bind:openHideen="openHideen" />
</view>

<!-- <van-popup show="{{ show }}" round position="bottom" bind:close="onClose">
  <view class="play-box" catchtap="handlePlayBoxTap">
    <view class="play-box-top">
      <view class="left-menu" catchtap="openMenu">热点<image class="jian" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_expand.png"></image>
      </view>
      <view class="menu-box" wx:if="{{menuShow}}">
        <view class="menu-box-item" catchtap="changeMenu" wx:for="{{4}}" wx:key="index">
          时政
        </view>
      </view>
      <view class="right-btn">
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_cycle.png"></image>
        列表循环
      </view>
    </view>
    <view class="play-list">
      <scroll-view scroll-y class="scroll-box-h">
        <view class="play-list-item {{index === 2? 'active':''}}" wx:for="{{4}}">
          <view class="title text-ellipsis-2">
            <image wx:if="{{index === 2}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif"></image>党的二十届三中全会党的二十届三中全会党的二十届三中全会{{item.title}}
          </view>
          <view class="fonts">
            <view class="time">时长：5分12秒</view>
            <view class="lines">｜</view>
            <view class="progress">已听35%</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</van-popup> -->