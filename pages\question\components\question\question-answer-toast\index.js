// components/auto-hide-toast/auto-hide-toast.js
Component({
  properties: {
    // 控制显示
    show: {
      type: Boolean,
      value: false,
    },
    // 自动关闭延时（毫秒）
    duration: {
      type: Number,
      value: 2000,
    },
  },

  observers: {
    // 监听show变化
    show: function (newVal) {
      if (newVal&&this.data.duration) {
        // 显示时启动定时器
        this.startTimer()
      }
    },
  },

  methods: {
    // 启动定时器
    startTimer() {
      // 先清除已有定时器
      this.clearTimer()
      this.timer = setTimeout(() => {
        this.setData({ show: false })
        this.triggerEvent("hide")
      }, this.data.duration)
    },

    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
  },

  // 组件生命周期
  lifetimes: {
    detached() {
      // 组件销毁时清除定时器
      this.clearTimer()
    },
  },
})
