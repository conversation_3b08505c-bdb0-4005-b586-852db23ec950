<wxs module="utils">
  // 格式化时间戳
  function formatTimeStamp(time) {
    // 辅助函数需要定义在外部并在此处引用或直接定义在这里
    var formatSingleNumber = function (number) {
      return number <= 9 ? "0" + number : number;
    };

    time = time >>> 0; // 确保time是整数
    if (time < 0) {
      time = 0;
    }

    var minute = Math.floor((time % 3600) / 60),
      second = Math.floor(time % 60);

    // 返回格式化的分钟和秒
    var ms = formatSingleNumber(minute) + ":" + formatSingleNumber(second);

    return ms;
  }
  module.exports = {
    formatTimeStamp: formatTimeStamp,
  }
</wxs>
<block wx:if="{{isComplete}}">
  <navigation-bar back="{{true}}" isResult="{{true}}" isSticky="{{ true }}" showWhite="{{show_white}}">
    <view slot="center" class="tab-title {{show_white?'black-color':''}}">
      <view class="text-ellipsis-1">题目详情</view>
    </view>
  </navigation-bar>
  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/singe_bg.png" class="banner" mode="widthFix"></image>
  <view class="main-content" wx:if="{{questionData}}">
    <view class="practice-list">
      <view class="practice-list-item" wx:for="{{questionData.question_list}}">
        <view class="title-box">
          <text class="tag-label" wx:if="{{item.tag_name}}">{{item.tag_name}}</text>
          <view class="title">
            <rich-text-box strText="{{item.content_delta}}"></rich-text-box>
          </view>

        </view>
        <view class="information-box" wx:if="{{item.link_data.id}}" catch:tap="goLinkUrl" data-id="{{item.link_data.id}}">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_down.png"></image>
          <text class="">{{item.link_data.title}}</text>
        </view>
      </view>
    </view>
    <view class="content-box">
      <view class="top-menu">
        <image class="bg" mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_hui.png"></image>
        <view catchtap="changeTab" data-index="0" class="menu-item {{activeIndex==0?'active':''}}">
          <image wx:if="{{activeIndex==0}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/menu_left.png"></image>
          <text class="text">题目解析</text>
        </view>
        <view catchtap="changeTab" data-index="1" class="menu-item {{activeIndex==1?'active-right':''}}">
          <image wx:if="{{activeIndex==1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/menu_right.png"></image>
          <text class="text">历史作答</text>
        </view>
      </view>
      <view class="content-wraper" wx:if="{{activeIndex==0}}">
        <report-card title="思路点拨" wx:if="{{ questionData.question_list[0].question_analyze}}" isCollapse="{{false}}">
          <rich-text class="report-card-text" nodes="{{questionData.question_list[0].question_analyze}}" />
        </report-card>
        <report-card title="思维导图" wx:if="{{ questionData.question_list[0].mind_mapping_img}}">
          <image class="mind-mapping-img" bind:tap="checkImg" data-url="{{questionData.question_list[0].mind_mapping_img}}" src="{{questionData.question_list[0].mind_mapping_img}}" mode="widthFix" />
        </report-card>
        <report-card title="参考答案" wx:if="{{questionData.question_list[0].reference_answer}}" isCollapse="{{ false }}">
          <rich-text class="report-card-text" nodes="{{questionData.question_list[0].reference_answer}}" />
        </report-card>
        <report-card title="讲解视频" wx:if="{{ questionData.question_list[0].video_data && questionData.question_list[0].video_data.url }}">
          <my-video videoUrl="{{questionData.question_list[0].video_data.url }}" poster="{{ questionData.question_list[0].video_data.cover}}"></my-video>
        </report-card>
      </view>
      <block wx:if="{{activeIndex==1}}">
        <view class="answering-process" wx:if="{{historyList.length>0}}">
          <view class="answering-process-box">
            <view class="process-item" catchtap="goRecordId" data-item="{{item}}" wx:for="{{historyList}}" wx:key="index">
              <view class="yuan"></view>
              <view class="time">{{item.record_time}}</view>
              <view class="right-box">
                <view class="left-font">
                  <view class="titles text-ellipsis-2">第{{item.record_num}}次作答</view>
                  <view class="font-bottom">
                    <view class="waiting" wx:if="{{item.correct_status ==0}}">
                      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_waiting.png"></image>
                      报告生成中…
                    </view>
                    <view class="time-all" wx:if="{{item.correct_status ==-1}}">
                      报告生成失败
                    </view>
                    <block wx:if="{{item.correct_status ==1}}">
                      <view class="time-all">时长：<text>{{utils.formatTimeStamp(item.answer_time)}}</text></view>
                      <view class="line"></view>
                      <view class="score-all">得分：<text>{{item.score}}</text></view>
                    </block>
                  </view>
                </view>
                <image class="right-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
              </view>
            </view>
          </view>
        </view>
        <view wx:else style="padding-bottom: 220rpx;">
          <tips-default text="暂无历史作答记录" width="220" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
        </view>
      </block>
    </view>


    <view class="action-bar-box">
      <remain-time wx:if="{{questionData.verify_record && isLogin}}" verify-record="{{questionData.verify_record}}"></remain-time>
      <view class="action-bar container flex-justify_between">
        <authorize-phone isBindPhone="{{isLogin}}" bind:onAuthorize="collectTap">
          <view class="left-collect">
            <image wx:if="{{questionData.is_collect==1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_star_select.png"></image>
            <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_star.png"></image>
            <text>{{questionData.is_collect==1?'取消':'收藏'}}</text>
          </view>
        </authorize-phone>

        <view class="button hui" wx:if="{{(questionData.verify_record.error_code == '7009' || questionData.verify_record.error_code == '7010' || questionData.verify_record.error_code == '7011') && isLogin}}">去练习</view>
        <authorize-phone wx:else class="button" customClass="button" isBindPhone="{{isLogin}}" bind:onAuthorize="goQuestion">
          去练习
        </authorize-phone>
      </view>
    </view>
  </view>
  <tips-default wx:else text="内容筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
</block>