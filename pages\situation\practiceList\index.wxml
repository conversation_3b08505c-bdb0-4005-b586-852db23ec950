<navigation-bar back="{{true}}" isSticky="{{ true }}">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">{{pageTitle}}</view>
  </view>
</navigation-bar>
<view class="main-content" wx:if="{{dataList.length}}">
  <view class="situation-list">
    <view class="situation-list-item" wx:for="{{dataList}}" catch:tap="goDetail" data-id="{{ item.id }}">
      <view class="title text-ellipsis-2">{{ item. title }}</view>
      <view class="label text-ellipsis-2">{{ item.summary }}</view>
      <view class="time">{{ item.issue_time }}</view>
    </view>
  </view>
</view>
<tips-default width="280" wx:else text="内容筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>