<wxs module="utils2">
  function formatTime(input) {
    // 尝试将输入转换为数字
    var seconds = input;

    // 如果秒数为0，直接返回空字符串
    if (seconds == 0) {
      return "";
    }

    var hours = Math.floor(seconds / 3600);
    var minutes = Math.floor((seconds % 3600) / 60);
    seconds = Math.floor(seconds % 60);

    var formattedTime = "";

    if (hours > 0) {
      // 如果有时，则格式化为 "X小时Y分Z秒"
      formattedTime += hours + "小时" + padZero(minutes) + "分";
      if (seconds > 0) {
        formattedTime += padZero(seconds) + "秒";
      }
    } else if (minutes > 0) {
      // 如果没有小时但有分钟，则格式化为 "X分Y秒" 或 "X分"
      formattedTime += minutes + "分";
      if (seconds > 0) {
        formattedTime += padZero(seconds) + "秒";
      }
    } else {
      // 如果没有小时也没有分钟，则仅格式化为 "X秒"
      formattedTime += seconds + "秒";
    }

    return formattedTime;
  }

  function padZero(number) {
    return number < 10 ? "0" + number : "" + number;
  }

  module.exports = {
    formatTime: formatTime,
  };
</wxs>
<view class="vans-tabbar van-hairline--top-bottom  van-tabbar--fixed" style="{{ zIndex ? 'z-index: ' + zIndex : '' }}">
  <slot />
</view>
<view style="height: {{ height }}px;width: 100%;">
</view>

<view class="boottom-video" wx:if="{{showAudio && title}}" style="bottom: {{ height+8 }}px;">
  <image src="{{coverImgUrl}}" class="music-bg"></image>
  <text class="text-ellipsis-1" catchtap="goMaterial">{{title}}</text>
  <view class="circle-box">
    <van-circle value="{{ progress}}" type="2d" layer-color="rgba(60, 61, 66, 0.3)" speed="{{0}}" color="#3C3D42" stroke-width="1.5" size="32">
      <view class="bo-box" catchtap="playMusic" style="width: 100%;height: 64rpx;" wx:if="{{!isPlaying}}">
        <image class="imgs" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_play_b.png"></image>
      </view>
      <view class="bo-box" catchtap="pausedMusic" wx:else style="width: 100%;height: 64rpx;">
        <image class="imgs" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_pause_b.png"></image>
      </view>
    </van-circle>
  </view>
  <image class="menu" catchtap="openList" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_menu.png"></image>
  <image class="close" catchtap="closeMusic" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_close.png"></image>
</view>

<van-popup show="{{ show }}" overlay-style="background-color: rgba(0, 0, 0, 0.3); " round position="bottom" bind:close="onClose" z-index="999">
  <view class="play-box" catchtap="handlePlayBoxTap">
    <view class="play-box-top">
      <view class="left-menu" catchtap="openMenu">{{tabList[activeIndex].title}}
        <image class="jian {{menuShow?'xuan':''}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_on_arrow.png"></image>
      </view>
      <view class="menu-box" wx:if="{{menuShow}}">
        <view class="menu-box-item" catchtap="changeMenu" data-index="{{index}}" wx:for="{{tabList}}" wx:key="index">
          {{item.title}}
        </view>
      </view>
      <view class="right-btn" catchtap="changeMode" data-type="random" wx:if="{{playMode=='loop-list'}}">
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_cycle.png"></image>
        列表循环
      </view>
      <view class="right-btn" catchtap="changeMode" data-type="loop-list" wx:else>
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_shuffle.png"></image>
        随机播放
      </view>
    </view>
    <view class="play-list">
      <scroll-view scroll-y class="scroll-box-h" :show-scrollbar="false">

        <view class="play-list-item {{id == item.id ? 'active':''}}" catchtap="changePlay" data-item="{{item}}" wx:for="{{musicList}}" wx:key="index">
          <view class="title text-ellipsis-2">
            <image wx:if="{{((id == item.id) &&isPlaying)}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.gif"></image>
            <image wx:if="{{id === item.id &&!isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulation_play.png"></image>
            {{item.title}}
          </view>
          <view class="fonts">
            <view class="time" wx:if="{{utils2.formatTime(item.video_time)}}">时长：{{utils2.formatTime(item.video_time)}}</view>
            <block wx:if="{{item.progress}}">
              <view class="lines">｜</view>
              <view class="progress">
                <block wx:if="{{item.progress == 100}}">
                  已听完
                </block>
                <block wx:else>
                  已听{{item.progress}}%
                </block>
              </view>
            </block>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</van-popup>