const APP = getApp()
const BASE_CACHE = require("@/utils/cache/baseCache")
const ROUTER = require("@/services/routerManager")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
let audioService = null
Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentProvincekey: null,
    examKey: null,
    examListArray: [],
    provinceListArray: [],

    back: false,
  },

  onLoad: async function (options) {
    console.log("进来没得吗")
    await APP.checkLoadRequest()
    audioService = APP.globalData.audioService
    if (options.isEntryFirst) {
      this.setData({
        isEntryFirst: true,
      })
    }
    if (options.back) {
      this.setData({
        back: true,
      })
    }
    this.initSelectd()
  },
  onShow() {},

  initSelectd() {
    const { province, exam } = BASE_CACHE.getBaseCache() || {}
    let examListArray = APP.globalData.serverConfig.exam_province_list
    let provinceListArray = examListArray[0]?.province_list || []
    if (exam?.key) {
      const list =
        examListArray.find((item) => item.exam_key === exam.key) ||
        examListArray[0]
      provinceListArray = list.province_list
    }

    let currentProvincekey = province?.key
    let examKey = exam?.key

    // 验证考试key是否有效
    if (!examListArray.find((item) => item.exam_key === examKey)) {
      examKey = ""
    }

    // 验证省份key是否有效
    if (
      !provinceListArray.find(
        (item) => item.province_key === currentProvincekey
      )
    ) {
      currentProvincekey = ""
    }

    this.setData({
      examListArray: examListArray,
      provinceListArray: provinceListArray,
      examKey,
      currentProvincekey,
    })
  },
  handleChangeProvince(data) {
    this.setData({
      currentProvincekey: data.province_key,
    })
  },
  onChangeProvince(e) {
    // 获取当前选中的省份信息
    const selectedProvince = e.detail
    this.setData({
      currentProvincekey: selectedProvince.province_key,
    })
  },
  onChangeExam(e) {
    const selectedExam = e.detail
    const examKey = selectedExam.exam_key
    let currentProvincekey = this.data.currentProvincekey
    const province_list = selectedExam.province_list
    // if (
    //   province_list.findIndex((item) => item.province_key === currentProvincekey) < 0
    // ) {
    //   currentProvincekey = province_list[0].province_key
    // }
    this.setData({
      examKey,
      currentProvincekey,
      provinceListArray: province_list,
    })
  },
  seveSelectd() {
    let examObj = this.data.examListArray.find(
      (item) => item.exam_key === this.data.examKey
    )
    let provinceObj = examObj.province_list.find(
      (item) => item.province_key === this.data.currentProvincekey
    )
    const exam = {
      name: examObj.exam_name,
      key: examObj.exam_key,
    }
    const province = {
      name: provinceObj.province_name,
      key: provinceObj.province_key,
    }
    BASE_CACHE.setBaseExamCache(exam)
    BASE_CACHE.setBaseProvinceCache(province)
  },
  closeMusic() {
    audioService.clearMusic()
  },
  async goHome() {
    if (!this.data.currentProvincekey || !this.data.examKey) return
    const { province, exam } = BASE_CACHE.getBaseCache() || {}
    let isChange = false
    if (
      this.data.currentProvincekey != province.key ||
      this.data.examKey != exam.key
    ) {
      await this.closeMusic()
      isChange = true
    }
    this.seveSelectd()
    if (isChange) {
      await audioService.getCcumulationListTag()
      if (audioService?._state?.tabList[0]?.id) {
        await audioService.getMusicList(audioService._state.tabList[0].id)
      }
      this.closeMusic() // 这里再次调用是因为切换省份重新设置了播放清单
    }
    UTIL.request(API.changeUserSetting, {
      province: this.data.currentProvincekey,
      exam: this.data.examKey,
    }).then((res) => {
      // this.seveSelectd()
      isChange = false
      if (this.data.back) {
        const pages = getCurrentPages()
        if (pages.length >= 2) {
          let page = pages[pages.length - 2]
          let options = page.options
          delete options.province
          delete options.exam
          delete options.campus_id

          console.log(page)
          ROUTER.reLaunch({
            path: "/" + page.route,
            query: options,
          })
          return
        }
      }

      ROUTER.reLaunch({
        path: "/pages/practice/home/<USER>",
        query: {},
      })
    })
  },
})
