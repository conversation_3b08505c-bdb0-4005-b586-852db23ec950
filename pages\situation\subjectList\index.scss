page {
  background: #f2f4f7;
  box-sizing: border-box;
  overscroll-behavior: none;
  // position: fixed;
  // top: 0;
  // left: 0;
  // bottom: 0;
  // right: 0;
  // overflow-y: auto;
}

.fixed-box {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
}

.top-box {
  width: 100%;
  padding: 48rpx 32rpx;
  text-align: center;
  font-size: 36rpx;
  color: #22242e;
  line-height: 48rpx;
  font-weight: bold;
  box-sizing: border-box;
}

.main-content {
  padding: 16rpx 32rpx 64rpx 32rpx;
}
.pt32 {
  padding-top: 32rpx !important;
}

.practice-list {
  &-item {
    width: calc(100% - 64rpx);
    position: relative;
    background: #fff;
    padding: 32rpx;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    text-align: left;
    .title {
      font-size: 30rpx;
      color: #3c3d42;
      line-height: 40rpx;
      .title-text {
        background: rgba(76, 96, 133, 0.1);
        color: #4c6085;
        font-size: 22rpx;
        line-height: 40rpx;
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
        margin-right: 10rpx;
      }
      .title-span {
        vertical-align: middle; /* 添加这个属性来帮助垂直对齐 */
      }
    }
    .new {
      top: -14rpx;
      right: -8rpx;
      position: absolute;
      width: 52rpx;
      height: 28rpx;
    }
    .line {
      display: flex;
      margin-top: 40rpx;
      width: 100%;
      height: 2rpx;
      background: rgba(235, 236, 240, 1);
    }
    .card-bottom {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24rpx;
      .text-num {
        margin-left: 32rpx;
      }
    }
    .gray-text {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1) !important;
    }
    .blue-text {
      font-size: 24rpx;
      color: #e60000;
    }
    .number {
      font-size: 24rpx;
      color: #c2c5cc;
    }
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
}

.action-bar-box {
  display: flex;
  z-index: 998;
  height: 88rpx;
  position: relative;
  box-sizing: border-box;
}

.action-bar .button {
  width: 100%;
  height: 100%;
  background-color: var(--main-color);
  font-size: 30rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-collect {
  display: flex;
  flex-direction: column;
  margin-right: 40rpx;
  text {
    font-size: 20rpx;
    color: rgba(145, 148, 153, 1);
  }
  image {
    width: 40rpx;
    height: 40rpx;
    margin-top: 4rpx;
  }
}

.search-box {
  background: #fff;
  padding: 16rpx 32rpx 32rpx 32rpx;
  .search-box-content {
    background: rgba(242, 244, 247, 0.5);
    padding: 20rpx;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    input {
      font-size: 28rpx;
      flex: 1;
      min-width: 0;
      color: rgba(60, 61, 66, 1);
    }
  }
}
.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}
.information-box {
  width: 398rpx;
  display: flex;
  align-items: center;
  flex: 1;
  image {
    width: 28rpx;
    height: 28rpx;
    margin-right: 8rpx;
  }
  .text-information {
    font-size: 24rpx;
    color: rgba(76, 96, 133, 1);
    line-height: 36rpx;
    flex: 1;
  }
}

.page-remain-time {
  border-bottom: 0.5px solid #ebecf0;
}
