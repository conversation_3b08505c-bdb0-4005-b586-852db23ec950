<view class="{{hideenShow?'oh-hideen':''}}">
  <tabbar-header show_white="{{show_white}}" back="{{true}}" title="面试课程">
  </tabbar-header>
  <view class="main-content" wx:if="{{isComplete}}">
    <block>
      <view class="course-list" wx:if="{{courseList.length>0}}">
        <block wx:for="{{courseList}}" wx:key="index">
          <!-- 普通课程 -->
          <block wx:if="{{item.course_type === 'course'}}">
            <card-type-horizontal style="width: 100%;" item="{{item}}" wx:if="{{item.mp_img &&item.show_cover ===1 }}"></card-type-horizontal>
            <card-type-deafult style="width: 100%;" item="{{item}}" wx:else></card-type-deafult>
          </block>
          <!-- 高端版课程 -->
          <!-- <block wx:elif="{{item.course_type === 'advclass'}}">
          <card-type-advclass style="width: 100%;" item="{{item}}"></card-type-advclass>
        </block> -->
          <!-- 面授课 -->
          <block wx:elif="{{item.course_type === 'offline_classes'}}">
            <view class="face-list-card" catchtap="goDetail" data-item="{{item}}" wx:if="{{item.img_data.type === 2}}">
              <image src="{{item.img_data.url}}" class="bg-img"></image>
              <view class="bottom-box">
                <view class="left-box">

                  <view class="title {{item.name.length>16?'text-ellipsis-2':''}}">
                    {{item.name}}
                  </view>
                  <view class="time" wx:if="{{item.name&&item.name.length<16}}">{{item.summary}}</view>
                </view>
                <view class="see-btn">查看详情</view>
              </view>
            </view>
            <view class="face-defaul-card" catchtap="goDetail" data-item="{{item}}" wx:elif="{{item.img_data.type === 1}}">
              <image src="{{item.img_data.url}}" class="banner-img"></image>
              <view class="fonts">
                <view class="title {{item.name.length>14?'text-ellipsis-2':''}}">{{item.name}}</view>
                <view class="label-text text-ellipsis-1" wx:if="{{item.name.length<14}}">{{item.summary}}</view>
                <view class="font-bottom">
                  <view class="left-tex">{{item.participants}}人已学</view>
                  <view class="see-detail">查看详情</view>
                </view>
              </view>
            </view>
            <view class="course-card" catchtap="goDetail" data-item="{{item}}" wx:elif="{{item.img_data.type === 0 || !item.img_data}}">
              <view class="titles text-ellipsis-2">
                {{item.name}}
              </view>
              <view class="label-text" wx:if="{{ite.summary}}">
                {{item.summary}}
              </view>
              <view class="label-list">
                <view class="try-listen" style="color: {{ite.font_color}};background: {{ite.color}};" wx:for="{{item.new_tags}}" wx:key="index" wx:for-item="ite">
                  {{ite.tag}}
                </view>
              </view>
              <view class="fonts-bootom">
                <view class="left-tex">{{item.participants}}人已学</view>
                <view class="see-detail">查看详情</view>
              </view>
            </view>
          </block>
        </block>
      </view>
      <tips-default wx:else text="课程内容筹备中" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
    </block>
  </view>
</view>
<!-- 底部导航栏 -->
<home-tabbar active="improve" bind:closeHidden="closeHidden" bind:openHideen="openHideen" />