const UTIL = require("@/utils/util")
import * as echarts from "../../ec-canvas/echarts"
Page({
  data: {
    ec: {
      onInit: null,
    },
    data: [30, 340, 500, 300, 490, 400],
    status: 1, // 初始播放状态
    isOpen: false, // 是否展开
    isMirror: true, // 是否镜像
    listenCount: 1, // 已听遍数
    dateInfo: "2024年12月14日重庆市市辖区涪陵区事业单位(网友回忆版)",
    showCacelGrouponDialog: false,
    duration: 642.632,
    currentTime: 270,
    isDragging: false,
    progressWidth: 50,
    facPopuShow: true,
  },
  onLoad(options) {},
  initChart(canvas, width, height, dpr) {
    const chart = echarts.init(canvas, null, {
      width: width,
      height: height,
      devicePixelRatio: dpr,
    })
    canvas.setChart(chart)

    var scores = this.data.scores

    var option = {
      backgroundColor: "#ffffff",
      xAxis: {
        show: false,
      },
      yAxis: {
        show: false,
      },
      radar: {
        radius: "65%",
        indicator: [
          { name: "{a|作答内容}\n{b|" + scores[0] + "分" + "}", max: 500 },
          { name: "{a|流畅度}\n{b|" + scores[1] + "分" + "}", max: 500 },
          { name: "{a|扣题}\n{b|" + scores[2] + "分" + "}", max: 500 },
          { name: "{a|答题框架}\n{b|" + scores[3] + "分" + "}", max: 500 },
          { name: "{a|作答时长}\n{b|" + scores[4] + "分" + "}", max: 500 },
          { name: "{a|作答逻辑}\n{b|" + scores[5] + "分" + "}", max: 500 },
        ],
        name: {
          textStyle: {
            fontSize: 12,
            color: "#000",
            rich: {
              a: {
                lineHeight: 18,
                align: "center", // 居中对齐
                color: "rgba(145, 148, 153, 1)",
              },
              b: {
                lineHeight: 18,
                align: "center", // 居中对齐
                color: "rgba(102, 102, 102, 1)",
              },
            },
          },
        },
      },
      series: [
        {
          name: "预算",
          type: "radar",
          data: [
            {
              value: scores,
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "rgba(23, 139, 255, 0.70)" },
                    { offset: 1, color: "rgba(60, 172, 255, 0.20)" },
                  ]),
                },
              },
              itemStyle: {
                color: "#fff", // 空心圆：内部填充为无色
                borderColor: "rgba(0, 146, 255, 1)", // 蓝色边框
                borderWidth: 2, // 边框宽度
              },
              lineStyle: {
                color: "rgba(0, 146, 255, 1)", // 设置线条颜色为蓝色
                width: 1.5, // 线条宽度
              },
            },
          ],
        },
      ],
    }

    chart.setOption(option)
    return chart
  },
  onShow() {},

  handlePopChange(e) {
    this.setData({
      isOpen: e.detail.open,  
    })
  },
  close() {
    this.setData({
      facPopuShow: false,
    })
  },
})
