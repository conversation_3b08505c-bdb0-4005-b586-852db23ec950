/* pages/speech/components/min-card/index.wxss */

.list-item {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
  padding: 32rpx 32rpx 24rpx 32rpx;
  .top-area {
    display: flex;
    align-items: center;
    .top-left {
      width: calc(100% - 48rpx);
      .list-title-text {
        color: #3c3d42;
        font-size: 28rpx;
        font-weight: 500;
        margin-bottom: 16rpx;
        line-height: 42rpx;
        text {
          font-size: 20rpx;
          color: #fff;
          background: linear-gradient(272deg, #fae396 0%, #cca629 100%);
          border-radius: 8rpx;
          height: 32rpx;
          padding: 0 8rpx;
          line-height: 32rpx;
          // margin-top: -4rpx;
          transform: translateY(-4rpx);
          display: inline-block;
          margin-right: 10rpx;
        }
      }
      .list-text {
        width: 100%;
        font-weight: 400;
        font-size: 24rpx;
        color: #919499;
        white-space: nowrap; /* 强制不换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 溢出时显示省略号 */
      }
    }
    .arrow {
      width: 24rpx;
      height: 24rpx;
      margin-left: 24rpx;
    }
  }
  .line {
    width: 100%;
    height: 1rpx;
    background: #ebecf0;
    margin: 32rpx 0 16rpx 0;
  }
  .bottom-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .photo {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        margin-right: 12rpx;
        position: relative;
        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 0.5rpx solid #eaeaea;
        }
        .index-label {
          display: flex;
          align-items: center;
          padding-left: 6rpx;
          font-size: 12rpx;
          background: linear-gradient(
            90deg,
            #fa9733 0%,
            rgba(250, 151, 51, 0.6) 58%,
            rgba(250, 151, 51, 0) 100%
          );
          &.hui {
            background: linear-gradient(
              90deg,
              #aeb0b4 0%,
              rgba(174, 176, 180, 0.6) 58%,
              rgba(174, 176, 180, 0) 100%
            );
          }
          &.ju {
            background: linear-gradient(
              90deg,
              #e66d57 0%,
              rgba(230, 109, 87, 0.6) 58%,
              rgba(230, 109, 87, 0) 100%
            );
          }
          color: #fff;
          width: 24rpx;
          height: 20rpx;
          position: absolute;
          bottom: 0;
          left: 0;
          border-radius: 28rpx;
        }
        // background: red;
      }
    }
    .right {
      font-weight: 400;
      font-size: 22rpx;
      color: #c2c5cc;
    }
  }
  .label-box {
    display: flex;
    align-items: center;
    .label-text {
      font-size: 24rpx;
      color: #919499;
      text {
        color: #666;
      }
      &.red {
        text {
          color: #e60000;
        }
      }
    }
    .lines {
      width: 0.5px;
      height: 24rpx;
      background-color: #ebecf0;
      margin: 0 25rpx;
    }
  }
}

.fire-box {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: rgba(249, 79, 79, 1);
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}

.start-time {
  font-size: 24rpx;
  color: rgba(248, 158, 44, 1);
}
