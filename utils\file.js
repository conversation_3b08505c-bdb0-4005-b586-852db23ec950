/**
 * 文件类型与 MIME 类型映射
 */
const FILE_EXTENSIONS = {
  pdf: "application/pdf",
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
  png: "image/png",
  gif: "image/gif",
  mp4: "video/mp4",
  m4v: "video/x-m4v",
  doc: "application/msword",
  docx:
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ppt: "application/vnd.ms-powerpoint",
  pptx:
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
}

/**
 * 获取文件信息
 * @param {string} url - 文件 URL
 * @returns {Object|null} 包含 MIME 类型和扩展名的对象，如果无法解析则返回 null
 */
function getFileInfo(url) {
  const extPattern = /\.[^/.]+$/
  const match = url.match(extPattern)

  if (match && FILE_EXTENSIONS[match[0].slice(1)]) {
    return {
      mimeType: FILE_EXTENSIONS[match[0].slice(1)],
      extension: match[0].slice(1),
    }
  }
  return null
}

/**
 * 解析 URL，返回基础 URL 和查询参数对象
 * @param {string} urlString - 完整的 URL
 * @returns {Object} 包含基础 URL 和查询参数的对象
 */
function parseUrl(urlString) {
  const queryStart = urlString.indexOf("?")

  if (queryStart === -1) {
    return { baseUrl: removeProtocol(urlString), queryParams: {} }
  }

  const baseUrlWithProtocol = urlString.slice(0, queryStart)
  const queryString = urlString.slice(queryStart + 1)
  const baseUrl = removeProtocol(baseUrlWithProtocol)
  const queryParams = parseQueryString(queryString)

  return { baseUrl, queryParams }
}

/**
 * 解析查询字符串
 * @param {string} queryString - 查询字符串
 * @returns {Object} 解析后的查询参数对象
 */
function parseQueryString(queryString) {
  const queryParams = {}
  queryString.split("&").forEach((pair) => {
    if (pair) {
      const [key, value] = pair.split("=", 2)
      queryParams[decodeURIComponent(key)] = decodeURIComponent(value || "")
    }
  })
  return queryParams
}

/**
 * 移除 URL 中的协议部分
 * @param {string} url - 完整的 URL
 * @returns {string} 移除协议后的 URL
 */
function removeProtocol(url) {
  const protocolEndIndex = url.indexOf("//") + 2
  return protocolEndIndex > 1 ? url.slice(protocolEndIndex) : url
}

/**
 * 解码文件名
 * @param {string} url - 文件 URL
 * @returns {string} 解码后的文件名
 */
function decodeFileName(url) {
  const fileNameWithExtension = url.substring(url.lastIndexOf("/") + 1)
  return decodeURIComponent(fileNameWithExtension)
}
/**
 * 预览图片
 * @param {string} current - 当前图片 URL
 * @returns {Promise} 返回一个 Promise，成功时无返回值，失败时返回错误信息
 */
function previewImage(current) {
  return new Promise((resolve, reject) => {
    wx.previewImage({
      current,
      urls: [current],
      complete: () => {
        resolve() // 成功时无返回值
      },
      fail: (error) => {
        reject(error) // 返回错误信息
      },
    })
  })
}

/**
 * 预览视频
 * @param {string} url - 视频 URL
 * @returns {Promise} 返回一个 Promise，成功时无返回值，失败时返回错误信息
 */
function previewVideo(url) {
  return new Promise((resolve, reject) => {
    wx.previewMedia({
      sources: [{ url, type: "video" }],
      complete: () => {
        resolve() // 成功时无返回值
      },
      fail: (error) => {
        reject(error) // 返回错误信息
      },
    })
  })
}

/**
 * 处理文件下载和预览
 * @param {string} url - 文件 URL
 * @returns {Promise} 返回一个 Promise，成功时返回文件信息，失败时返回错误信息
 */
function previewFile(url) {
  return new Promise((resolve, reject) => {
    const fileInfo = getFileInfo(url)

    // 如果无法获取文件信息，直接下载文件
    if (!fileInfo) {
      wx.downloadFile({
        url,
        success: (res) => {
          const filePath = res.tempFilePath
          const fileName = decodeFileName(url)
          reject({ filePath, fileName }) // 返回文件信息
        },
        fail: (e) => {
          reject({ filePath: "", fileName: "未知文件" }) // 返回错误信息
        },
      })
      return
    }

    const extension = fileInfo?.extension?.toLowerCase()

    // 处理图片文件
    if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
      setTimeout(() => {
        previewImage(url)
          .then(() => resolve({ fileInfo, type: "image" })) // 返回文件信息和类型
          .catch((error) => reject(error)) // 返回错误信息
      }, 200)
    }
    // 处理视频文件
    else if (["mp4", "m4v"].includes(extension)) {
      setTimeout(() => {
        previewVideo(url)
          .then(() => resolve({ fileInfo, type: "video" })) // 返回文件信息和类型
          .catch((error) => reject(error)) // 返回错误信息
      }, 500)
    }
    // 处理其他文件类型
    else {
      downloadAndOpenDocument(url)
        .then(() => resolve({ fileInfo, type: "document" })) // 返回文件信息和类型
        .catch((error) => reject(error)) // 返回错误信息
    }
  })
}

/**
 * 下载文件并打开文档（返回 Promise）
 * @param {string} url - 文件 URL
 * @returns {Promise} 返回一个 Promise，成功时无返回值，失败时返回错误信息
 */
function downloadAndOpenDocument(url) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url,
      success: (res) => {
        wx.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            resolve() // 成功时无返回值
          },
          fail: (error) => {
            reject(error) // 返回错误信息
          },
        })
      },
      fail: (e) => {
        reject(e) // 返回错误信息
      },
    })
  })
}
/**
 * 导出模块
 */
module.exports = {
  parseUrl,
  previewFile,
}
