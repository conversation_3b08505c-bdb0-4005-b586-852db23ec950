.accordion {
  position: relative;
  z-index: 1;
  // padding: 0 32rpx;
  // background: #fff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  &-item {
    background: #fff;
    &:first-child {
      border-radius: 16rpx 16rpx 0 0;
    }
    &:last-child {
      border-radius: 0 0 16rpx 16rpx;
      border-bottom: 0;
    }
    &.active {
      & > .accordion-children {
        display: block;
      }
    }
    &.accordion-item-1 {
      padding: 0 32rpx 0 32rpx;
      position: relative;
      &::after {
        display: block;
        content: " ";
        position: absolute;
        width: calc(100% - 32px);
        height: 0.5px;
        background: rgba(235, 236, 240, 1);
        bottom: 0;
        left: 32rpx;
        z-index: 2;
      }
      &:last-child {
        &::after {
          display: none;
        }
      }
      & > .accordion-body > .accordion-body-content {
        border-color: transparent !important;
      }
    }
  }

  .accordion-body {
    display: flex;
    width: 100%;
  }
  .accordion-body-header {
    padding-top: 42rpx;
    width: 56rpx;
    .icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .accordion-body-content {
    padding-top: 40rpx;
    padding-bottom: 40rpx;
    flex: 1;
    display: flex;

    .accordion-left {
      flex: 1;
      line-height: 1.2;
    }
    .accordion-right {
      display: flex;
      align-items: center;
    }
  }
}
.accordion-info {
  .title {
    font-size: 28rpx;
    color: #22242e;
  }
  .describe {
    .text-group {
      margin-top: 4rpx;
      .text {
        font-size: 22rpx;
        color: #919499;
        & + .text {
          margin-left: 68rpx;
        }
      }
    }
  }
}
.accordion-children {
  display: none;
}
.right-info {
  display: flex;
  align-items: center;
  font-size: 0;
  .icon {
    vertical-align: middle;
    display: inline-block;
  }
  .text {
    font-size: 24rpx;
    color: #919499;
    vertical-align: middle;
    line-height: 1em;
    display: inline-block;
  }
}
.accordion-item-1 {
  .title {
    font-size: 30rpx;
    line-height: 1.5;
  }
}
.accordion-item-2 {
  border-top: 1rpx solid rgba($color: #ebecf0, $alpha: 0.5);
  .title {
    font-weight: 400;
  }
  &.active + .accordion-item-2 > .accordion-body > .accordion-body-content {
    border-color: transparent !important;
  }
  .accordion-body-header {
    padding-left: 24rpx;
  }
}
.accordion-item-3 {
  background-color: rgba(242, 244, 247, 0.5);
  margin: 0 -32rpx 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  border-radius: 0 !important;
  position: relative;
  &::after {
    display: block;
    content: " ";
    position: absolute;
    width: calc(100% - 32px);
    height: 0.5px;
    background-color: rgba(235, 236, 240, 1);
    left: 32rpx;
    bottom: 0;
  }
  &:last-child {
    &::after {
      display: none;
    }
  }

  &:nth-child(1) > .accordion-body > .accordion-body-content {
    border-color: transparent !important;
  }
  .title {
    font-size: 26rpx;
  }
}
.icon-box {
  display: flex;
  align-items: center;
  .icon {
    width: 24rpx;
    height: 24rpx;
  }
}
