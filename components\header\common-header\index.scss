.top-header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64rpx;
  margin-bottom: 12rpx;

  .exam-name {
    font-size: 30rpx;
    color: #18191a;

    .icon {
      display: inline-block;
      width: 0;
      height: 0;
      border-right: 8rpx solid transparent;
      border-left: 8rpx solid transparent;
      border-top: 10rpx solid #93969b;
      border-bottom: none;
      transform: translateY(-4rpx);
    }
  }
}

.status-box {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: transparent;
  // padding-left: 32rpx;
  box-sizing: border-box;
  &.bgf {
    background-color: #fff;
  }
}

.tab-mt {
  margin-top: 24rpx;
}
