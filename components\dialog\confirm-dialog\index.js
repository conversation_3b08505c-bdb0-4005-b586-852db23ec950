// components/common/dialog/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    title: {
      type: String,
      value: "确定提交提问吗？",
    },

    subtitle: {
      type: String,
      value: "",
    },
    cancel: {
      type: String,
      value: "取消",
    },
    confirm: {
      type: String,
      value: "确定",
    },
    // 点击遮罩层时是否关闭弹窗
    closeOnClickOverlay: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    onClickHide() {
      if (!this.data.closeOnClickOverlay) {
        return
      }
      this.triggerEvent("onClickHide", false)
    },
    cancel() {
      this.triggerEvent("cancel", false)
    },
    confirm() {
      this.triggerEvent("confirm", false)
    },
  },
})
