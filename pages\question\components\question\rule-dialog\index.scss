.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 600rpx;
  // height: 336rpx;
  box-sizing: border-box;
  border-radius: 24rpx;
  padding: 70rpx 48rpx 48rpx 48rpx;
  background: linear-gradient(186deg, #ffe2e2 0%, #ffffff 100%);
  .title {
    display: inline-block;
    font-size: 32rpx;
    color: #22242e;
    font-weight: bold;
    position: relative;
    text {
      color: #e60000;
    }
    image {
      position: absolute;
      right: -32rpx;
      width: 24rpx;
      height: 26rpx;
      top: 0;
    }
  }
  .content {
    margin-top: 40rpx;
    font-size: 26rpx;
    color: #3c3d42;
    line-height: 48rpx;
    .text {
      white-space: pre-wrap;
    }
    text {
      color: #e60000;
    }
  }
  .bottom-box {
    display: flex;
    align-items: center;
    padding: 0 48rpx;
    justify-content: space-between;
    padding-bottom: 48rpx;
    .item {
      font-size: 28rpx;
      padding: 32rpx 0;
      box-sizing: border-box;
      width: 224rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #eaeaea;
      border-radius: 16rpx;
      &:last-child {
        color: #fff;
        background: #e60000;
      }
    }
  }

  .know-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 470rpx;
    background: #e60000;
    height: 88rpx;
    color: #fff;
    border-radius: 16rpx;
    margin: 0 auto;
    margin-top: 80rpx;
  }
}
