page {
  background: #f2f4f7;
  box-sizing: border-box;
}

.main-content {
  padding: 32rpx 32rpx 64rpx 32rpx;
}

.situation-list {
  &-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    .title {
      font-size: 30rpx;
      color: #3c3d42;
    }
    .label {
      font-size: 24rpx;
      color: #919499;
      line-height: 36rpx;
      margin-top: 24rpx;
    }
    .time {
      font-size: 22rpx;
      color: #c2c5cc;
      margin-top: 30rpx;
    }
  }
}
.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}
