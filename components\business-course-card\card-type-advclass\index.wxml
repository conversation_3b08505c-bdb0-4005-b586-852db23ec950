<wxs module="utils">
  function findFour(arr) {
    if (arr.length > 3) {
      return arr.slice(0, 3);
    } else {
      return arr;
    }
  }
  module.exports = {
    findFour: findFour,
  };
</wxs>
<view class="course-card" bindtap="goDetail" wx:if="{{item}}" data-item="{{item}}" style="background-image:url({{item.course_bgimg}})">
  <view class="titles text-ellipsis-2">
    <view class="label-tip" wx:if="{{item.name_prefix}}" style="background:{{item.name_prefix.bg_color}};color: {{item.name_prefix.font_color}};">
      <image class="image" src="{{item.name_prefix.img}}"></image>
      {{item.name_prefix.text}}
    </view>
    {{item.name}}
  </view>
  <view class="label-text">
    {{item.intro}}
  </view>
  <view class="label-list">
    <view class="try-listen" style="color:{{ite.font_color}};background:{{ite.color}}" wx:key="index" wx:for="{{item.new_tags}}" wx:for-item="ite">
      {{ite.tag}}
    </view>
  </view>



  <!-- 在课程列表时的底部样式 -->
  <view class="fonts-bootom" wx:if="{{!isInHome}}">
    <view class="teacher-list">
      <block wx:if="{{item.teachers.length>0}}">
        <block wx:for="{{utils.findFour(item.teachers)}}" wx:key="index">
          <image wx:if="{{index<=2}}" src="{{item.portrait}}"></image>
        </block>
      </block>
    </view>
    <view class="right-box">
      <!-- 价格文本 -->
      <course-card-price-text courseType="advclass" price="{{item.product.price}}" oldPrice="{{item.product.o_price}}" desc="{{item.right_bottom_txt}}" isQualification="{{item.is_bought ===1}}" couponPrice="{{item.coupon_price || ''}}" />
    </view>
  </view>

  <!-- 首页时的底部样式 -->
  <view class="fonts-bootom" wx:else>
    <!-- 价格文本 -->
    <course-card-price-text couponPrice="{{item.coupon_price || ''}}" courseType="advclass" price="{{item.product.price}}" oldPrice="{{item.product.o_price}}" desc="{{item.right_bottom_txt}}" isQualification="{{!!item.is_bought}}" isHideDescText="{{true}}" isInHome="{{true}}" />
    <course-card-button isQualification="{{!!item.is_bought}}">
    </course-card-button>
  </view>
</view>