page {
  background: rgba(245, 246, 247, 1);
}
.speech-history-list {
  box-sizing: border-box;
  .speech-top-box {
    position: relative;
    width: 100%;
    height: 388rpx;
    margin-bottom: 24rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
    .speech-bg {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }
    .font {
      position: absolute;
      left: 0;
      bottom: 0;
      top: 0;
      right: 0;
      padding: 42rpx 40rpx 40rpx 40rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .top-area {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24rpx;
        width: 100%;
        .drill_fire {
          width: 40rpx;
          height: 40rpx;
          display: block;
        }
        .top-left {
          width: 470rpx;

          .title {
            font-size: 44rpx;
            font-weight: bold;
            color: #ffffff;
            line-height: 56rpx;
          }
        }
        .top-right {
          background: linear-gradient(90deg, #f94f4f 0%, #ff886c 100%);
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 400;
          font-size: 22rpx;
          color: #ffffff;
          width: 90rpx;
          height: 38rpx;
        }
        .blue-top-right {
          background: linear-gradient(90deg, #0092ff 0%, #e60000 100%);
        }
      }
      .desc-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #c2c5cc;
        line-height: 36rpx;
        margin-bottom: 48rpx;
        margin-top: 32rpx;
      }
      .time-text {
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
      }
      .bottom-area {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex: 1;
        .text {
          color: #c2c5cc;
          font-size: 24rpx;
          font-weight: 400;
        }
      }
    }
    .blank {
      align-items: center;
      justify-content: center;
      &-img {
        width: 96rpx;
        height: 96rpx;
        margin-bottom: 40rpx;
      }
      &-text {
        font-weight: 500;
        font-size: 24rpx;
        color: #eaeaea;
        line-height: 46rpx;
      }
    }
  }
}

.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: bold;
}

.main-content {
  padding: 32rpx;
  padding-bottom: 64rpx;
}
