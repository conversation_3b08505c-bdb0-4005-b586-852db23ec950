const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const { parseQuestionHtml } = require("@/utils/QuestionParseHtml")

let PAGE_OPTIONS = {}
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLogin: false, // 用户是否登录
    questionData: {},
    historyList: [],
    isComplete: false, //是否加载完成
    activeIndex: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    PAGE_OPTIONS = options
    await APP.checkLoadRequest()
    await this.getQuestionDetail()
    this.getHistoryList()
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
  },
  async onShow() {
    if (!this.data.isComplete) return
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
    await this.getQuestionDetail()
    this.getHistoryList()
  },
  goLinkUrl(e) {
    const { id } = e.currentTarget.dataset
    let path = ""
    if (
      this.data.questionData.question_list[0].link_data.type == "question_list"
    ) {
      path = "/pages/situation/subjectList/index"
    } else {
      path = "/pages/situation/sheetDetail/index"
    }
    if (getCurrentPages().length > 6) {
      ROUTER.reLaunch({
        path,
        query: {
          id,
        },
      })
    } else {
      ROUTER.navigateTo({
        path,
        query: {
          id,
        },
      })
    }
  },
  changeTab(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      activeIndex: index,
    })
  },
  // 获取历史作答
  getHistoryList() {
    const param = {
      type: PAGE_OPTIONS.type,
      question_id: PAGE_OPTIONS?.question_id,
      item_no: PAGE_OPTIONS?.item_no || "",
      key: PAGE_OPTIONS.key || "",
      occasion_key: PAGE_OPTIONS.occasion_key || "",
    }

    // const param = {
    //   type: "accumulation",
    //   item_no: 1,
    //   question_id: 11448022,
    // }
    return UTIL.request(API.getRecordHistory, param).then((res) => {
      const resData = res.data
      console.log(resData, "练题历史")
      this.setData({
        historyList: resData.record_history,
      })
    })
  },
  // 放大图片
  checkImg(e) {
    const { url } = e.currentTarget.dataset
    wx.previewImage({
      current: url, // 当前显示图片的链接
      urls: [url], // 需要预览的图片链接列表（即使只有一张图片，也需要放在数组中）
    })
  },
  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  // 去报告页
  goRecordId(e) {
    const { record_id, to_question_id } = e.currentTarget.dataset.item
    ROUTER.navigateTo({
      path: "/pages/question/result/index",
      query: {
        record_id,
        to_question_id,
      },
    })
  },
  // 获取详情
  getQuestionDetail() {
    return UTIL.request(API.getQuestionDetail, {
      question_id: PAGE_OPTIONS.question_id,
      type: PAGE_OPTIONS.type,
      item_no: PAGE_OPTIONS?.item_no || "",
      key: PAGE_OPTIONS.key || "",
      occasion_key: PAGE_OPTIONS.occasion_key || "",
    }).then((res) => {
      const resData = res.data
      if (resData.question_list.length > 0) {
        resData.question_list.forEach((item) => {
          item.reference_answer = parseQuestionHtml(item.reference_answer.dalta)
        })
      }
      this.setData({
        questionData: resData,
        isComplete: true,
      })
    })
  },
  // 收藏
  collectTap() {
    console.log(PAGE_OPTIONS, "12312312312321")
    const param = {
      is_collect: this.data.questionData.is_collect == 0 ? 1 : 0,
      module: "question",
      params: {
        type: PAGE_OPTIONS.type,
        question_id: PAGE_OPTIONS.question_id,
        occasion_key: PAGE_OPTIONS.occasion_key,
        item_no: PAGE_OPTIONS?.item_no,
      },
    }
    return UTIL.request(API.toggleCollect, param).then((res) => {
      if (res.error.code != 0) {
        wx.showToast({
          title: "操作失败",
          icon: "none",
        })
      } else {
        this.setData({
          ["questionData.is_collect"]:
            this.data.questionData.is_collect == 0 ? 1 : 0,
        })
        const title =
          this.data.questionData.is_collect == 1 ? "收藏成功" : "已取消收藏"
        wx.showToast({
          title,
          icon: "none",
        })
      }
      this.setData({
        isLogin: APP.getIsUserLogin(),
      })
    })
  },
  // 去答题
  goQuestion() {
    ROUTER.navigateTo({
      path: "/pages/question/answer-vertical/index",
      query: {
        question_id: PAGE_OPTIONS.question_id,
        type: PAGE_OPTIONS.type,
        key: PAGE_OPTIONS.key || "",
        occasion_key: PAGE_OPTIONS.occasion_key || "",
        item_no: PAGE_OPTIONS.item_no || "",
      },
    })
  },
  getPageShareParams() {
    let query = { ...PAGE_OPTIONS }
    return APP.createShareParams({
      title: this.data?.questionData?.share_info?.title || "",
      imageUrl: this.data?.questionData?.share_info?.image || "screenshot",
      path: "/pages/question/single-detail/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const result = this.getPageShareParams()
    delete result.imageUrl
    result.query = ROUTER.convertPathQuery(result.query)
    return result
  },
})
