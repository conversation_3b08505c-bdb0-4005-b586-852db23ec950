<block wx:if="{{isComplete}}">
  <view wx:if="{{homeData}}" class="{{hideenShow?'oh-hideen':''}}">
    <tabbar-header wx:if="{{show_white || isShowDeafult}}" show_white="{{show_white}}"></tabbar-header>
    <view class="exam-box" bind:tap="toChangeExam" style="top:{{navigationBarHeight}}px"><text>{{cacheStting.exam.name}} ({{cacheStting.province.name}})</text>
      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/header_exam_down_white.png" mode="widthFix" class="icon" />
    </view>
    <view class="home-banner">
      <swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" circular="{{true}}" interval="{{5000}}" bindchange="handleBannerSwiperChange">
        <swiper-item wx:for="{{homeData.home_ad}}" wx:key="index">
          <view class="swiper-item" catch:tap="goMenu" data-item="{{item}}">
            <image style="width: 100%;display: block;" mode="widthFix" src="{{item.img}}"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="custom-indicator" wx:if="{{homeData.home_ad.length>1}}">
        <view wx:for="{{homeData.home_ad}}" wx:key="index" class="indicator-dot {{index === bannerSwiperIndex?'active':''}}"></view>
      </view>

    </view>
    <view class="main-content">
      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/bg_banner.png" wx:if="{{homeData.home_ad.length>0}}" mode="widthFix" class="banner-bg"></image>
      <!-- 分类卡片 -->
      <view class="card-list" wx:if="{{homeData.menu_list.length>0}}">
        <view class="content-c {{homeData.notice.length>0?'':'pb_0'}}">
          <view class="card-list-item" wx:for="{{homeData.menu_list}}" wx:key="index" catchtap="goDetail" data-item="{{item}}">
            <image src="{{item.img}}"></image>
            <text>{{item.title}}</text>
          </view>
        </view>
        <block wx:if="{{homeData.notice.length>0}}">
          <view class="common-tips" catchtap="goNotice">
            <view class="left">
              <image src="{{homeData.notice[0].icon}}"></image>
              {{homeData.notice[0].name}}
            </view>
            <view class="lines">｜</view>
            <view class="top-text text-ellipsis-1">{{homeData.notice[0].desc}}</view>
            <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
          </view>
        </block>
      </view>
      <!-- 轮播 -->
      <view class="banner-swiper" wx:if="{{!!is_show_accumulation_banner&&!isShowDeafult}}">
        <view class="swiper video-bo">
          <view class="swiper-item pr">
            <view class="video-box walkman">
              <!-- <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_video_bg.png" class="video-bg"></image> -->
              <view class="video-content">
                <view class="film">
                  <view class="rotate {{isPlaying?'xuan':''}}">
                    <image class="film-img" src="{{musicFilm}}"></image>
                  </view>
                  <image class="pointer" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_video_pointer.png"></image>
                </view>
                <view class="fonts-box">
                  <view class="left-box">
                    <view class="title" catchtap="openMusicPopu"><text class="text-ellipsis-1">{{musicTitle}}</text>
                      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_arrow_right.png"></image>
                    </view>
                    <view class="label text-ellipsis-1">{{musicLabel}}</view>
                  </view>
                  <view class="right-box" catchtap="changePlay">
                    <image wx:if="{{!isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_video_play.png"></image>
                    <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_pause.png"></image>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- <swiper class="swiper" indicator-dots="{{false}}" autoplay="{{!isPlaying}}" circular="{{true}}" interval="{{15000}}" bindchange="handleBannerSwiperChange">
        <swiper-item wx:for="{{homeData.home_ad}}" wx:key="index">
          <view class="swiper-item pr" wx:if="{{item.cmd_json.type==='accumulation'}}">
            <view class="video-box walkman">
              <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_video_bg.png" class="video-bg"></image>
              <view class="video-content">
                <view class="film">
                  <view class="rotate {{isPlaying?'xuan':''}}">
                    <image class="film-img" src="{{musicFilm}}"></image>
                  </view>
                  <image class="pointer" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_video_pointer.png"></image>
                </view>
                <view class="fonts-box">
                  <view class="left-box">
                    <view class="title" catchtap="openMusicPopu"><text class="text-ellipsis-1">{{musicTitle}}</text>
                      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_arrow_right.png"></image>
                    </view>
                    <view class="label text-ellipsis-1">{{musicLabel}}</view>
                  </view>
                  <view class="right-box" catchtap="changePlay">
                    <image wx:if="{{!isPlaying}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/home_video_play.png"></image>
                    <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/single_pause.png"></image>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="swiper-item" catch:tap="goMenu" data-item="{{item}}" wx:else>
            <image style="height: 156rpx;width: 100%;border-radius: 24rpx;display: block;" src="{{item.img}}"></image>
          </view>
        </swiper-item>
      </swiper> -->

      </view>
      <!-- 单题练习 -->
      <view class="ins-card-content" wx:if="{{exerciseStructureList.length}}">
        <view class="exercise-content">
          <view class="" style="height: 170rpx;">
            <view class="exercise-title">
              单题练习
              <view class="line"></view>
            </view>
          </view>
          <image class="top-bg" mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_top_bg.png"></image>
          <view style="margin-top: -80rpx;">
            <home-structure-collapse isLogin="{{isLogin}}" list="{{exerciseStructureList}}" activeNames="{{exerciseStructureAcitves}}" continueKey="{{ continueKey }}" bind:onChange="onExerciseStructureChange" bind:onRightButton="onRightButton">
            </home-structure-collapse>
          </view>
        </view>
      </view>
      <!-- 套卷练习 -->
      <view class="roll-box" wx:if="{{homeData.home_book_list&&homeData.home_book_list.list.length>0}}">
        <view class="roll-content">
          <view class="roll-top">
            <view class="exercise-title">
              {{homeData.home_book_list.title}}
              <view class="line"></view>
            </view>
            <view class="see-more" catchtap="goMore" wx:if="{{homeData.home_book_list.has_more}}">查看全部<image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow_q.png"></image>
            </view>
            <image class="top-bg" mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/top_bg1.png"></image>
          </view>

          <view class="roll-list">
            <view class="roll-list-item" catchtap="goPractice" data-item="{{item}}" wx:for="{{homeData.home_book_list.list}}" wx:key="index">
              <view class="title text-ellipsis-2">{{item.title}}</view>
              <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
            </view>
          </view>
        </view>
      </view>
      <!-- 底部广告 -->
      <block wx:if="{{homeData.home_bottom_ad.length>0}}">
        <view class="advert-content" wx:for="{{homeData.home_bottom_ad}}" wx:key="index">
          <image class="bottom-advert" mode="widthFix" catch:tap="goDetail" wx:if="{{item.img}}" data-item="{{item}}" src="{{item.img}}"></image>
        </view>
      </block>
      <!-- 浮窗 -->
      <view class="floating" catchtap="goFloat" wx:if="{{homeData.suspension_ad.length>0}}">
        <image mode="widthFix" wx:if="{{homeData.suspension_ad[0].img}}" src="{{homeData.suspension_ad[0].img}}"></image>
      </view>
      <!-- 首页弹窗 -->
      <van-overlay show="{{ showPopup }}" z-index="999" bind:click="onClickHide">
        <view class="wrapper">
          <image class="img-wrap" catchtap="goDetail" data-item="{{homeData.popup_ad[0]}}" src="{{homeData.popup_ad[0].img}}" mode="widthFix"></image>
          <image class="close-yuan" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_close_yuan.png"></image>
        </view>
      </van-overlay>
      <!-- 指令弹窗 -->
      <van-overlay show="{{ cmdPopup }}" z-index="999" bind:click="closePopu">
        <view class="wrapper">
          <image class="img-wrap" src="{{imagePopu}}" show-menu-by-longpress="true" mode="widthFix"></image>
          <image class="close-yuan" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_close_yuan.png"></image>
        </view>
      </van-overlay>
      <tips-default text="内容筹备中..." wx:if="{{isShowDeafult}}" margin_top="100" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png">
      </tips-default>
    </view>
  </view>
  <block wx:else>
    <tabbar-header show_white="{{show_white}}"></tabbar-header>
    <view class="home-banner">
      <swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" circular="{{true}}" interval="{{5000}}" bindchange="handleBannerSwiperChange">
        <block wx:if="{{homeData.home_ad}}">
          <swiper-item wx:for="{{homeData.home_ad}}" wx:key="index">
            <view class="swiper-item" catch:tap="goMenu" data-item="{{item}}">
              <image style="width: 100%;display: block;" mode="widthFix" src="{{item.img}}"></image>
            </view>
          </swiper-item>
        </block>
      </swiper>
      <view class="custom-indicator" wx:if="{{homeData.home_ad.length>1}}">
        <view wx:for="{{homeData.home_ad}}" wx:key="index" class="indicator-dot {{index === bannerSwiperIndex?'active':''}}"></view>
      </view>
    </view>
    <tips-default text="页面加载失败" margin_top="100" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png">
      <view catchtap="refresh" class="empty-button">重试</view>
    </tips-default>
  </block>
</block>

<!-- 底部导航栏 -->
<home-tabbar active="practice" bind:closeHidden="closeHidden" bind:openHideen="openHideen" showAudio="{{false}}" id="home" />

<!-- 弹窗客服 -->
<wechat-popup weichatShow="{{weichatShow}}" info="{{weichatCustomerService}}" bindonClickHide="closeWeichatCustomerService" />