const BASE_CACHE = require("@/utils/cache/baseCache")
export function checkSetupLocaltionBase(exam_province_list, optionsQuery) {
  const { exams, provinces } = organizeExamData(exam_province_list)
  const { province: cacheProvince, exam: cacheExam } =
    BASE_CACHE.getBaseCache() || {}

  let { province: queryProvinceKey, exam: queryExamKey } = optionsQuery

  // 设置默认值
  const defaultProvinceKey = queryProvinceKey || cacheProvince.key
  const defaultExamKey = queryExamKey || cacheExam.key
  const newCache = {}

  // 新用户首次进入时，不做任何处理
  if (!defaultProvinceKey && !defaultExamKey) {
    return false
  }

  // 优先处理校区信息
  if (defaultProvinceKey) {
    const newProvindeData =
      findData(provinces, defaultProvinceKey) || provinces["chongqing"]
    newCache.province = newProvindeData?.data
    newCache.exam =
      newProvindeData.list.find((item) => item.exam_key === defaultExamKey) ||
      newProvindeData.list[0]
  }

  // 处理考试信息
  else if (defaultExamKey) {
    const newExamData =
      findData(exams, defaultExamKey) || Object.values[exams][0]
    newCache.exam = newExamData?.data
    newCache.province =
      newExamData.list.find((item) => item.exam_key === defaultProvinceKey) ||
      newExamData.list[0]
  }

  // 检查是否需要更新缓存
  if (
    newCache.province?.province_key !== cacheProvince?.key ||
    newCache.exam?.exam_key !== cacheExam?.key
  ) {
    return newCache
  }

  return false
}

export function findData(obj = {}, key) {
  let result = obj[key] || {}
  if (!result.data || !result.list?.length) {
    return null
  }
  return result
}

export function organizeExamData(data) {
  const exams = {}
  const provinces = {}

  // 遍历考试类型
  data.forEach((exam) => {
    const simpleExam = { ...exam }
    simpleExam.key = simpleExam.exam_key
    delete simpleExam.province_list
    const examKey = exam.exam_key
    exams[examKey] = { data: simpleExam, list: [] }

    // 构建考试对象
    exams[examKey].list = exam.province_list.map((p) => {
      let provinceKey = p.province_key
      if (!provinces[provinceKey]) {
        provinces[provinceKey] = {}
        provinces[provinceKey].list = []
        provinces[provinceKey].data = p
      }
      provinces[provinceKey].list.push(simpleExam)
      return p
    })
  })

  return { exams, provinces }
}

/**
 * 根据 provinceKey 和 examKey 查询对应数据
 * @param {Array} exam_province_list - 原始数据列表
 * @param {string} [provinceKey] - 省份 key
 * @param {string} [examKey] - 考试 key
 * @returns {Object|null} 匹配的数据项或 null
 */
export function findExamProvinceData(exam_province_list, provinceKey, examKey) {
  if (!exam_province_list?.length || (!provinceKey && !examKey)) {
    return null
  }

  // 利用已有方法构建结构化数据
  const { exams } = organizeExamData(exam_province_list)
  // 如果同时传了两个 key，做精确匹配
  if (provinceKey && examKey) {
    if (!exams) return null
    let examList = exams[examKey]

    const matchExam = examList.list.find(
      (item) => item.province_key === provinceKey
    )
    if (matchExam) {
      return matchExam
    }
  }

  return null
}
