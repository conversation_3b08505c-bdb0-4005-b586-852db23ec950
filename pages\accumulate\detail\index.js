const API = require("@/config/api")
const UTIL = require("@/utils/util")
const { parseQuestionHtml } = require("@/utils/QuestionParseHtml")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
let audioService = null
let PAGE_OPTIONS = {}
Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    type: 3, // 1- 时政  2-热点 3-人物 5-金句
    title: "",
    dataList: [],
    data: null,
    isPlaying: false,
    musciId: "",
    isLogin: false,
    isComplete: false,
  },
  async onLoad(options) {
    PAGE_OPTIONS = options
    await APP.checkLoadRequest()
    const { id } = options
    if (id) {
      this.setData({
        id,
        isLogin: APP.getIsUserLogin(),
      })
      this.getDetailInfo()
    }
    audioService = APP.globalData.audioService
    // 注册状态变化监听器
    this.unwatchPlayer = audioService?.watchPlayer((state) => {
      // console.log("积累详情页播放器状态更新:", state)
      // 直接从state中获取当前播放项的状态信息
      const currentItemState = state
      this.setData({
        isPlaying: currentItemState.isPlaying,
        musciId: currentItemState.id,
        isLogin: APP.getIsUserLogin(),
      })
    })
  },

  async getDetailInfo() {
    const pam = {
      id: this.data.id,
    }
    if (PAGE_OPTIONS.tag_id) {
      pam.tag_id = PAGE_OPTIONS.tag_id
      pam.from = "collect"
    }
    const res = await UTIL.request(API.getCcumulationDetail, pam)
    if (res?.error?.code === 0) {
      const data = res.data
      let arr = data.content || []
      if (arr.length) {
        arr.forEach((item) => {
          item.content = parseQuestionHtml(item.delta)
        })
      }
      this.setData({
        data,
        title: data.title,
        dataList: arr,
        type: data.tag_type,
        isComplete: true,
      })
      console.log(arr, "11111111111")
    }
  },
  async goQuestion() {
    if (this.data.data.question_id === 0) {
      return
    }
    if (!this.data.isLogin) {
      await this.getDetailInfo()
    }
    let url = ""
    if (this.data.data.record_data.is_involved == 0) {
      url = "/pages/question/answer-vertical/index"
    } else {
      url = "/pages/question/single-detail/index"
    }
    ROUTER.navigateTo({
      path: url,
      query: {
        question_id: this.data.data.question_id,
        type: "accumulation",
        item_no: this.data.data.id,
      },
    })
  },
  onShow() {
    if (this.data.isComplete) {
      this.setData({
        isLogin: APP.getIsUserLogin(),
      })
      this.getDetailInfo()
    }
  },
  onUnload() {
    // audioService._setContinue(false)
  },
  changePlay(e) {
    const item = e.currentTarget.dataset.item
    const isPlaying = audioService.getState().isPlaying
    if (this.data.musciId == item.id && isPlaying) {
      audioService.pause()
    } else {
      audioService._setContinue(true)
      audioService.playItemById(item.id, item.tag_type)
    }
  },
  getPageShareParams() {
    let query = { ...PAGE_OPTIONS }
    return APP.createShareParams({
      title: this.data.data.share_info.title || "",
      imageUrl: this.data.data.share_info.image || "",
      path: "/pages/accumulate/detail/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
  goNext(e) {
    const { data } = e.currentTarget.dataset
    let id = null
    if (data.id == data.accum_list[data.accum_list.length - 1]) {
      return
    } else {
      id =
        data.accum_list[
          data.accum_list.findIndex((item) => item == data.id) + 1
        ]
    }
    let query = {
      id,
    }
    if (PAGE_OPTIONS.tag_id) {
      query.tag_id = PAGE_OPTIONS.tag_id
    }
    ROUTER.redirectTo({
      path: "/pages/accumulate/detail/index",
      query,
    })
  },

  // 收藏
  collectTap() {
    if (!this.data.isLogin) {
      this.getDetailInfo()
    }
    const is_collect = this.data.data.is_collect == 0 ? 1 : 0
    const param = {
      is_collect,
      module: "accumulation",
      params: {
        item_no: this.data.data.id,
      },
    }
    return UTIL.request(API.toggleCollect, param).then((res) => {
      if (res.error.code != 0) {
        wx.showToast({
          title: "操作失败",
          icon: "none",
        })
      } else {
        wx.showToast({
          title: is_collect == 1 ? "收藏成功" : "已取消收藏",
          icon: "none",
        })
        this.setData({
          ["data.is_collect"]: this.data.data.is_collect == 0 ? 1 : 0,
          isLogin: APP.getIsUserLogin(),
        })
      }
    })
  },
})
