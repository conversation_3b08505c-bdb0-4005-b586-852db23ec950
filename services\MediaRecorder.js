/**
 * 媒体录制器类，封装微信原生录音/录像功能并集成云存储上传
 */
class MediaRecorder {
  constructor() {
    /**
     * 录制开始时间戳
     * @type {number}
     */
    this.startTimestamp = 0
    this.isForceStopping = false // 新增：强制停止标志
    this.questionRecord = {}

    /**
     * 录制时长定时器
     * @type {NodeJS.Timeout}
     */
    this.durationTimer = null

    /**
     * 当前录制问题ID（用于关联业务逻辑）
     * @type {string|null}
     */
    this.currentQuestionId = null

    /**
     * 微信录音管理器实例
     * @type {RecorderManager|null}
     */
    this.recorder = null

    /**
     * 微信摄像头上下文实例
     * @type {CameraContext|null}
     */
    this.camera = null

    /**
     * 是否启用音频录制
     * @type {boolean}
     */
    this.enableAudio = false

    /**
     * 是否启用视频录制
     * @type {boolean}
     */
    this.enableVideo = false

    /**
     * 音频配置参数
     * @type {object}
     */
    this.audioConfig = {}

    /**
     * 最大录制时长（毫秒）
     * @type {number}
     */
    this.duration = 60000

    /**
     * 是否自动处理权限申请
     * @type {boolean}
     */
    this.autoHandlePermission = true

    this.isPaused = false // 新增：暂停状态
    this.pauseStartTime = 0 // 新增：暂停开始时间
    this.totalPausedDuration = 0 // 新增：累计暂停时间
    /**
     * 录制时长更新回调
     * @type {function|null}
     */
    this.onTimeUpdate = null

    /**
     * 录制超时回调
     * @type {function|null}
     */
    this.onTimeout = null
    this.onResume = null

    this.isRecordPermission = false // 是否开启录音授权
    this.isRCameraPermission = false // 是否开启摄像
  }

  /**
   * 初始化录制器配置
   * @param {object} config 配置参数
   * @param {object} config.cosConfig 云存储配置
   * @param {boolean} [config.enableAudio=true] 是否启用音频
   * @param {boolean} [config.enableVideo=true] 是否启用视频
   * @param {object} [config.audioConfig={format:"mp3",sampleRate:16000}] 音频配置
   * @param {number} [config.duration=60000] 最大时长（毫秒）
   * @param {boolean} [config.autoHandlePermission=true] 是否自动处理权限
   * @param {function} [config.onTimeUpdate] 时长更新回调
   * @param {function} [config.onTimeout] 超时回调
   */
  async init(config) {
    // 解构配置参数并设置默认值
    const {
      enableAudio = true,
      enableVideo = true,
      audioConfig = {
        format: "mp3",
        sampleRate: 16000,
      },
      duration = 60000,
      autoHandlePermission = true,
      onTimeUpdate = null,
      onTimeout = null,
      onError = null,
      onPause = null,
      onResume = null,
    } = config

    if (!enableAudio && !enableVideo) {
      throw new Error("必须启用至少一个录制类型")
    }

    console.log("库 录音初始化", enableAudio)
    // 初始化成员变量
    this.enableAudio = enableAudio
    this.enableVideo = enableVideo
    this.audioConfig = {
      ...audioConfig,
      duration: duration,
    }
    this.duration = duration
    this.autoHandlePermission = autoHandlePermission
    this.onTimeUpdate = onTimeUpdate
    this.onTimeout = onTimeout
    this.onError = onError
    this.onPause = onPause
    this.onResume = onResume

    // 创建微信原生实例
    this.recorder = enableAudio ? wx.getRecorderManager() : null
    this.camera = enableVideo ? wx.createCameraContext() : null

    this.recorder.onStart((res) => {
      console.log("库 录音启动成功", res)
    })
    this.recorder.onStop((res) => {
      console.log("库 监听onStop", res)
      if (this.isForceStopping) {
        return // 跳过后续处理
      }
      if (!this.getQuestionAudio(this.currentQuestionId)?.tempFilePath) {
        this.setQuestionAudio(this.currentQuestionId, res)
        this.onTimeout?.()
      }
    })

    this.recorder.onError((res) => {
      console.log("库 内部监听到错误", res)
      this.onError?.({ audio: res })
    })
    this.recorder.onPause(() => {
      console.log("库 内部监听到暂停")
      this.pauseTime()
      this.onPause?.()
    })
    this.recorder.onResume(() => {
      this.resumeTime()
      this.onResume?.()

      console.log("库 内部监听到暂停恢复播放")
    })
    this.recorder.onInterruptionBegin(() => {
      console.log("库 内部监听到干扰")
      this.recorder.pause()
    })
    this.recorder.onInterruptionEnd(() => {
      console.log("库 内部监听到干扰停止")
      this.recorder.resume()
    })
  }

  setQuestionAudio(questionId, value) {
    const question = this.questionRecord[questionId] || {}
    question.audio = value
    this.questionRecord[questionId] = question
  }
  getQuestionAudio(questionId) {
    return this.questionRecord[questionId]?.audio
  }
  setQuestionVideo(questionId, value) {
    const question = this.questionRecord[questionId] || {}
    question.video = value
    this.questionRecord[questionId] = question
  }
  getQuestionVideo(questionId) {
    return this.questionRecord[questionId]?.video
  }

  /**
   * 开始录制
   * @param {string} questionId 问题ID（用于关联业务逻辑）
   * @returns {Promise<void>} 录制开始的Promise
   */
  async start(questionId) {
    return new Promise(async (resolve, reject) => {
      try {
        // 自动权限检查
        if (this.autoHandlePermission) {
          await this._checkPermissions()
        }

        // 初始化录制状态
        this.currentQuestionId = questionId
        this.startTimestamp = Date.now()
        console.log("库 启动---")

        // 启动录制设备
        this._startRecording(questionId)
          .then((res) => {
            this._startTimer()
            resolve(res)
          })
          .catch((res) => {
            reject(res)
          })
      } catch (error) {
        console.log("库 启动异常", error)
        // 异常处理
        this._handleError("start", error)
        reject(error)
      }
    })
  }
  // 启动录音
  startRecordingAudio(questionId) {
    return new Promise((resolve, reject) => {
      let audioConfig = { ...this.audioConfig }
      console.log("库 启动录音", this.audioConfig)
      this.isForceStopping = false // 重置标志

      if (this.enableAudio) {
        let recorder = this.recorder
        try {
          // 优化录制时间不够的问题 设置3000 可能会再2920结束录制
          audioConfig.duration += 100

          recorder.start(audioConfig)
          resolve()
        } catch (error) {
          console.error("录音启动失败:", error)
          reject(new Error("录音启动失败")) // 抛出明确的错误
        }
      } else {
        resolve()
      }
    })
  }

  // 启动录像
  startRecordingVideo(questionId) {
    return new Promise((resolve, reject) => {
      console.log("库 启动录像", this.enableVideo)
      this.isForceStopping = false // 重置标志

      if (this.enableVideo) {
        if (!this.camera) {
          this.camera = wx.createCameraContext()
        }
        try {
          console.log("相机时长", this.duration / 1000)
          this.camera.startRecord({
            timeout: this.duration / 1000, // 默认是ms 需要转为s
            timeoutCallback: (res) => {
              console.log("相机timeoutCallback", res)
              if (res?.tempVideoPath) {
                this.setQuestionVideo(questionId, res)
              } else {
                this.onError?.({ video: res })
              }
            },
          })
          resolve()
        } catch (error) {
          console.error("录像启动失败:", error)
          reject(new Error("录像启动失败")) // 抛出明确的错误
        }
      } else {
        resolve()
      }
    })
  }

  /**
   * 停止录制并处理结果
   * @returns {Promise<object>} 包含录制结果的Promise
   */
  async stop() {
    // 清除计时相关状态
    clearInterval(this.durationTimer)
    this.isPaused = false
    this.totalPausedDuration = 0
    this.pauseStartTime = 0

    return new Promise((resolve, reject) => {
      console.log("库 触发封装 stop函数")

      // 清除定时器
      clearInterval(this.durationTimer)
      let questionId = this.currentQuestionId
      // 结果容器
      const results = {
        audio: null,
        video: null,
        questionId: questionId,
        duration: Date.now() - this.startTimestamp,
      }

      // 创建停止Promise数组
      const stopPromises = []

      // 处理视频停止
      if (this.enableVideo) {
        let cacheVideo = this.getQuestionVideo(questionId)
        console.log("库 触发视频停止", cacheVideo)
        if (cacheVideo) {
          results.video = cacheVideo
          return
        }
        this.setQuestionVideo(questionId, {})

        // 只有录制中时，才可以暂停
        if (this.camera?._isRecording) {
          console.log("库 触发视频停止1", cacheVideo, this.camera)

          this.camera.stopRecord({
            success: (videoRes) => {
              console.log("视频停止成功 进入", videoRes, this.isForceStopping)
              if (this.isForceStopping) return // 强制停止时不保存
              console.log("视频停止成功 写入", videoRes)
              this.setQuestionVideo(questionId, videoRes)
            },
            fail: (error) => {
              console.log("视频停止失败", error)
              this.onError?.({ video: error })
            },
            complete: (res) => {
              console.log("视频停止完成", res)
            },
          })
        }
      }

      // 处理音频停止
      if (this.enableAudio) {
        stopPromises.push(
          new Promise((res, rej) => {
            let cacheAudio = this.getQuestionAudio(questionId)
            if (cacheAudio) {
              results.audio = cacheAudio
              res()
              return
            }
            this.setQuestionAudio(questionId, {})
            this.recorder.stop()
          })
        )
      }

      // 等待所有停止操作完成
      Promise.all(stopPromises)
        .then(() => resolve(results))
        .catch((error) => reject(error))
    })
  }

  /**
   * 强制停止录制，不触发任何保存逻辑
   */
  forceStop(questionId) {
    this.isForceStopping = true

    // 立即清除状态和数据
    clearInterval(this.durationTimer)
    this.isPaused = false
    this.totalPausedDuration = 0
    this.pauseStartTime = 0

    // 停止录音设备（会触发onStop，但通过标志位过滤）
    if (this.enableAudio && this.recorder) {
      this.recorder.stop()
    }

    // 停止录像设备（直接停止不处理结果）
    if (this.enableVideo && this.camera) {
      this.camera.stopRecord() // 注意：这里使用无回调的停止方式
    }

    // 清空当前问题数据
    if (questionId) {
      this.setQuestionAudio(questionId, null)
      this.setQuestionVideo(questionId, null)
    }
  }
  /**
   * 检查并申请录音权限
   * @returns {Promise<boolean>} 是否已授权
   */
  async checkRecordPermission() {
    let result = this._checkSinglePermission("scope.record")
    this.isRecordPermission = result
    return result
  }

  /**
   * 检查并申请摄像头权限
   * @returns {Promise<boolean>} 是否已授权
   */
  async checkCameraPermission() {
    let result = this._checkSinglePermission("scope.camera")
    this.isRCameraPermission = result
    return result
  }

  async _checkSinglePermission(scope) {
    try {
      // 获取当前权限状态
      const status = await new Promise((resolve) => {
        wx.getSetting({
          success: (res) => resolve(res.authSetting[scope]),
          fail: () => resolve(undefined), // 获取设置失败视为未查询到状态
        })
      })

      // 已授权，直接返回 true
      if (status === true) return true

      // 用户之前已拒绝授权，直接返回 false
      if (status === false) return false

      // 首次请求权限，尝试直接调用授权
      await wx.authorize({ scope })
      return true // 授权成功
    } catch (error) {
      // 授权失败（用户拒绝或系统错误）
      console.error(`权限申请失败: ${scope}`, error)
      return false
    }
  }

  /**
   * 打开系统设置页面进行权限管理
   * @returns {Promise<void>}
   */
  async openPermissionSetting() {
    return new Promise((resolve) => {
      wx.openSetting({
        success: () => resolve(),
        fail: () => resolve(),
      })
    })
  }

  /**
   * 检查并申请必要权限
   * @returns {Promise<void>} 权限检查Promise
   * @private
   */
  async _checkPermissions() {
    const requiredScopes = []
    this.enableAudio && requiredScopes.push("scope.record")
    this.enableVideo && requiredScopes.push("scope.camera")

    let allGranted = true

    for (const scope of requiredScopes) {
      const status = await new Promise((resolve) => {
        wx.getSetting({
          success: (res) => resolve(res.authSetting[scope]),
          fail: () => resolve(false),
        })
      })

      if (!status) {
        allGranted = false
      }
    }

    if (!allGranted) {
      throw new Error("缺少必要的权限，请检查授权设置。")
    }
  }

  /**
   * 启动录音/录像设备
   * @private
   */ async _startRecording(questionId) {
    try {
      // 并行启动音频和视频录制
      const [audioPromise, videoPromise] = [
        this.startRecordingAudio(questionId),
        this.startRecordingVideo(questionId),
      ]

      // 等待两者都完成
      await Promise.all([audioPromise, videoPromise])
    } catch (error) {
      console.error("启动录音或录像失败:", error)
      throw error // 抛出错误
    }
  }
  resumeRecord() {
    this.recorder.resume()
  }

  /**
   * 启动录制计时器
   * @private
   */
  _startTimer() {
    // 清除已有定时器
    clearInterval(this.durationTimer)

    this.durationTimer = setInterval(() => {
      // 计算经过时间（总时间 - 累计暂停时间）
      const elapsed =
        Date.now() - this.startTimestamp - this.totalPausedDuration

      // 触发时间更新回调
      this.onTimeUpdate?.(elapsed, {
        question_id: this.currentQuestionId,
        isPaused: this.isPaused,
      })
    }, 1000)
  }

  /**
   * 暂停计时
   */
  pauseTime() {
    if (!this.isPaused) {
      this.isPaused = true
      this.pauseStartTime = Date.now() // 记录暂停开始时间
      clearInterval(this.durationTimer) // 停止计时器
    }
  }

  /**
   * 恢复计时
   */
  resumeTime() {
    if (this.isPaused) {
      this.isPaused = false
      // 计算本次暂停持续时间并累加
      this.totalPausedDuration += Date.now() - this.pauseStartTime
      // 重新启动计时器
      this._startTimer()
    }
  }

  /**
   * 统一错误处理
   * @param {string} source 错误来源
   * @param {Error} error 错误对象
   * @private
   */
  _handleError(source, error) {
    const errorMap = {
      audio: {
        msg: "麦克风故障",
        code: 1001,
      },
      video: {
        msg: "摄像头故障",
        code: 1002,
      },
      start: {
        msg: "请打开麦克授权",
        code: 2001,
      },
      device: {
        msg: "设备初始化失败",
        code: 2002,
      },
      video_stop: {
        msg: "视频停止失败",
        code: 3001,
      },
      upload: {
        msg: "文件上传失败",
        code: 4001,
      },
      default: {
        msg: "未知错误",
        code: 9999,
      },
    }

    const errorInfo = errorMap[source] || errorMap.default
    console.error(
      `[MediaRecorder] ${source} error: ${errorInfo.msg} (Code: ${errorInfo.code})`,
      error
    )

    // 自动权限处理时显示提示
    if (this.autoHandlePermission) {
      // wx.showToast({
      //   title: errorInfo.msg,
      //   icon: "none",
      //   duration: 3000,
      // })
      console.log("------------")
      console.log(errorInfo.msg)
    }

    // 异常时强制停止录制
    this.stop().catch(() => {})
  }

  /**
   * 动态设置视频录制状态
   * @param {boolean} enableVideo 是否启用视频
   */
  setEnableVideo(enableVideo) {
    this.enableVideo = enableVideo
  }

  /**
   * 销毁录制器释放资源
   */
  destroy() {
    try {
      clearInterval(this.durationTimer) // 清除计时器

      console.log("触发销毁")
      if (this.isRecordPermission) {
        this.recorder?.stop()
      }
      if (this.isRCameraPermission) {
        this.camera?.stop()
      }

      this.recorder?.offStop() // 移除录音回调
      this.camera?.destroy() // 销毁摄像头实例
      this.recorder = null
      this.camera = null
    } catch (error) {
      console.log(error)
    }
  }
}

module.exports = MediaRecorder
