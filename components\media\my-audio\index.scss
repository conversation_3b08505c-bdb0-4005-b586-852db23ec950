.my-audio-box {
  padding: 40rpx;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 50%, #f2f4f7 100%);
  box-shadow: 0rpx 12rpx 16rpx 2rpx rgba(34, 36, 46, 0.05);
  border: 1rpx solid rgba(194, 197, 204, 0.4);
  border-radius: 72rpx 72rpx 72rpx 72rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .btn-img {
    width: 64rpx;
    height: 64rpx;
    display: block;
  }
  .controls {
    // display: flex;
    // align-items: center;
    flex: 1;
    // height: 70px;
    padding: 0 40rpx;
    box-sizing: border-box;
  }

  .progress-bar {
    position: relative;
    width: 100%;
    height: 12rpx;
    background-color: rgba(194, 197, 204, 0.2);
    border-radius: 8rpx;
    touch-action: none; /* 防止默认行为 */
  }

  .progress-filled {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(140deg, #e60000 0%, #ff7272 100%);
    border-radius: 6rpx;
  }

  .progress-handle {
    position: absolute;
    width: 24rpx;
    height: 24rpx;
    background: #ff4141;
    box-shadow: 0rpx 2rpx 8rpx 2rpx rgba(57, 160, 237, 0.2);
    border-radius: 6rpx;
    border-radius: 50%;
    border: 6rpx solid #ffffff;
    top: 50%;
    transform: translate(-10rpx, -50%);
  }
  .time {
    font-size: 24rpx;
    color: #666666;
  }
}
