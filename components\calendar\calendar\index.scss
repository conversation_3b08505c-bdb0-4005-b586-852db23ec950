/* component/calendar/calendar.wxss */

.icon {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACcUlEQVRYhe2WvWtUURDFf8dNVk1ETViMX4WNna22CopELBQUkYASgzFFUPBPsLGSgAqJGDRGBEUsRA0KIthaip2I/4CEoIR8JyP33lldl7fuR1AL9xSP3Xfvmzlz7pl5jyaa+O+hq0NDqDEV8sBGYA3wDZirP4TFhxvFApAD1gPz5TGqF2Xx2pK5puwA9uuPPUA30A48Bd43Ukg2gWpQTHwe2Iax5GRuA6//BoFuwQDQaUF6xaPYBFzCYryXf4yAoAc44We/INjgJvzqGwYwWg2e1Rqzogmt9MwTjgGngBXvgLB8Axh1HyiuiV7BwVoJ1KpAqPqMu70dtFbYNWDC7SqwfoMp0r9BLHbHi2qBa2nDk0A/aAmUF9oscR1pAikJIR4g3RXqUFIoxD0nOLQqAoLjwFlgOuyVKCDGve3KMY54BOqMrBTVDSQON0rgqLfajJCF5IJ7SmdeifBIICgISgQSbUCPYH+9BI4Ag8BidLzY4VXf+V01TmIM9BhU8Gc7gNOCfVn7s0y4N1SuNNuXhbYDDwU3qyX/QUIMm7Eo6HNj7gQueKe8KyfQ4guh2gPAZW+zWdAW4Al1JC+RYjTMBKA3xYpdcZE0O974rlxIXvDeDhv6gF1Kg6UrnrcYqTv5TwwLpi0VFRTd6oTmQB/AckUPrMS5LjpR9MU6n+2rSV7EfRQH1orSwOoC2w0WlQ+XZZ9qs/57CnQFLMnk47A4FdXYx8MYMJl8ECN9hPgSyxdNGM7qC3DLibyt9Ea3rHtmFddK8DzlURvYZ1c5GrA4ueZBr5S8EPp3JjNgyc061QgvsE9gk/4x0+qKN9FEE/8QwHd9qo6ectzgFAAAAABJRU5ErkJggg==");
  background-size: 100% auto;
  width: 32rpx;
  height: 32rpx;
}

.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.swiper {
  transition: height 0.3s;
}

.header-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.today {
  width: 88rpx;
  height: 42rpx;
  background: #f3f4f4;
  border-radius: 22rpx;
  font-size: 24rpx;
  line-height: 42rpx;
  color: #868d8d;
  text-align: center;
  margin-right: 6rpx;
}

.today:active {
  background: #dfdfdf;
  color: #5f6464;
}

.direction-column {
  flex-direction: column;
}

.flex1 {
  flex: 1;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-stretch {
  align-items: stretch;
}

.calendar {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, Roboto,
    "Helvetica Neue", Helvetica, Arial, "Hiragino Sans GB", "Source Han Sans",
    "Noto Sans CJK Sc", "Microsoft YaHei", "Microsoft Jhenghei", sans-serif;
}

.calendar .title {
  padding: 10rpx 16rpx 10rpx 20rpx;
  line-height: 60rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1c2525;
  letter-spacing: 1px;
}

.calendar .title .year-month {
  margin-right: 20rpx;
}

.calendar .title .icon {
  padding: 0 16rpx;
  font-size: 32rpx;
  color: #999;
}

.calendar .title .open {
  background-color: #f6f6f6;
  color: #999;
  font-size: 22rpx;
  line-height: 36rpx;
  border-radius: 18rpx;
  padding: 0 14rpx;
}

.list-open {
  position: relative;
  justify-content: center;
  .bg-color {
    width: 160rpx;
    height: 32rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16rpx 16rpx 0rpx 0rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.fold {
  transform: rotate(0deg);
}

.unfold {
  transform: rotate(180deg);
}

.calendar .calendar-week {
  line-height: 40rpx;
  padding: 0 22rpx;
  font-size: 28rpx;
  color: #999;
}

.calendar .calendar-week .calendar-week-item {
  width: 100rpx;
  text-align: center;
}

.calendar .calendar-main {
  padding: 18rpx 22rpx 0rpx;
  transition: height 0.3s;
  align-content: flex-start;
  overflow: hidden;
}

.calendar .calendar-main .day {
  position: relative;
  width: 100rpx;
  color: rgba(145, 148, 153, 0.6);
  text-align: center;
  height: 82rpx;
}

.calendar .calendar-main .day .bg {
  position: relative;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 26rpx;
  color: #cccccc;
  font-weight: 500;
  .circle-text {
    position: absolute;
    bottom: 6rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 6rpx;
    height: 6rpx;
    background: #c2c5cc;
    border-radius: 50%;
  }
  .today-circle {
    background: #ffffff;
  }
}

// .calendar .calendar-main .day .now {
//   width: 66rpx;
//   border-radius: 50%;
//   text-align: center;
//   color: #0ec0b8;
//   background: rgba(14, 192, 184, 0.2);
//   margin: 0 auto;
// }

.calendar .calendar-main .day .select {
  width: 64rpx;
  border-radius: 12rpx;
  text-align: center;
  color: #fff !important;
  background: #e60000 !important;
  margin: 0 auto;
}

.calendar .calendar-main .day .spot::after {
  position: absolute;
  content: "";
  display: block;
  width: 8rpx;
  height: 8rpx;
  bottom: 22rpx;
  background: #0ec0b8;
  border-radius: 50%;
  left: 0;
  right: 0;
  bottom: 6rpx;
  margin: auto;
}

.calendar .calendar-main .day .deep-spot::after {
  position: absolute;
  content: "";
  display: block;
  width: 8rpx;
  height: 8rpx;
  bottom: 22rpx;
  background: #ff6a4d;
  border-radius: 50%;
  left: 0;
  right: 0;
  bottom: 6rpx;
  margin: auto;
}

.calendar .calendar-main .day .other-month {
  color: #3c3d42;
  background: transparent;
}

.calendar .calendar-main .day .not-show {
  display: none !important;
}

.header-wrap .month {
  font-size: 28rpx;
  color: #929797;
}
