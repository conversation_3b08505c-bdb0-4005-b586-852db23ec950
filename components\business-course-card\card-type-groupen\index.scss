/* pages/course/components/deafultCard/index.wxss */
.course-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 32rpx;
  box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(0, 0, 0, 0.04);

  .fonts-bootom {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .teacher-list {
      display: flex;
      align-items: center;
      .img {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 16rpx;
      }
    }

    .right-box {
      margin-left: auto;
    }
  }
  .label-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16rpx;
    .try-listen {
      display: flex;
      align-items: center;
      font-size: 20rpx;
      color: #ff6a4d;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      .img {
        width: 14rpx;
        height: 16rpx;
        display: block;
        margin-right: 8rpx;
      }
      background: rgba(255, 106, 77, 0.05);
    }
  }
}
