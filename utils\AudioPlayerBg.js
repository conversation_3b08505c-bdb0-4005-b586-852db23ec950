const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()

const EVENT_MAP = {
  onCanplay: "onCanplay",
  onEnded: "onEnded",
  onError: "onError",
  onPause: "onPause",
  onPlay: "onPlay",
  onSeeked: "onSeeked",
  onSeeking: "onSeeking",
  onStop: "onStop",
  onTimeUpdate: "onTimeUpdate",
  onWaiting: "onWaiting",
}

const PLAY_MODES = {
  LOOP_LIST: "loop-list",
  RANDOM: "random",
}

class audioService {
  constructor() {
    if (audioService.instance) return audioService.instance

    this._state = {
      list: [],
      currentIndex: -1,
      currentTime: 0,
      duration: 0,
      playMode: PLAY_MODES.LOOP_LIST,
      randomHistory: [],
      retryCount: 0,
      playStateMap: {}, // 存储每个音乐项的播放状态，键为id
      volume: wx.getStorageSync("playerVolume") || 1,
      isPlaying: false, // 判断是否播放
      id: "",
      tabList: [],
      tagId: "", // 播放分类
      continue: false,
      close: false,
      collection: false,
    }
    this.lastSubmitTime = 0 // 上次提交时间戳
    this.submitInterval = 5000 // 提交间隔，单位毫秒（5秒）
    this.seekNumber = 0
    this.submit_player = true // 用于单次播放行为注册
    this.timer = null // 全局定时器
    this.musicTime = 0 // 全局播放时间
    this.player_record_id = null

    try {
      this.audioContext = wx.getBackgroundAudioManager()
      this.audioContext.volume = this._state.volume
      this.audioContext.onError((res) => {
        console.log("播放器错误-")
      })
    } catch (error) {
      console.error("音频上下文创建失败:", error)
      throw new Error("无法初始化音频播放器")
    }

    this.events = new Map()
    this._initAudioEvents()
    this._initStateUpdates()

    audioService.instance = this
    return this
  }

  //初始化全局定时器
  setTimer() {
    this.timer = setInterval(() => {
      if (this._state.isPlaying && this.player_record_id) {
        this.musicTime++
      }
    }, 1000)
  }

  // 获取播放分类
  async getCcumulationListTag() {
    const res = await UTIL.request(API.getCcumulationListTag, {}, "get")
    if (res?.error?.code === 0) {
      if (res.data.list.length) {
        this._state.tabList = res.data.list
        if (!this._state.tagId) {
          this._state.tagId = this._state.tabList[0].id
        }
      }
    }
  }

  // 获取播放清单
  async getMusicList(id) {
    this._state.tagId = id
    const param = {
      tag_id: id,
      only_video: 1,
      page: 1,
    }
    const res = await UTIL.request(API.getCcumulationList, param, "get")
    if (res?.data?.list.length) {
      let newArr = res.data.list || []
      newArr = newArr.map((item) => {
        if (item.progress_record && item.progress_record.time !== undefined) {
          item.progress = Math.round(
            (item.progress_record.time / Number(item.video_time)) * 100
          )
          item.currentTime = item.progress_record.time
        }
        return item
      })
      await this.setPlaylist(newArr)
    } else {
      console.log("进来了处理tab")
      this._state = {
        list: [],
        currentIndex: -1,
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        playMode: PLAY_MODES.LOOP_LIST,
        randomHistory: [],
        retryCount: 0,
        playStateMap: {}, // 存储每个音乐项的播放状态，键为id
        volume: wx.getStorageSync("playerVolume") || 1,
        id: "",
        tabList: [],
        tagId: "", // 播放分类
        continue: false,
        close: false,
      }
      return
    }
    // this.setPlay(this._state.currentIndex, false)
    // this.setPlayHome(this._state.currentIndex)
  }

  // 改变播放模式
  _setActiveMode(mode) {
    this._state.activeMode = mode
  }

  _setContinue(mode) {
    this._state.continue = mode
  }

  // 随机播放
  _getRandomIndex() {
    const { list } = this._state

    // 获取所有 progress 为 0 的歌曲
    const unplayedSongs = list.filter(
      (item) => this._state.playStateMap[item.id]?.progress === 0
    )

    if (unplayedSongs.length > 0) {
      // 如果存在未播放的歌曲，从中随机选择一首
      const randomIndex = Math.floor(Math.random() * unplayedSongs.length)
      return list.indexOf(unplayedSongs[randomIndex])
    }

    // 如果所有歌曲都已经播放过（progress 都大于 0），则重置所有歌曲的 progress 并重新随机选择
    for (const id in this._state.playStateMap) {
      this._state.playStateMap[id].progress = 0
    }

    // 递归调用自身以获取新的随机索引
    return this._getRandomIndex()
  }

  _initAudioEvents() {
    Object.entries(EVENT_MAP).forEach(([eventName, methodName]) => {
      const audioContext = this.audioContext
      if (!audioContext) return

      if (typeof audioContext[methodName] !== "function") {
        console.error(
          `无法监听 ${eventName}: audioContext.${methodName} 不是函数`
        )
        return
      }

      try {
        audioContext[methodName]((res) => {
          this._handleAudioEvent(eventName, res)
        })
      } catch (error) {
        console.error(`监听 ${eventName} 事件失败:`, error)
      }
    })
  }

  _initStateUpdates() {
    let lastUpdate = 0
    this.audioContext.onTimeUpdate((e) => {
      const now = Date.now()
      if (now - lastUpdate > 200) {
        console.log("走的这里")
        this._updateTimeState()
        lastUpdate = now
      }
    })
  }

  _handleAudioEvent(eventName, eventData) {
    switch (eventName) {
      case "onPlay":
        this._state.isPlaying = true
        this._state.retryCount = 0
        break
      case "onPause":
        this.handPause()
        break
      case "onSeeking":
        break
      case "onStop":
        this._state.isPlaying = false
        const { list, currentIndex } = this._state
        const currentItem = list[currentIndex]
        this._state.close = true
        if (currentItem?.id) {
          this.updateAndEmitState(currentItem.id, false)
        }
        this._state.continue = true
        this.submit_player = true
        console.log(this._state.continue, "停止状态判断")
        break
      case "onEnded":
        this._handlePlayEnd()
        break
      case "onError":
        this._handlePlayError(eventData)
        break
      case "onCanplay":
        this._state.duration = this.audioContext.duration

        break
    }
    this.emit(eventName, eventData)
  }

  handPause() {
    this._state.isPlaying = false
    // 获取当前播放的音乐项
    const currentItem = this._state.list[this._state.currentIndex]
    if (!currentItem || !currentItem.id) {
      console.warn("当前播放项无效或缺少唯一标识符 'id'")
      return
    }
    // 触发状态变化事件，传递更新后的状态
    this.updateAndEmitState(currentItem.id, this._state.isPlaying)
    this.setStateCache()
  }

  setStateCache() {
    // 使用 JSON 序列化和反序列化进行深拷贝
    const stateCopy = JSON.parse(JSON.stringify(this._state))
    wx.setStorageSync("AudioState", stateCopy)
  }

  setState(data) {
    this._state = data
    const { list, currentIndex } = this._state
    const currentItem = list[currentIndex]
    if (!currentItem || !currentItem.id) {
      console.warn("当前播放项无效或缺少唯一标识符 'id'")
      return
    }
    this._state.isPlaying = false
    // console.log("进来处理缓存没有")
    this.updateAndEmitState(currentItem.id, false)
  }

  _handlePlayError(error) {
    this._state.isPlaying = false

    if (this._state.retryCount < 3) {
      this._state.retryCount++
      setTimeout(() => this.audioContext.play(), 1000)
    } else {
      this.emit("error-retry-exceeded", error)
    }
  }

  _handlePlayEnd() {
    this._handlePlaylistEnd()
  }

  _handlePlaylistEnd() {
    const { playMode, list, currentIndex } = this._state
    if (list.length === 0) return
    let newIndex = currentIndex
    switch (playMode) {
      case PLAY_MODES.LOOP_LIST:
        newIndex = (currentIndex + 1) % list.length
        break
      case PLAY_MODES.RANDOM:
        newIndex = this._getRandomIndex()
        break
    }
    this.setMusicProgress()
    this.playPlaylist(newIndex)
  }

  seekTime(time) {
    this.audioContext.seek(Number(time))
    this.seekNumber = 0
  }

  // 提交进度
  async setMusicProgress() {
    const res = await UTIL.request(API.submitPlayTime, {
      accumulation_id: this._state.id,
      time: this._state.currentTime,
      player_record_id: this.player_record_id,
      play_time: this.musicTime,
    })
    console.log({
      accumulation_id: this._state.id,
      time: this._state.currentTime,
      player_record_id: this.player_record_id,
      play_time: this.musicTime,
    })
  }

  // 注册单次播放行为
  async submitPlayerRecord() {
    if (!this._state.id) return
    const res = await UTIL.request(API.submitPlayerRecord, {
      accumulation_id: this._state.id,
    })
    console.log(res)
    this.player_record_id = res?.data?.player_record_id
    this.musicTime = 0
    this.submit_player = false
  }

  _updateTimeState() {
    const { list, currentIndex, playStateMap } = this._state
    const currentItem = list[currentIndex]

    if (!currentItem || !currentItem.id) {
      console.warn("当前播放项无效或缺少唯一标识符 'id'")
      return
    }

    const currentTime = this.audioContext.currentTime
    const duration = this.audioContext.duration || 0

    const progress = Math.round((currentTime / duration) * 100)

    // 更新当前播放项的状态
    playStateMap[currentItem.id] = {
      ...currentItem,
      progress: progress,
      currentTime: currentTime,
      duration: duration,
    }

    // 触发状态变化事件
    this.updateAndEmitState(currentItem.id, this._state.isPlaying)

    // 每5秒提交一次进度
    const now = Date.now()
    if (now - this.lastSubmitTime >= this.submitInterval) {
      this.setMusicProgress()
      this.lastSubmitTime = now // 更新上次提交时间
    }

    // 更新当前播放时间
    this._state.currentTime = currentTime
  }

  convertMusicListToPlayStateMap(musicList) {
    return musicList.reduce((acc, item) => {
      if (!item.id) {
        console.warn("音乐项缺少唯一标识符 'id'", item)
        return acc
      }
      acc[item.id] = {
        ...item, // 包含原始音乐项的所有属性
        // progress: 0, // 播放进度（0-100）
        // currentTime: 0, // 当前播放时间
        duration: 0, // 总时长
      }
      return acc
    }, {})
  }

  watchPlayer(handler) {
    const listener = (state) => handler(state)
    this.on("stateChange", listener)

    // 返回取消监听函数
    return () => {
      this.off("stateChange", listener)
    }
  }

  // 改变播放状态
  setStatus(obj) {
    this._state.id = obj.id
    this._state.isPlaying = obj.isPlaying
  }

  // 设置播放清单
  setPlaylist(list) {
    const newList = Array.isArray(list) ? [...list] : [list]
    const oldList = this._state.list
    this._state.playStateMap = this.convertMusicListToPlayStateMap(list)

    let newIndex = -1

    if (
      this._state.currentIndex >= 0 &&
      this._state.currentIndex < oldList.length
    ) {
      const currentItem = oldList[this._state.currentIndex]
      newIndex = newList.findIndex((item) => item.src === currentItem.src)
    }

    this._state.list = newList
    if (this._state.currentIndex == -1) {
      this._state.currentIndex = newIndex !== -1 ? newIndex : 0 // 默认从第一首开始
    }

    this._state.randomHistory = []
    this.emit("playlistUpdated", newList)
  }

  // 初始化时设置播放数据
  setPlay(index, isSrc = true) {
    console.log(
      "触发了setplay触发了setplay触发了setplay触发了setplay触发了setplay触发了setplay触发了setplay"
    )
    this.audioContext.title = this._state.list[index].title
    const list = this._state.list
    this.audioContext.singer =
      APP?.globalData?.serverConfig?.accumulation_default_config?.summary ||
      "金标尺素材随身听"
    this.audioContext.coverImgUrl = this._state.list[index].tag_square_img
    const targetUrl = list[index].video_url
    if (this.audioContext.src === targetUrl) {
      this.play()
      return
    }

    if (isSrc) {
      console.log("改了这里", this._state.list[index], this._state)
      this.audioContext.src = this._state.list[index].video_url
    }
  }

  setPlayHome(index) {
    this.audioContext.title = this._state.list[index].title
    this.audioContext.singer =
      APP?.globalData?.serverConfig?.accumulation_default_config?.summary ||
      "金标尺素材随身听"
    this.audioContext.coverImgUrl = this._state.list[index].tag_square_img
  }

  // 播放素材
  async playPlaylist(index = 0, id) {
    const { list } = this._state
    console.log(
      "playPlaylistplayPlaylistplayPlaylistplayPlaylistplayPlaylistplayPlaylistplayPlaylist"
    )
    if (this.submit_player) {
      this.submitPlayerRecord()
    }
    if (index < 0 || index >= list.length) {
      this.emit("error", { code: "INVALID_INDEX", message: "无效的曲目索引" })
      return
    }
    this._state.currentIndex = index
    this._state.close = false
    const currentItem = list[index]
    this.setPlay(index, true)
    this.seekNumber = this._state.playStateMap[currentItem.id].currentTime

    console.log(this.seekNumber, "播放设置时间")

    this._state.id = currentItem.id
    this.setStateCache()
  }

  // 通过分类id和素材id进行播放
  async playItemById(itemId, tagId) {
    // 检查 itemId 是否在当前 _state.list 中
    const itemExists = this._state.list.some((item) => item.id === itemId)
    if (!itemExists) {
      await this.getMusicList(tagId)
    }
    this._state.tagId = tagId
    this._state.id = itemId
    const index = this._state.list.findIndex((item) => item.id == itemId)
    this.playPlaylist(index)
  }

  // 关闭播放器
  clearMusic() {
    this._state.close = true
    this.stop()
    this.deafaultState()
  }

  // 初始化播放数据对象
  deafaultState() {
    console.log("重置数据", this._state)
    const { list, playStateMap, tabList } = this._state
    this._state = {
      list: list,
      playStateMap: playStateMap,
      currentIndex: -1,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      playMode: PLAY_MODES.LOOP_LIST,
      randomHistory: [],
      retryCount: 0,
      volume: wx.getStorageSync("playerVolume") || 1,
      id: "",
      tabList: tabList,
      tagId: "", // 播放分类
      continue: false,
      close: false,
    }
    // 触发状态变化事件
    this.emit("stateChange", {
      playStateMap: this._state.playStateMap,
      id: "",
      currentIndex: this._state.currentIndex,
      tagId: this._state.tagId, // 播放分类
      playMode: this._state.playMode,
      title: "",
      isPlaying: false,
      summary: "",
      tag_film_img: "",
    })
    wx.removeStorageSync("AudioState")
  }

  // 播放
  play() {
    console.log(this.audioContext.src, this._state.continue, "进来啦")
    if (this.audioContext.src && !this._state.continue) {
      this.audioContext.play()
      this._state.isPlaying = true
    } else {
      const { currentIndex } = this._state
      // 播放失败时重新设置 src
      this._state.continue = false
      this.playPlaylist(currentIndex)
    }
    this.setStateCache()
  }

  // 暂停
  pause() {
    this.audioContext.pause()
    this._state.isPlaying = false
    // 获取当前播放的音乐项
    const currentItem = this._state.list[this._state.currentIndex]
    if (!currentItem || !currentItem.id) {
      console.warn("当前播放项无效或缺少唯一标识符 'id'")
      return
    }
    // 触发状态变化事件，传递更新后的状态
    this.updateAndEmitState(currentItem.id, this._state.isPlaying)
    this.setStateCache()
  }

  // 停止
  stop() {
    this.audioContext.stop()
    this.setStateCache()
  }

  // 下一首
  next() {
    this.stop()
    const { playMode, list, currentIndex } = this._state

    if (list.length === 0) {
      this.emit("error", { code: "EMPTY_PLAYLIST", message: "播放列表为空" })
      return
    }

    // 检查所有歌曲的进度是否都大于0
    let allProgressGreaterThanZero = true

    for (const id in this._state.playStateMap) {
      if (this._state.playStateMap[id].progress <= 0) {
        allProgressGreaterThanZero = false
        break
      }
    }

    // 如果所有进度都大于0，则重置为0
    if (allProgressGreaterThanZero) {
      for (const id in this._state.playStateMap) {
        this._state.playStateMap[id].progress = 0
      }
    }

    let newIndex = currentIndex

    switch (playMode) {
      case PLAY_MODES.RANDOM:
        // 只有当列表中有超过一首歌时才随机选择下一首
        newIndex = this._getRandomIndex()
        break
      default:
        // 只有当列表中有超过一首歌时才切换到下一首
        newIndex = (currentIndex + 1) % list.length
        break
    }

    // 获取新的当前播放项
    const currentItem = list[newIndex]
    if (!currentItem || !currentItem.id) {
      console.warn("当前播放项无效或缺少唯一标识符 'id'")
      return
    }

    this.playPlaylist(newIndex)
    console.log("下一首", currentItem.id)
    // 更新播放状态并触发状态变化事件
    this.updateAndEmitState(currentItem.id, true)
  }

  // 上一首
  prev() {
    const { list, currentIndex } = this._state
    if (list.length === 0) {
      this.emit("error", { code: "EMPTY_PLAYLIST", message: "播放列表为空" })
      return
    }
    // 检查所有歌曲的进度是否都大于0
    let allProgressGreaterThanZero = true

    for (const id in this._state.playStateMap) {
      if (this._state.playStateMap[id].progress <= 0) {
        allProgressGreaterThanZero = false
        break
      }
    }

    // 如果所有进度都大于0，则重置为0
    if (allProgressGreaterThanZero) {
      for (const id in this._state.playStateMap) {
        this._state.playStateMap[id].progress = 0
      }
    }
    const newIndex = (currentIndex - 1 + list.length) % list.length
    // 获取新的当前播放项
    const currentItem = list[newIndex]
    if (!currentItem || !currentItem.id) {
      console.warn("当前播放项无效或缺少唯一标识符 'id'")
      return
    }
    this.playPlaylist(newIndex)

    // 更新播放状态并触发状态变化事件
    this.updateAndEmitState(currentItem.id, true)
  }

  // 更新状态
  updateAndEmitState(currentItemId, isPlaying) {
    if (!this._state?.playStateMap[currentItemId]) return
    const currentItemState = JSON.parse(
      JSON.stringify(this._state?.playStateMap[currentItemId])
    )
    if (!currentItemState) {
      console.warn("无法找到对应id的播放状态", currentItemId)
      return
    }

    // 更新播放状态
    currentItemState.isPlaying = isPlaying
    currentItemState.currentTime = this.audioContext.currentTime
    currentItemState.duration = this.audioContext.duration || 0
    currentItemState.progress = Math.round(
      (this.audioContext.currentTime / (this.audioContext.duration || 0)) * 100
    )
    // 触发状态变化事件
    this.emit("stateChange", {
      id: currentItemId,
      currentIndex: this._state.currentIndex,
      tagId: this._state.tagId, // 播放分类
      playMode: this._state.playMode,
      playStateMap: this._state.playStateMap,
      ...currentItemState,
    })
  }

  // 改变播放模式
  setPlayMode(mode) {
    if (Object.values(PLAY_MODES).includes(mode)) {
      this._state.playMode = mode
    }
    console.log(this._state.playMode, "改变模式了哦")
  }

  // 设置音量
  setVolume(vol) {
    const volume = Math.min(1, Math.max(0, vol))
    this.audioContext.volume = volume
    this._state.volume = volume
    wx.setStorageSync("playerVolume", volume)
  }

  // 跳转到指定位置
  seek(position) {
    if (typeof position !== "number" || position < 0) {
      this.emit("error", {
        code: "INVALID_SEEK_POSITION",
        message: "无效的播放位置",
      })
      return
    }
    const currentDuration = this._state.duration
    if (position > currentDuration) {
      position = currentDuration
    }
    this.audioContext.seek(position)
    this._state.currentTime = position
  }

  on(event, callback) {
    if (!this.events.has(event)) this.events.set(event, new Set())
    this.events.get(event).add(callback)
  }

  off(event, callback) {
    if (this.events.has(event)) {
      this.events.get(event).delete(callback)
    }
  }

  emit(event, ...args) {
    if (this.events.has(event)) {
      this.events.get(event).forEach((cb) => cb(...args))
    }
  }

  getState(mode) {
    return mode ? { ...this._state[mode] } : { ...this._state }
  }

  destroy() {
    this.audioContext.stop()
    this.audioContext = null
    this.events.clear()
    wx.setStorageSync("playerVolume", this._state.volume)
    audioService.instance = null
  }
}

module.exports = audioService
