<view class="date-choose shrink border-bottom10">
  <view class="weekday">
    <block wx:for-item="weekday" wx:for="{{dateListArray}}" wx:key="index">
      <text class="week">{{weekday}}</text>
    </block>
  </view>
  <swiper class="date-choose-swiper" circular="{{ false }}" indicator-dots="{{false}}" current="{{swiperCurrent}}" bindchange="dateSwiperChange">
    <block wx:for="{{dateList}}" wx:for-item="date" wx:key="date.id">
      <swiper-item class="swiper-item">
        <view class="dateday">
          <block wx:for="{{date.days}}" wx:for-item="day" wx:key="index">
            <view class="day" id="{{day.id}}" bindtap="chooseDate">
              <view class="day-text {{dateCurrentStr==day.id?'active':''}}">{{today==day.id?'今':day.day}}
                <view wx:if="{{classPointObj[day.id]}}" class="circle-text {{ dateCurrentStr==day.id?'reds':''}} {{ today> day.id?'gray':'orange'}}">
                </view>
              </view>
            </view>
          </block>
        </view>
      </swiper-item>
    </block>
  </swiper>
</view>