/* pages/question/question-detail-history/index.wxss */
.answering-process {
  // margin-top: 64rpx;
  .answering-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #3c3d42;
  }
  .answering-process-box {
    margin-top: 32rpx;
    .process-item {
      position: relative;
      padding-bottom: 32rpx;
      .waiting {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #e60000;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-right: 4rpx;
        }
      }
      &:last-child::after {
        display: none;
      }
      .yuan {
        width: 14rpx;
        height: 14rpx;
        border: 4rpx solid #e60000;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 7rpx;
        background: #fff;
        z-index: 2;
      }
      padding-left: 38rpx;
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 1px;
        height: 100%;
        border-left: 1px dashed rgba(57, 160, 237, 0.2);
        left: 10rpx;
        top: 7rpx;
        z-index: 1;
      }

      .time {
        font-size: 24rpx;
        color: #919499;
        margin-bottom: 24rpx;
      }
      .right-box {
        flex: 1;
        background: #fff;
        padding: 24rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        .left-font {
          flex: 1;
          min-width: 0;
          padding-right: 30rpx;
        }
        .right-arrow {
          width: 24rpx;
          height: 24rpx;
        }
        .titles {
          font-size: 28rpx;
          color: #3c3d42;
        }

        .time-all {
          font-size: 24rpx;
          color: #919499;
          text {
            color: #666666;
          }
          margin-right: 26rpx;
        }

        .line {
          width: 2rpx;
          height: 24rpx;
          background: #ebecf0;
        }

        .score-all {
          font-size: 24rpx;
          color: #919499;
          margin-left: 26rpx;
          text {
            color: #e60000;
          }
        }

        .font-bottom {
          display: flex;
          align-items: center;
          margin-top: 20rpx;
        }

        .label {
          font-size: 24rpx;
          color: #919499;
          margin-top: 14rpx;
        }
      }
    }
  }
}

page {
  background: rgba(242, 244, 247, 1);
  box-sizing: border-box;
}

.main-content {
  padding: 32rpx;
  box-sizing: border-box;
}
