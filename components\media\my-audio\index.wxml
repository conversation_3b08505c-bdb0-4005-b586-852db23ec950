<view class="my-audio-box">
  <block wx:if="{{ !isMultiple }}">
    <image class="btn-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/{{showPlay?'video_play':'video_pause'}}.png" bindtap="togglePlay"></image>
  </block>
  <block wx:else>
    <image class="btn-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/{{isPlaying?'video_play':'video_pause'}}.png" bindtap="togglePlay"></image>
  </block>
  <view class="controls">
    <view class="progress-bar" style="touch-action: none;" id="progressBar" catchtouchstart="onTouchStart" catchtouchmove="onTouchMove" catchtouchend="onTouchEnd">
      <view class="progress-filled" style="width:{{progressWidth}}%"></view>
      <view class="progress-handle" style="left: {{progressWidth}}%"></view>
    </view>
  </view>
  <!-- {{currentTime}} / -->
  <view class="time"> {{duration}}</view>
</view>