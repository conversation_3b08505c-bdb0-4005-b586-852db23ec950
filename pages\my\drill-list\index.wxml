<wxs module="utils">
  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };
  function getName(id, arr) {
    var title = ''
    for (var i = 0; i < arr.length; i++) {
      if (arr[i].id == id) {
        title = arr[i].title
      }
    }
    return '暂未参与过' + title
  }
  module.exports = {
    getNum: getNum,
    getName: getName
  };
</wxs>
<navigation-bar back="{{true}}" background="#ffffff">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">我的演练</view>
  </view>
</navigation-bar>
<block wx:if="{{isComplete}}">
  <view class="drill-list">
    <view class="tab-list {{ tabList.length < 3?'pd-style':''}}">
      <view wx:for="{{tabList}}" wx:key="index" class="tab-list-item {{ activeIndex == item.id?'active':'' }}" bind:tap="changeIndex" data-index="{{ item.id }}">
        <text class="text">{{item.title}}</text>
      </view>
    </view>
    <block wx:if="{{isChangeOver}}">
      <view wx:for="{{tabList}}" wx:key="index" class="main-content">
        <scroll-view hidden="{{activeIndex != item.id}}" scroll-y style="{{listHeight}}" bindscrolltolower="onScrollToLower">
          <view class="list-area" wx:if="{{ item.dataList && item.dataList.length }}">
            <view class="list-area-item" wx:for="{{ item.dataList }}" wx:for-item="citem" wx:key="cindex" bind:tap="toSingeQuestion" data-data="{{citem}}">
              <view class="top-area">
                <view class="top-text">{{ citem.created_time }}</view>
                <view class="right">
                  <view wx:if="{{citem.correct_status == 1}}" class="top-text">得分：{{utils.getNum(citem.score)}}分</view>
                  <view wx:else class="{{citem.correct_status == -1?'':'red-text'}} top-text">{{citem.correct_status == -1?'报告生成失败':'报告生成中'}}</view>
                  <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
                </view>
              </view>
              <view class="content-area text-ellipsis-2">{{ citem.title }}</view>
            </view>
          </view>
          <view class="no-data-box" wx:if="{{!item.dataList.length &&  !item.isRequest}}">
            <tips-default text="{{utils.getName(activeIndex,tabList)}}" height="280" width="280" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/my_exercise_no_data.png">
            </tips-default>
          </view>
        </scroll-view>
      </view>
    </block>
  </view>
</block>