const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")

const QUESTION_SETUP_CACHE = require("@/utils/cache/questionSetupCache")
let PAGE_OPTIONS = {}
Page({
  data: {
    show_white: false,
    activeIndex: 0,
    tabListIndex: 1,
    questionData: {},
    questionInfo: {},
    isLogin: false, // 用户是否登录
    isComplete: false,
    historyList: [],
  },
  async onLoad(options) {
    PAGE_OPTIONS = options
    await APP.checkLoadRequest()
    this.setData({
      isLogin: APP.getIsUserLogin(),
      id: PAGE_OPTIONS.id,
    })
    await this.getInfo()
  },
  async getInfo() {
    const res = await UTIL.request(API.getBookLetDetail, { id: this.data.id })
    if (res?.error?.code === 0) {
      this.setData({
        questionInfo: res.data,
      })
      await this.getHistoryList()
      await this.getQuestionDeatail()
    }
  },
  onShow() {
    if (this.data.isComplete) {
      this.setData({
        isLogin: APP.getIsUserLogin(),
      })
      this.getInfo()
    }
  },
  goHistory() {
    ROUTER.navigateTo({
      path: "/pages/question/question-detail-history/index",
      query: PAGE_OPTIONS,
    })
  },
  getQuestionDeatail() {
    return UTIL.request(API.getVoiceQuestionDetail, {
      type: "booklet",
      item_no: this.data.questionInfo.id,
    }).then((res) => {
      const resData = res.data
      this.setData({
        questionData: resData,
        isComplete: true,
      })
    })
  },
  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/practice/home/<USER>",
      })
      return false
    }
    wx.navigateBack()
  },
  changeSetting(e) {
    const data = e.currentTarget.dataset.item
    this.setData({
      activeIndex: data,
    })
  },
  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  goQuestion() {
    if (!this.data.isLogin) {
      this.getInfo()
    }
    let manyMode = this.data.activeIndex === 0 ? "listen" : "look"
    ROUTER.navigateTo({
      path: "/pages/question/answer-vertical/index",
      query: {
        type: "booklet",
        item_no: this.data.questionInfo.id,
        manyMode,
      },
    })
  },
  // 收藏
  collectTap() {
    if (!this.data.isLogin) {
      this.getInfo()
    }
    const is_collect = this.data.questionInfo.is_collect == 0 ? 1 : 0
    const param = {
      is_collect,
      module: "booklet",
      params: {
        item_no: this.data.questionInfo.id,
      },
    }
    return UTIL.request(API.toggleCollect, param).then((res) => {
      if (res.error.code != 0) {
        wx.showToast({
          title: "操作失败",
          icon: "none",
        })
      } else {
        wx.showToast({
          title: is_collect == 1 ? "收藏成功" : "已取消收藏",
          icon: "none",
        })
        this.setData({
          ["questionInfo.is_collect"]:
            this.data.questionInfo.is_collect == 0 ? 1 : 0,
          isLogin: APP.getIsUserLogin(),
        })
      }
    })
  },
  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    let oldIndex = this.data.tabListIndex
    if (oldIndex != index) {
      this.setData({
        tabListIndex: index,
      })
    }
  },
  toSingeQuestion(e) {
    if (!this.data.isLogin) {
      this.getInfo()
    }
    const item = e.detail.item
    let param = PAGE_OPTIONS
    param.question_id = item.id
    param.type = "booklet"
    param.item_no = PAGE_OPTIONS.id
    if (item.record_data.is_involved == 0) {
      ROUTER.navigateTo({
        path: "/pages/question/answer-vertical/index",
        query: param,
      })
    } else {
      ROUTER.navigateTo({
        path: "/pages/question/single-detail/index",
        query: param,
      })
    }
  },
  // 获取历史作答
  getHistoryList() {
    const param = {
      type: "booklet",
      item_no: this.data.questionInfo.id,
    }
    return UTIL.request(API.getRecordHistory, param).then((res) => {
      const resData = res.data
      console.log(resData, "练题历史")
      this.setData({
        historyList: resData.record_history,
      })
    })
  },
  // 去报告页
  goRecordId(e) {
    const record_id = e.currentTarget.dataset.id
    ROUTER.navigateTo({
      path: "/pages/question/result/index",
      query: {
        record_id,
      },
    })
  },

  getPageShareParams() {
    let query = PAGE_OPTIONS
    return APP.createShareParams({
      title: this.data.questionInfo.share_info.title || "",
      imageUrl: this.data.questionInfo.share_info.image || "",
      path: "/pages/situation/sheetDetail/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
