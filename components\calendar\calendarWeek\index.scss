.date-choose {
  background: #fff;
  overflow: hidden;
  height: auto;
}

.data-month {
  width: 100%;
  align-items: center;
  padding: 0.5rem 0.35rem;
  text-align: left;
  color: #333;
  font-size: 36rpx;
}

.date-choose-swiper {
  flex-grow: 1;
  height: 120rpx;
}

.swiper-item {
  display: flex;
  flex-direction: column;
}

.weekday,
.dateday {
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  flex-wrap: wrap;
  flex-grow: 1;
}

.week,
.day {
  width: 14.286%;
  flex-basis: 14.286%;
}

.week {
  color: #c2c5cc;
  font-size: 24rpx;
  font-weight: 500;
}

.day .day-text {
  position: relative;
  color: #22242e;
  font-size: 28rpx;
  font-weight: 500;
  .circle-text {
    position: absolute;
    bottom: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 6rpx;
    height: 6rpx;
    border-radius: 50%;
  }
  .orange {
    background: #ff6a4d;
  }
  .gray {
    background: #c2c5cc;
  }
}

.day .active:before {
  content: "";
  position: absolute;
  width: 76rpx;
  height: 76rpx;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 8rpx;
  background: #e60000;
  z-index: -1;
}

.day .day-text.active {
  color: #fff;
}

.day .reds {
  background: #ffffff !important;
}

/*开始*/

.headerone {
  width: 100%;
  height: auto;
  font-size: 24rpx;
}

.headerone .ra {
  margin-right: 20rpx;
}

.headerone .radio-group {
  margin: 20rpx 0 20rpx 30rpx;
}

.headertwo {
  width: 100%;
  height: auto;
  font-size: 24rpx;
  margin-top: 10rpx;
  margin-bottom: 26rpx;
}

.headertwo .le image {
  width: 70rpx;
  height: 70rpx;
  border-radius: 10px;
  margin-left: 30rpx;
  margin-right: 20rpx;
}

.headertwo .ri {
  flex: 1;
  margin-right: 30rpx;
  border-radius: 6px;
  box-shadow: 0px 1px 6px 0px rgba(198, 198, 198, 0.5);
}

.headertwo .ri .one {
  width: 100%;
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}

.headertwo .ri .one view .jiao {
  margin: 0 16rpx;
  border: 15rpx solid;
  border-color: #ffffff #ffffff #b3b3b3 #ffffff;
}

.xi {
  background: red;
  color: #ffffff;
  padding: 3px 10px;
  border-radius: 6px 0px 0 6px;
}

.headertwo .ri .one view view.jiaos {
  margin: 0 16rpx;
  border: 15rpx solid;
  margin-top: 14rpx;
  border-color: #b3b3b3 #ffffff #ffffff #ffffff;
}

.headertwo .ri .two {
  width: 100%;
  overflow: hidden;
  transition: all 0.5s;
}

.headertwo .ri .two .body {
  width: 100%;
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}
