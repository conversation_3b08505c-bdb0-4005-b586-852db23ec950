<navigation-bar back="{{true}}" background="#ffffff">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">我的收藏</view>
  </view>
</navigation-bar>
<view class="exercise" wx:if="{{isComplete}}">
  <view wx:if="{{examTabs.active}}" class="tab-list">
    <home-exam-tabs list="{{examTabs.list}}" active='{{examTabs.active}}' bind:clickItem="handleExamTabsItemEvent"></home-exam-tabs>
    <view class="tab-mt">
      <home-subject-tabs class="mt38" list="{{subjectTabs.list}}" active='{{subjectTabs.active}}' bind:clickItem="handleSubjectTabsItemEvent"></home-subject-tabs>
    </view>
  </view>
  <view class="no-data-box" wx:else>
    <tips-default width="280" height="280" text="暂未收藏过任何内容..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
  </view>
  <block wx:if="{{examTabs.active}}">
    <block wx:if="{{dataList.length}}">
      <view wx:for="{{dataList}}" wx:key="{{item.key}}" class="main-content">
        <scroll-view hidden="{{listKey != item.key}}" scroll-y style="{{listHeight}}" bindscrolltolower="onScrollToLower">
          <view class="list-area" wx:if="{{ item.value.length }}">
            <block wx:if="{{ examTabs.active === 'single_all' }}">
              <view class="practice-list" wx:for="{{ item.value }}" wx:key="cindex" wx:for-item="citem" bind:tap="goDetail" data-data="{{citem}}">
                <view class="text-ellipsis-3" style="line-height: 48rpx;">
                  {{citem.content}}
                </view>
              </view>
            </block>
            <block wx:if="{{ examTabs.active === 'multiple' }}">
              <view class="question-list" wx:for="{{ item.value }}" wx:key="cindex" wx:for-item="citem" bind:tap="goDetail" data-data="{{citem}}">
                <view class="text-ellipsis-2" style="line-height: 46rpx;">
                  {{citem.title}}
                </view>
              </view>
            </block>
            <block wx:if="{{ examTabs.active === 'accumulation' }}">
              <view class="point-list" wx:for="{{ item.value }}" wx:key="cindex" wx:for-item="citem" bind:tap="goDetail" data-data="{{citem}}">
                <view class="title">{{citem.title}}</view>
                <view class="content text-ellipsis-2">{{citem.summary}}</view>
              </view>
            </block>
          </view>
          <view class="no-data-box" wx:else>
            <tips-default width="280" height="280" text="暂未收藏过任何内容..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
          </view>
        </scroll-view>
      </view>
    </block>
    <view class="no-data-box" wx:else>
      <tips-default width="280" height="280" text="暂未收藏过任何内容..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
    </view>
  </block>
</view>