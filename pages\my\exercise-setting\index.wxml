<navigation-bar back="{{true}}">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">练习设置</view>
  </view>
</navigation-bar>
<view class="exercise-setting">
  <view class="model-list" wx:for="{{ modelList }}" wx:key="index" bind:tap="changeSelect" data-index="{{ index }}">
    <image wx:if="{{ setttingIndex == index }}" class="select-bg-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/practice_selected_bg.png" mode="" />
    <image wx:else class="select-bg-image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/practice_select_bg.png" mode="" />
    <image class="bg-image" src="{{item.bg_url}}" mode="" />
    <view class="right-area">
      <view class="text-area">
        <view class="title">{{ item.title }}</view>
        <view class="desc">{{ item.desc }}</view>
      </view>
      <image wx:if="{{ setttingIndex == index }}" class="select-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/practice_selected.png" mode="" />
      <image class="select-icon" wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/practice_select.png" mode="" />
    </view>
  </view>
</view>