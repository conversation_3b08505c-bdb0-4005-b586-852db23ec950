{"compileType": "miniprogram", "libVersion": "trial", "packOptions": {"ignore": [], "include": []}, "setting": {"useCompilerPlugins": ["sass"], "coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": true, "compileWorklet": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wx8996456fd1b2a886", "projectname": "AI面试"}