.price-text {
  .money {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    font-size: 28rpx;
    color: #e60000;
    text-align: right;

    .coupons {
      background: rgba(255, 106, 77, 0.05);
      color: rgba(255, 106, 77, 1);
      border: 0.5px solid rgba(255, 106, 77, 0.3);
      padding: 2rpx 6rpx;
      border-radius: 6rpx;
    }
    .symbol {
      font-size: 24rpx;
      font-weight: bold;
      margin-left: 4rpx;
    }
    .num {
      font-family: "DINBold";
      font-size: 30rpx;
      color: #e60000;
      letter-spacing: -1rpx;
    }
    .old-num {
      font-weight: normal;
      margin: 0 8rpx;
      color: #919499;
      line-height: 38rpx;
      font-size: 22rpx;
      text-decoration-line: line-through;
    }
  }
  .free-text {
    font-size: 28rpx;
    color: #e60000;
    font-weight: bold;
    text-align: right;
  }
  .study-num {
    font-size: 16rpx;
    color: #c2c5cc;
    text-align: right;
    margin-top: 4rpx;
  }

  .start-study {
    font-size: 28rpx;
    color: #e60000;
    font-weight: 500;
    text-align: right;
    font-weight: bold;
  }
  .buy-text {
    color: #c2c5cc !important;
  }
}
.text-left {
  justify-content: flex-start;
}

.groupon-label {
  display: flex;
  justify-content: center;
  align-items: center;
  width: max-content;
  padding: 0 4rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #fe9854 0%, #ff2f5a 100%);
  border-radius: 6rpx 6rpx 6rpx 6rpx;
  font-size: 16rpx;
  color: #ffffff;
  margin: 0 4rpx;
  transform: translateY(-4rpx);
}

.price-home-text {
  .free-text {
    font-size: 32rpx;
  }
  .money {
    align-items: flex-end;
  }

  .symbol {
    font-size: 28rpx;
  }
  .num {
    font-size: 36rpx !important;
  }
  .groupon-label {
    transform: translateY(-6rpx);
  }
}
