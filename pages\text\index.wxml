<wxs module="utils">
  function formatTime(seconds) {
    var minutes = Math.floor(seconds / 60);
    var secs = Math.floor(seconds % 60);
    return minutes + ':' + (secs < 10 ? '0' : '') + secs;
  }
  module.exports.formatTime = formatTime;
</wxs>
<view class="container" style="padding-top: 200px;">
  <!-- <echarts data="{{data}}" wx:if="{{data}}">
  </echarts> -->
  <view class="main-content">
    <!-- 单题 -->
    <!-- <custom-pop status="{{status}}" open="{{isOpen}}" mirror="{{isMirror}}" listen-count="{{listenCount}}" date-info="{{dateInfo}}" bind:popchange="handlePopChange"></custom-pop> -->
    <!-- 多题 -->
    <many-popu status="{{status}}" open="{{isOpen}}" mirror="{{isMirror}}" listen-count="{{listenCount}}" date-info="{{dateInfo}}" bind:popchange="handlePopChange">
    </many-popu>
    <!-- 确认弹窗 -->
    <confirm-dialog bindonClickHide="onClickHide" bindcancel="handleCacelGroupon" bindconfirm="hideCacelGrouponDialog" show="{{showCacelGrouponDialog}}" title="今日免费体验AI点评次数用完，获取更多次数后继续使用" cancel="取消" confirm="去获取"></confirm-dialog>
    <!-- 规则弹窗 -->
    <rule-dialog bindonClickHide="onClickHide" bindconfirm="hideCacelGrouponDialog" show="{{false}}"></rule-dialog>
    <!-- 播放器 -->
    <view class="" style="margin-top: 20px;">
      <my-audio></my-audio>
    </view>
    <!-- 面试答题首页弹窗 -->
    <face-popu show="{{facPopuShow}}" bindclose="close">
      <view>内容啊实打实大萨达按时阿萨德</view>
    </face-popu>
  </view>
</view>