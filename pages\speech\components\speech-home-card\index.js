// 组件js
Component({
  properties: {
    card: { type: Object, value: {} },
    endTime: {
      type: Number,
      value: 1742537101,
    },
    isLogin: {
      type: Boolean,
      value: false,
    },
  },

  data: {
    timeStr: "00:00:00",
    isTimeEnd: false, //是否计时结束
  },

  observers: {
    "card.start_timestamp": function (endTime) {
      this.startCountdown(endTime)
    },
  },

  lifetimes: {
    attached() {
      this.startCountdown(this.data.card.start_timestamp)
    },
    detached() {
      this.clearTimer()
    },
  },

  methods: {
    startCountdown(targetTime = 0) {
      this.clearTimer()

      const update = () => {
        const now = Date.now() / 1000
        const diff = targetTime - now
        console.log(diff, "12312312312")
        if (diff <= 0) {
          this.setData({ timeStr: "00:00:00", isTimeEnd: true })
          this.triggerEvent("timeEnd", { data: this.data.card })
          this.clearTimer()
          return
        }
        console.log(this.formatTime(diff), "123123123")
        this.setData({
          timeStr: this.formatTime(diff),
          isTimeEnd: false,
        })
      }

      update() // 立即执行一次避免初始延迟
      this.timer = setInterval(update, 1000)
    },

    // formatTime(ms) {
    //   const totalSeconds = Math.floor(ms)
    //   const hours = Math.floor(totalSeconds / 3600)
    //   const minutes = Math.floor((totalSeconds % 3600) / 60)
    //   const seconds = totalSeconds % 60
    //   console.log(hours, "1231231")

    //   const pad = (n) => n.toString().padStart(2, "0")
    //   return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
    // },
    formatTime(ms) {
      const totalSeconds = Math.floor(ms)
      // 计算天数
      const days = Math.floor(totalSeconds / 86400)

      // 计算剩余小时
      const hours = Math.floor((totalSeconds % 86400) / 3600)

      // 计算剩余分钟
      const minutes = Math.floor((totalSeconds % 3600) / 60)

      // 计算剩余秒数
      const seconds = totalSeconds % 60

      console.log(days, "天数") // 日志输出用于调试

      // 补零函数
      const pad = (n) => n.toString().padStart(2, "0")

      if (days > 0) {
        // 如果天数大于0，则返回包含天数的格式化字符串
        return `${days}天${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
      } else {
        // 否则仅返回标准的时间格式
        return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
      }
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    toHistory() {
      this.triggerEvent("onHistoryButton", { data: this.data.card })
    },
    toJoin() {
      this.triggerEvent("onJoinButton", { data: this.data.card })
    },
    goDetail() {
      this.triggerEvent("onDetail", { data: this.data.card })
    },
    joinSpeech(e) {
      console.log(e)
      console.log("dianji")
      this.triggerEvent("joinSpeech", { data: this.data.card })
    },
    openWeichatCustomerService() {
      this.triggerEvent("openWeichat")
    },
  },
})
