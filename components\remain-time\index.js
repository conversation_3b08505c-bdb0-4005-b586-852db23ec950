const ROUTER = require("@/services/routerManager")
const APP = getApp()
Component({
  externalClasses: ["custom-class"], // 定义外部样式类名
  properties: {
    verifyRecord: {
      type: Object,
      value: {},
    },
    isMultiple: {
      type: Boolean,
      value: false,
    },
    elementTop: {
      type: String,
      value: "",
    },
  },
  data: {
    weichatShow: false, // 微信客服弹窗
    weichatCustomerService: false, // 微信客服信息
  },
  methods: {
    // 处理关闭校区微信客服
    closeWeichatCustomerService() {
      this.setData({
        weichatShow: false,
      })
    },
    // 处理打开校区微信客服
    openWeichatCustomerService() {
      const type = this.data.verifyRecord.customer_config.type
      if (type === "share") {
        ROUTER.navigateTo({
          path: "/pages/share/index",
          query: {},
        })
      } else {
        let customerService = this.data.verifyRecord.customer_config
        if (customerService.type == "popup") {
          this.setData({
            weichatCustomerService: customerService,
            weichatShow: true,
          })
          return
        }
        ROUTER.navigateTo({
          path: "/pages/webview/web/index",
          query: {
            url: customerService.url,
          },
        })
      }
    },
  },
})
