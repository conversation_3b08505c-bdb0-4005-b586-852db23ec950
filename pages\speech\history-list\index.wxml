<wxs module="utils2">
  function formatTime(input) {
    // 尝试将输入转换为整数
    var seconds = parseInt(input, 10);

    // 如果秒数为0，直接返回"00:00"
    if (seconds == 0) {
      return "00:00";
    }

    var hours = Math.floor(seconds / 3600);
    var minutes = Math.floor((seconds % 3600) / 60);
    seconds = Math.floor(seconds % 60);

    // 使用padZero确保每部分都是两位数
    var formattedHours = padZero(hours);
    var formattedMinutes = padZero(minutes);
    var formattedSeconds = padZero(seconds);

    // 根据是否有小时来决定返回的格式
    if (hours > 0) {
      // 如果有时，则格式化为 "XX:XX:XX"
      return formattedHours + ":" + formattedMinutes + ":" + formattedSeconds;
    } else {
      // 如果没有小时，则仅格式化为 "XX:XX"
      return formattedMinutes + ":" + formattedSeconds;
    }
  }

  function padZero(number) {
    return number < 10 ? "0" + number : "" + number;
  }

  function handleList(list) {
    // 如果列表长度大于3，则返回前三个元素；否则返回整个列表
    return list.length > 3 ? list.slice(0, 3) : list;
  }

  module.exports = {
    formatTime: formatTime,
    handleList: handleList
  };
</wxs>
<navigation-bar back="{{true}}" isSticky="{{ true }}">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">线下专场</view>
  </view>
</navigation-bar>
<view class="main-content" wx:if="{{isComplete}}">
  <block wx:if="{{drillList.length>0}}">
    <min-card list="{{drillList}}"></min-card>
  </block>
  <tips-default wx:else text="面试PK赛筹备中，敬请期待" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
</view>