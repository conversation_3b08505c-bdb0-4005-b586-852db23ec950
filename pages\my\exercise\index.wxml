<wxs module="utils">
  function getNum(num) {
    var str = num.toString()
    // 检查是否包含小数点
    var dotIndex = str.indexOf('.');
    if (dotIndex !== -1) {
      var len = str.length;
      var lastNonZeroIndex = len;

      // 从后往前遍历，找到第一个非零字符的位置
      for (var i = len - 1; i >= dotIndex; i--) {
        if (str[i] !== '0') {
          lastNonZeroIndex = i;
          break;
        }
      }

      // 如果最后只剩下一个小数点，则也去掉小数点
      if (lastNonZeroIndex === dotIndex) {
        return str.substring(0, dotIndex);
      }

      // 截取到第一个非零字符的位置
      return str.substring(0, lastNonZeroIndex + 1);
    } else {
      // 如果没有小数点，直接返回原字符串
      return str;
    }
  };
  module.exports = {
    getNum: getNum
  };
</wxs>
<view class="page-content">
  <navigation-bar back="{{true}}" background="#ffffff">
    <view slot="center" class="tab-title">
      <view class="text-ellipsis-1">我的练习</view>
    </view>
  </navigation-bar>

  <view class="tab-list {{ tabList.length < 3?'pd-style':''}}">
    <view wx:for="{{tabList}}" wx:key="index" class="tab-list-item {{ activeIndex == item.id?'active':'' }}" bind:tap="changeIndex" data-index="{{ item.id }}">
      <text class="text">{{item.title}}</text>
    </view>
  </view>
  <view wx:if="{{ isChangeOver }}">
    <view wx:for="{{tabList}}" wx:key="index" class="main-content">
      <scroll-view hidden="{{activeIndex != item.id}}" scroll-y style="{{listHeight}}" bindscrolltolower="onScrollToLower">
        <block wx:if="{{ item.dataList.length > 0 }}">
          <view class="exercise-list">
            <view class="exercise-list-item" wx:for="{{ item.dataList }}" wx:for-item="citem" wx:key="cindex" bind:tap="toSingeQuestion" data-data="{{citem}}">
              <view class="top-area">
                <view class="top-text">{{ citem.created_time }}</view>
                <view class="right">
                  <view wx:if="{{citem.correct_status == 1}}" class="top-text">得分：{{utils.getNum(citem.score)}}分</view>
                  <view wx:else class="{{citem.correct_status == -1 || citem.correct_status == -2?'':'red-text'}} top-text">{{citem.correct_status == -1?'报告生成失败':citem.correct_status == -2?'未完成':'报告生成中'}}</view>
                  <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png" mode="" />
                </view>
              </view>
              <view class="content-area text-ellipsis-3">{{ activeIndex == 2?citem.question_content:citem.title }}</view>
            </view>
          </view>
        </block>
        <view class="no-data-box" wx:if="{{!item.dataList.length &&  !item.isRequest}}">
          <tips-default width="280" text="还没练习过题目，去提升一下吧" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/my/my_exercise_no_data.png">
            <view class="no-data-btn" catch:tap="goPractice">去练习</view>
          </tips-default>
        </view>
      </scroll-view>
    </view>
  </view>
</view>