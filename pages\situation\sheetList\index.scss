page {
  background: #f2f4f7;
  box-sizing: border-box;
}

.van-tabs__line {
  display: none !important;
}

// .van-tabs__scroll {
//   background-color: transparent !important;
// }

.sheet-list {
  padding: 32rpx 32rpx 64rpx 32rpx;
  box-sizing: border-box;
  &-item {
    background: #ffffff;
    padding: 32rpx;
    border-radius: 16rpx;
    position: relative;
    margin-bottom: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .title {
      font-size: 30rpx;
      color: #3c3d42;
      line-height: 46rpx;
      font-weight: 500;
      flex: 1;
    }
    .arrow-icon {
      width: 24rpx;
      height: 24rpx;
      margin-left: 32rpx;
    }
    .new {
      top: -14rpx;
      right: -8rpx;
      position: absolute;
      width: 52rpx;
      height: 28rpx;
    }
  }
}
.tab-list-area {
  .van-tab {
    padding: 0 32rpx;
    text-align: left;
    flex: none;
    font-size: 30rpx;
    flex-basis: auto !important;
  }
  .van-tab--active {
    font-weight: bold;
    font-size: 32rpx;
  }
}
