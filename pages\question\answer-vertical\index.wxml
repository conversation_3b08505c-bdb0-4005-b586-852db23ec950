<wxs module="utils">
  // formatTime.wxs
  function formatSeconds(seconds) {
    var secondsNumber = parseInt(seconds, 10);
    if (isNaN(secondsNumber)) {
      return '00:00';
    }
    secondsNumber = Math.max(0, secondsNumber); // 确保非负
    var minutes = Math.floor(secondsNumber / 60);
    var secondsRemaining = secondsNumber % 60;
    // 补零处理
    var minutesStr = minutes < 10 ? '0' + minutes : '' + minutes;
    var secondsStr = secondsRemaining < 10 ? '0' + secondsRemaining : '' + secondsRemaining;
    return minutesStr + ':' + secondsStr;
  }

  module.exports = {
    formatSeconds: formatSeconds
  };
</wxs>

<view class="page" wx:if="{{isCompleted}}">
  <quesiton-navigation-bar back="{{true}}">
    <view slot="left">
      <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common//common_back.png" mode="widthFix" style="width: 40rpx;height: 40rpx;" catch:tap="onBackButton" />
    </view>
    <view slot="center">
      <!-- <text wx:if="{{pageState.useTime>=60}}" class="red-time-text">{{utils.formatSeconds(pageState.useTime)}}（已超时）</text>
      <text wx:else>计时 {{utils.formatSeconds(pageState.useTime)}}</text> -->
      <text>计时 {{utils.formatSeconds(pageState.useTime)}}</text>
    </view>
  </quesiton-navigation-bar>
  <block wx:if="{{questionData}}">
    <!-- 回答结束时显示 -->
    <block wx:if="{{currentAnswerQuestion.state==='complete'}}">
      <anwer-result id="answerResult" answerState="{{currentAnswerQuestion}}" mediaInfo="{{questionCosMap[currentQuestionId]}}" pageState="{{pageState}}" bind:onSubmit="handleSingleSubmit" bind:onRetry="handleRetryAnswer" />
    </block>

    <block wx:else>
      <view class="page-header">
        <!-- 背景视频 -->
        <view class="background-video">
          <video id="backgroundVideo" src="{{questionData.video_info.url}}" loop autoplay="{{true}}" show-center-play-btn="{{false}}" muted="{{isVideoMuted}}" controls="{{false}}" class="video" object-fit="fill" bindtimeupdate="onBackgroundVideoTimeupdate" binderror="onBackgroundVideoError" bindloadedmetadata="onBackgroundVideoLoadedmetadata"></video>

          <view class="video-text" wx:if="{{!pageState.isVideoLoadCompleted}}">面试场景加载中，请稍候...</view>
        </view>
        <view class="top-actionbar">
          <view class="button-group">
            <!-- <view class="button"></view> -->
          </view>

          <!-- 视频上层顶部按钮 -->
          <!-- <view class="button-group">
        <view class="button">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/header_landscape.png" mode="widthFix" class="icon" />
        </view>
        <view class="button">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/header_top.png" mode="widthFix" class="icon" />
        </view>
      </view> -->

          <!-- 视频画面底部遮罩 -->
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/video_bottom_mask.png" mode="widthFix" class="video-bottom-mask" />
        </view>
      </view>
      <view class="page-content {{pageState.isOpenCamera?'skin-dark':''}} {{questionType==='manyQuestion'?'many-question':'single-question'}}">

        <!-- content顶部遮罩 -->
        <view class="content-top-mask"></view>

        <!-- 摄像头组件 -->
        <camera device-position="front" class="background-video" wx:if="{{pageState.isOpenCamera}}" binderror="onCameraError" bindinitdone="onCameraInitDone" />
        <view class="camera-mask" wx:if="{{pageState.isOpenCamera}}"></view>



        <!-- 题干展示 -->
        <view class="content-container">
          <question-rule-stem wx:if="{{mainState==='init'}}" content="{{questionData.video_info.text}}" isDark="{{pageState.isOpenCamera}}" />
          <question-stem id="questionStem" wx:else questionList="{{questionData.question_list}}" question="{{currentAnswerQuestion}}" isDark="{{pageState.isOpenCamera}}" playState="{{questionPlayRecord}}" isRecording="{{isRecording}}" mainState="{{mainState}}" questionType="{{questionType}}" manyMode="{{manyMode}}" bind:onPlayQuestion="handlePlayQuestion" bind:onPauseQuestion="handlePauseQuestion" bind:onMaterialOpen="onMaterialOpen" bind:onImagePreview="previewComponentImage" />
        </view>

        <!-- 提示区域（单题/套题） -->
        <block>

          <!-- 单题提示框 -->
          <block wx:if="{{questionType==='singleQuestion'}}">
            <!-- 思考提示（单题） -->
            <question-answer-toast show="{{dialogStateManager.isTipsThink}}" duration="3000" bind:hide="hideTipsThink" class="question-answer-toast  think-toast">
              <view class="question-answer-toast-content">
                <text class="text">已思考</text>
                <text class="number"> {{questionData.think_allow_time/60}} </text>
                <text class="text">分钟</text>
              </view>
            </question-answer-toast>
          </block>

          <!-- 时间结束提示 -->
          <question-answer-toast show="{{dialogStateManager.isTipsQuestionTimeEnd}}" duration="8000" bind:hide="hideTipsThink" class="question-answer-toast  qeustion-time-toast">
            <view class="question-answer-toast-content">
              <text class="number"> {{ questionData.end_record_time }} </text>
              <text class="text"> 秒后结束录制</text>
            </view>
          </question-answer-toast>

          <!-- 播放题目提示 -->
          <question-answer-toast show="{{dialogStateManager.isPopupQuestionPlayTime}}" duration="3000" bind:hide="hidePopupQuestionPlayTime" class="question-answer-toast  qeustion-time-toast">
            <view class="question-answer-toast-content">
              <text class="number"> {{ questionPlayBeforeTime }} </text>
              <text class="text"> 秒后开始听题</text>
            </view>
          </question-answer-toast>


          <!-- 题目语音加载中提示 -->
          <question-answer-toast show="{{dialogStateManager.isTipsQuestionAudioLoading}}" duration="0" bind:hide="hideTipsQuestionAudioLoading" class="question-answer-toast  qeustion-time-toast">
            <view class="question-answer-toast-content">
              <text class="number"> 题目音频加载中，请稍候...</text>
            </view>
          </question-answer-toast>
        </block>
      </view>
    </block>

    <!-- 作答区域 -->
    <block>
      <!-- 套题模式 -->
      <block wx:if="{{ questionType==='manyQuestion'}}">

        <!-- （套题）准备进入答题状态 -->
        <view class="many-ready-actionbar " wx:if="{{mainState==='init'}}">
          <view class="actionbar-item" catch:tap="switchCamera" style="margin-right: 60rpx;">
            <view class="image-box">
              <image wx:if="{{pageState.isOpenCamera}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_open_whtie.png" mode="widthFix" class="icon" />
              <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_close_black.png" mode="widthFix" class="icon" />
            </view>
            <view class="text">对镜</view>
          </view>
          <view class="actionbar-item" style="flex: 1;">
            <view class="actionbar-button" catch:tap="handleEntryFirstAnswer">{{manyMode==='look'?'开始看题':'开始听题'}}</view>
          </view>
        </view>
        <block wx:else>


          <!-- （套题）准备答题-->
          <view class="many-ready-actionbar " wx:if="{{currentAnswerQuestion.state==='prepare'}}">
            <view class="actionbar-item" catch:tap="switchCamera" style="margin-right: 60rpx;">
              <view class="image-box">
                <image wx:if="{{pageState.isOpenCamera}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_open_whtie.png" mode="widthFix" class="icon" />
                <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_close_black.png" mode="widthFix" class="icon" />
              </view>
              <view class="text">对镜</view>
            </view>
            <view class="actionbar-item" style="flex: 1;">
              <view class="actionbar-button {{(currentAnswerQuestion.prepareTime<=questionData.multiple_allow_time||!currentAnswerQuestion.prepareTime)?'disabled':''}}" catch:tap="handleStartAnswer">开始作答第 {{currentAnswerQuestion.index+1}} 题</view>
            </view>
          </view>

          <!-- （套题）答题中 -->
          <view class="single-answer-actionbar many-answer-actionbar content-container" wx:if="{{currentAnswerQuestion.state==='answer'}}">
            <view class="answer-voice">

              <!-- 作答上传中 -->
              <block wx:if="{{currentAnswerQuestion.isUploading|| pageState.answserUploadState==='uploading'}}">
                <!-- <block wx:if="{{true}}"> -->
                <view style="flex: 1;display: flex;align-items: center;justify-content: center;">
                  <view style="transform: translateY(4rpx);">
                    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_upload_loadding.png" mode=" widthFix" class="upload-loading-icon" />
                  </view>
                  <view class="text-question" style="padding-left: 8rpx;">{{pageState.isOpenCamera?'视频':'音频'}}正在{{pageState.answserUploadState==='uploading'?'上传中':'生成中'}}，请稍候....</view>
                </view>
              </block>
              <block wx:else>
                <view class="line">
                  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_voice_playing.gif" mode="heightFix" class="icon" />
                </view>
                <view class="text-question">第 {{currentAnswerQuestion.index+1}} 题录制中</view>
                <view class="voice-icon">
                  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_make_orange.png" mode="widthFix" style="width: 100%;" />
                </view>
                <block wx:if="{{pageState.isUploadFailed}}">
                  <view class="button" catch:tap="handleRetryUpload">重试</view>
                </block>
                <view wx:else class="button {{questionCosMap[currentQuestionId].audio.time<=questionData.answer_min_time?'disabled':''}}" catch:tap="handleStopButton">完成</view>
              </block>
            </view>
          </view>
        </block>


      </block>

      <!-- 单题模式 -->
      <block wx:if="{{ questionType==='singleQuestion'}}">
        <!-- （单题）答题中 -->
        <view class="single-answer-actionbar content-container" wx:if="{{currentAnswerQuestion.state==='answer'}}">
          <view class="answer-voice">
            <!-- 作答上传中 -->
            <block wx:if="{{currentAnswerQuestion.isUploading|| pageState.answserUploadState==='uploading'}}">
              <!-- <block wx:if="{{true}}"> -->
              <view style="flex: 1;display: flex;align-items: center;justify-content: center;">
                <view style="transform: translateY(4rpx);">
                  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_upload_loadding.png" mode=" widthFix" class="upload-loading-icon" />
                </view>
                <view class="text-question" style="padding-left: 8rpx;">{{pageState.isOpenCamera?'视频':'音频'}}正在{{pageState.answserUploadState==='uploading'?'上传中':'生成中'}}，请稍候....</view>
              </view>
            </block>
            <block wx:else>
              <view class="line">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_voice_playing.gif" mode="heightFix" class="icon" />
              </view>
              <view class="time">{{utils.formatSeconds(questionCosMap[currentQuestionId].audio.time)}}</view>
              <view class="voice-icon">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_make_orange.png" mode="widthFix" style="width: 100%;" />
              </view>


              <block wx:if="{{pageState.isUploadFailed}}">
                <view class="button" catch:tap="handleStopButton">重新上传</view>
              </block>
              <view wx:else class="button {{questionCosMap[currentQuestionId].audio.time<=questionData.answer_min_time?'disabled':''}}" catch:tap="handleStopButton">完成</view>
            </block>

          </view>
        </view>

        <!-- （单题）准备进入答题-->
        <view class="single-ready-actionbar" wx:if="{{currentAnswerQuestion.state==='prepare'}}">
          <view class="actionbar-item small-item" catch:tap="changePopupQuestion" wx:if="{{currentAnswerQuestion.mind_mapping_img}}">
            <view class="image-box">
              <image wx:if="{{dialogStateManager.isPopupQuestion}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_tips_blue.png" mode="widthFix" class="icon" />
              <image wx:elif="{{pageState.isOpenCamera}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_tips_white.png" mode="widthFix" class="icon" />
              <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_tips_black.png" mode="widthFix" class="icon" />
            </view>
            <view class="text">提示</view>
          </view>

          <view wx:else class="actionbar-item small-item" style="background:transparent;">
            <view class="image-box" style="background:transparent;"></view>
          </view>
          <view class="actionbar-item big-item" catch:tap="handleStartAnswer">
            <view class="image-box">
              <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_make_whtie.png" mode="widthFix" class="icon" />
            </view>
            <view class="text">开始作答</view>
          </view>
          <view class="actionbar-item small-item" catch:tap="switchCamera">
            <view class="image-box">
              <block wx:if="{{pageState.isOpenCamera}}">
                <image wx:if="{{!dialogStateManager.isPopupQuestion}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_open_whtie.png" mode="widthFix" class="icon" />
                <image wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_open_black.png" mode="widthFix" class="icon" />
              </block>
              <block wx:else>
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/question/answer_video_close_black.png" mode="widthFix" class="icon" />
              </block>
            </view>
            <view class="text">对镜</view>
          </view>
        </view>
      </block>
    </block>
  </block>
  <!-- 未查到数据 -->
  <block wx:else>
    <tips-default text="未找到题目类容" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png">
      <view class="no-data-btn" catch:tap="toBack">返回</view>
    </tips-default>
  </block>


</view>




<!-- 提示弹窗 -->
<question-popup show="{{dialogStateManager.isPopupQuestion}}" bind:onClose="hidePopupQuestion">
  <view class="tips-popup">
    <view class="tips-popup-content">
      <view class="tips-popup-content-scoll">
        <image src="{{currentAnswerQuestion.mind_mapping_img}}" style="width:100%;" mode="widthFix" data-item="{{currentAnswerQuestion.mind_mapping_img}}" catch:tap="previewImage" />
      </view>
    </view>
    <view class="tips-popup-footer"></view>
  </view>
</question-popup>


<!-- 材料弹窗 -->
<question-popup show="{{dialogStateManager.isPopupMaterial}}" bind:onClose="hidePopupMaterial" zindex="{{500}}">
  <view class="tips-material">
    <view class="tips-material-content">
      <view wx:for="{{currentMaterial}}">
        <question-rich-text customClass="material-rich-text" strText="{{item.text}}"></question-rich-text>
      </view>
    </view>
  </view>
</question-popup>

<!-- 提交确认弹窗 -->
<confirm-dialog show="{{dialogStateManager.isDialogSubmitConfirm}}" cancel="继续答题" title="是否结束作答" subtitle="{{pageState.questionSubmitText}}" bind:confirm="stopRecording" bind:cancel="hideDialogSubmitConfirm" />

<!-- 退出确认弹窗 -->
<confirm-dialog show="{{dialogStateManager.isQuitDialog}}" cancel="继续答题" title="确定退出作答吗？" bind:onClickHide="hideDialogSubmitConfirm" bind:confirm="toBack" bind:cancel="hideQuitDialog" />

<!-- 规则弹窗（仅单题） -->
<rule-dialog show="{{dialogStateManager.isRuleDialog}}" text="{{questionData.single_answer_rule}}" bind:onClickHide="confimRule" />

<!-- 文件上传失败弹窗 -->
<confirm-dialog show="{{dialogStateManager.isDialogUploadFailed}}" wx:if="{{dialogStateManager.isDialogUploadFailed}}" cancel="" confirm="重新上传" title="作答上传失败，请重试" bind:confirm="handleRetryUpload" />

<!-- 文件上传失败弹窗（次数用尽） -->
<confirm-dialog show="{{dialogStateManager.isDialogUploadRetryFailed}}" wx:if="{{dialogStateManager.isDialogUploadRetryFailed}}" cancel="" confirm="返回" title="上传文件失败" subtitle="可返回后重新作答" bind:confirm="toBack" />


<!-- 提交失败弹窗 -->
<confirm-dialog show="{{dialogStateManager.isDialogSubmitFailed}}" wx:if="{{dialogStateManager.isDialogSubmitFailed}}" cancel="" confirm="重新提交" title="作答提交失败，请重试" bind:confirm="handleRetrySubmit" />

<!-- 提交失败弹窗（次数用尽） -->
<confirm-dialog show="{{dialogStateManager.isDialogSubmitRetryFailed}}" wx:if="{{dialogStateManager.isDialogSubmitRetryFailed}}" cancel="" confirm="返回" title="提交失败" subtitle="可返回后重新作答" bind:confirm="toBack" />


<!-- 录制中断（音频） -->
<confirm-dialog show="{{dialogStateManager.isDialogRecordAudioBreak}}" wx:if="{{dialogStateManager.isDialogRecordAudioBreak}}" title="音频录制已停止" subtitle="请选择继续录制或重新录制本题" cancel="重新录制" confirm="继续录制" bind:confirm="resumeRecord" bind:cancel="handleRetryAnswer" />

<!-- 录制中断（视频） -->
<confirm-dialog show="{{dialogStateManager.isDialogRecordVideoBreak}}" wx:if="{{dialogStateManager.isDialogRecordVideoBreak}}" title="视频录制已停止" subtitle="请选择重新录制本题或退出作答" cancel="退出作答" confirm="重新录制" bind:confirm="handleRetryAnswer" bind:cancel="toBack" />



<!-- 录音授权 -->
<confirm-dialog show="{{dialogStateManager.isDialogRecordAuthorize}}" wx:if="{{dialogStateManager.isDialogRecordAuthorize}}" title="您需要授权才能使用录音功能" cancel="关闭" confirm="去授权" bind:confirm="handleRecordAuthorize" bind:onClickHide="hideDialogRecordAuthorize" bind:cancel="hideDialogRecordAuthorize" />

<!-- 录像授权 -->
<confirm-dialog show="{{dialogStateManager.isDialogCameraAuthorize}}" wx:if="{{dialogStateManager.isDialogCameraAuthorize}}" title="您需要授权才能使用摄像功能" cancel="关闭" confirm="去授权" bind:confirm="handleCameraAuthorize" bind:onClickHide="hideDialogCameraAuthorize" bind:cancel="hideDialogCameraAuthorize" />

<!-- 次数达到限制 -->
<confirm-dialog show="{{dialogStateManager.isDialogTriesLimit}}" wx:if="{{dialogStateManager.isDialogTriesLimit}}" title="{{questionData.verify_record.message}}" cancel="返回" confirm="去了解" bind:confirm="handleTriesLimitButton" bind:cancel="toBack" />

<!-- 题目音频播放中断 -->
<confirm-dialog show="{{dialogStateManager.isDialogQuestionPlayError}}" wx:if="{{dialogStateManager.isDialogQuestionPlayError}}" title="音频加载失败，检查网络后重试" cancel="返回" confirm="重试" bind:confirm="handleTryPlayAudioButton" bind:cancel="toBack" />

<!-- 题目音频播放失败 -->
<confirm-dialog show="{{dialogStateManager.isDialogQuestionPlayFailed}}" wx:if="{{dialogStateManager.isDialogQuestionPlayFailed}}" title="音频加载失败，请查看文字题目" cancel="返回" confirm="查看文字题目" bind:confirm="handlePlayAudioFailedButton" bind:cancel="toBack" />


<!-- 图片预览 -->
<single-image-preview id="preview" imageUrl="{{previewImageUrl}}" />