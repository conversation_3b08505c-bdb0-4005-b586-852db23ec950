// components/home-header/index.js
const BASE_CACHE = require("@/utils/cache/baseCache")
const ROUTER = require("@/services/routerManager")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
  },
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: "",
    },
    back: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    cacheStting: {},
  },
  lifetimes: {
    attached() {
      this.setData({ isLoad: true })
      this.checkExam()
    },
    detached() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  pageLifetimes: {
    // 组件所在的页面被展示时执行
    show() {
      this.checkExam()
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 检查考试
    checkExam() {
      if (!this.data.isLoad) {
        return
      }
      this.setData({
        cacheStting: BASE_CACHE.getBaseCache(),
      })

      if (!this.data.cacheStting?.exam?.key) {
        this.toChangeExam()
      }
    },
    toChangeExam() {
      // wx.navigateTo({
      //   url: "/pages/entry/list/index",

      // })
      ROUTER.navigateTo({
        path: "/pages/entry/list/index",
        query: {
          back: this.data.back,
        },
      })
    },
  },
})
