{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/**\r\n * Lodash (Custom Build) <https://lodash.com/>\r\n * Build: `lodash modularize exports=\"npm\" -o ./`\r\n * Copyright JS Foundation and other contributors <https://js.foundation/>\r\n * Released under MIT license <https://lodash.com/license>\r\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\r\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\r\n */\r\n\r\n/** Used as the size to enable large array optimizations. */\r\nvar LARGE_ARRAY_SIZE = 200;\r\n\r\n/** Used to stand-in for `undefined` hash values. */\r\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\r\n\r\n/** Used to compose bitmasks for value comparisons. */\r\nvar COMPARE_PARTIAL_FLAG = 1,\r\n    COMPARE_UNORDERED_FLAG = 2;\r\n\r\n/** Used as references for various `Number` constants. */\r\nvar MAX_SAFE_INTEGER = 9007199254740991;\r\n\r\n/** `Object#toString` result references. */\r\nvar argsTag = '[object Arguments]',\r\n    arrayTag = '[object Array]',\r\n    asyncTag = '[object AsyncFunction]',\r\n    boolTag = '[object Boolean]',\r\n    dateTag = '[object Date]',\r\n    errorTag = '[object Error]',\r\n    funcTag = '[object Function]',\r\n    genTag = '[object GeneratorFunction]',\r\n    mapTag = '[object Map]',\r\n    numberTag = '[object Number]',\r\n    nullTag = '[object Null]',\r\n    objectTag = '[object Object]',\r\n    promiseTag = '[object Promise]',\r\n    proxyTag = '[object Proxy]',\r\n    regexpTag = '[object RegExp]',\r\n    setTag = '[object Set]',\r\n    stringTag = '[object String]',\r\n    symbolTag = '[object Symbol]',\r\n    undefinedTag = '[object Undefined]',\r\n    weakMapTag = '[object WeakMap]';\r\n\r\nvar arrayBufferTag = '[object ArrayBuffer]',\r\n    dataViewTag = '[object DataView]',\r\n    float32Tag = '[object Float32Array]',\r\n    float64Tag = '[object Float64Array]',\r\n    int8Tag = '[object Int8Array]',\r\n    int16Tag = '[object Int16Array]',\r\n    int32Tag = '[object Int32Array]',\r\n    uint8Tag = '[object Uint8Array]',\r\n    uint8ClampedTag = '[object Uint8ClampedArray]',\r\n    uint16Tag = '[object Uint16Array]',\r\n    uint32Tag = '[object Uint32Array]';\r\n\r\n/**\r\n * Used to match `RegExp`\r\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\r\n */\r\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\r\n\r\n/** Used to detect host constructors (Safari). */\r\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\r\n\r\n/** Used to detect unsigned integer values. */\r\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\r\n\r\n/** Used to identify `toStringTag` values of typed arrays. */\r\nvar typedArrayTags = {};\r\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] =\r\ntypedArrayTags[int8Tag] = typedArrayTags[int16Tag] =\r\ntypedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =\r\ntypedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =\r\ntypedArrayTags[uint32Tag] = true;\r\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] =\r\ntypedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =\r\ntypedArrayTags[dataViewTag] = typedArrayTags[dateTag] =\r\ntypedArrayTags[errorTag] = typedArrayTags[funcTag] =\r\ntypedArrayTags[mapTag] = typedArrayTags[numberTag] =\r\ntypedArrayTags[objectTag] = typedArrayTags[regexpTag] =\r\ntypedArrayTags[setTag] = typedArrayTags[stringTag] =\r\ntypedArrayTags[weakMapTag] = false;\r\n\r\n/** Detect free variable `global` from Node.js. */\r\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\r\n\r\n/** Detect free variable `self`. */\r\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\r\n\r\n/** Used as a reference to the global object. */\r\nvar root = freeGlobal || freeSelf || Function('return this')();\r\n\r\n/** Detect free variable `exports`. */\r\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\r\n\r\n/** Detect free variable `module`. */\r\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\r\n\r\n/** Detect the popular CommonJS extension `module.exports`. */\r\nvar moduleExports = freeModule && freeModule.exports === freeExports;\r\n\r\n/** Detect free variable `process` from Node.js. */\r\nvar freeProcess = moduleExports && freeGlobal.process;\r\n\r\n/** Used to access faster Node.js helpers. */\r\nvar nodeUtil = (function() {\r\n  try {\r\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\r\n  } catch (e) {}\r\n}());\r\n\r\n/* Node.js helper references. */\r\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\r\n\r\n/**\r\n * A specialized version of `_.filter` for arrays without support for\r\n * iteratee shorthands.\r\n *\r\n * @private\r\n * @param {Array} [array] The array to iterate over.\r\n * @param {Function} predicate The function invoked per iteration.\r\n * @returns {Array} Returns the new filtered array.\r\n */\r\nfunction arrayFilter(array, predicate) {\r\n  var index = -1,\r\n      length = array == null ? 0 : array.length,\r\n      resIndex = 0,\r\n      result = [];\r\n\r\n  while (++index < length) {\r\n    var value = array[index];\r\n    if (predicate(value, index, array)) {\r\n      result[resIndex++] = value;\r\n    }\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * Appends the elements of `values` to `array`.\r\n *\r\n * @private\r\n * @param {Array} array The array to modify.\r\n * @param {Array} values The values to append.\r\n * @returns {Array} Returns `array`.\r\n */\r\nfunction arrayPush(array, values) {\r\n  var index = -1,\r\n      length = values.length,\r\n      offset = array.length;\r\n\r\n  while (++index < length) {\r\n    array[offset + index] = values[index];\r\n  }\r\n  return array;\r\n}\r\n\r\n/**\r\n * A specialized version of `_.some` for arrays without support for iteratee\r\n * shorthands.\r\n *\r\n * @private\r\n * @param {Array} [array] The array to iterate over.\r\n * @param {Function} predicate The function invoked per iteration.\r\n * @returns {boolean} Returns `true` if any element passes the predicate check,\r\n *  else `false`.\r\n */\r\nfunction arraySome(array, predicate) {\r\n  var index = -1,\r\n      length = array == null ? 0 : array.length;\r\n\r\n  while (++index < length) {\r\n    if (predicate(array[index], index, array)) {\r\n      return true;\r\n    }\r\n  }\r\n  return false;\r\n}\r\n\r\n/**\r\n * The base implementation of `_.times` without support for iteratee shorthands\r\n * or max array length checks.\r\n *\r\n * @private\r\n * @param {number} n The number of times to invoke `iteratee`.\r\n * @param {Function} iteratee The function invoked per iteration.\r\n * @returns {Array} Returns the array of results.\r\n */\r\nfunction baseTimes(n, iteratee) {\r\n  var index = -1,\r\n      result = Array(n);\r\n\r\n  while (++index < n) {\r\n    result[index] = iteratee(index);\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * The base implementation of `_.unary` without support for storing metadata.\r\n *\r\n * @private\r\n * @param {Function} func The function to cap arguments for.\r\n * @returns {Function} Returns the new capped function.\r\n */\r\nfunction baseUnary(func) {\r\n  return function(value) {\r\n    return func(value);\r\n  };\r\n}\r\n\r\n/**\r\n * Checks if a `cache` value for `key` exists.\r\n *\r\n * @private\r\n * @param {Object} cache The cache to query.\r\n * @param {string} key The key of the entry to check.\r\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\r\n */\r\nfunction cacheHas(cache, key) {\r\n  return cache.has(key);\r\n}\r\n\r\n/**\r\n * Gets the value at `key` of `object`.\r\n *\r\n * @private\r\n * @param {Object} [object] The object to query.\r\n * @param {string} key The key of the property to get.\r\n * @returns {*} Returns the property value.\r\n */\r\nfunction getValue(object, key) {\r\n  return object == null ? undefined : object[key];\r\n}\r\n\r\n/**\r\n * Converts `map` to its key-value pairs.\r\n *\r\n * @private\r\n * @param {Object} map The map to convert.\r\n * @returns {Array} Returns the key-value pairs.\r\n */\r\nfunction mapToArray(map) {\r\n  var index = -1,\r\n      result = Array(map.size);\r\n\r\n  map.forEach(function(value, key) {\r\n    result[++index] = [key, value];\r\n  });\r\n  return result;\r\n}\r\n\r\n/**\r\n * Creates a unary function that invokes `func` with its argument transformed.\r\n *\r\n * @private\r\n * @param {Function} func The function to wrap.\r\n * @param {Function} transform The argument transform.\r\n * @returns {Function} Returns the new function.\r\n */\r\nfunction overArg(func, transform) {\r\n  return function(arg) {\r\n    return func(transform(arg));\r\n  };\r\n}\r\n\r\n/**\r\n * Converts `set` to an array of its values.\r\n *\r\n * @private\r\n * @param {Object} set The set to convert.\r\n * @returns {Array} Returns the values.\r\n */\r\nfunction setToArray(set) {\r\n  var index = -1,\r\n      result = Array(set.size);\r\n\r\n  set.forEach(function(value) {\r\n    result[++index] = value;\r\n  });\r\n  return result;\r\n}\r\n\r\n/** Used for built-in method references. */\r\nvar arrayProto = Array.prototype,\r\n    funcProto = Function.prototype,\r\n    objectProto = Object.prototype;\r\n\r\n/** Used to detect overreaching core-js shims. */\r\nvar coreJsData = root['__core-js_shared__'];\r\n\r\n/** Used to resolve the decompiled source of functions. */\r\nvar funcToString = funcProto.toString;\r\n\r\n/** Used to check objects for own properties. */\r\nvar hasOwnProperty = objectProto.hasOwnProperty;\r\n\r\n/** Used to detect methods masquerading as native. */\r\nvar maskSrcKey = (function() {\r\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\r\n  return uid ? ('Symbol(src)_1.' + uid) : '';\r\n}());\r\n\r\n/**\r\n * Used to resolve the\r\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\r\n * of values.\r\n */\r\nvar nativeObjectToString = objectProto.toString;\r\n\r\n/** Used to detect if a method is native. */\r\nvar reIsNative = RegExp('^' +\r\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\r\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\r\n);\r\n\r\n/** Built-in value references. */\r\nvar Buffer = moduleExports ? root.Buffer : undefined,\r\n    Symbol = root.Symbol,\r\n    Uint8Array = root.Uint8Array,\r\n    propertyIsEnumerable = objectProto.propertyIsEnumerable,\r\n    splice = arrayProto.splice,\r\n    symToStringTag = Symbol ? Symbol.toStringTag : undefined;\r\n\r\n/* Built-in method references for those with the same name as other `lodash` methods. */\r\nvar nativeGetSymbols = Object.getOwnPropertySymbols,\r\n    nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,\r\n    nativeKeys = overArg(Object.keys, Object);\r\n\r\n/* Built-in method references that are verified to be native. */\r\nvar DataView = getNative(root, 'DataView'),\r\n    Map = getNative(root, 'Map'),\r\n    Promise = getNative(root, 'Promise'),\r\n    Set = getNative(root, 'Set'),\r\n    WeakMap = getNative(root, 'WeakMap'),\r\n    nativeCreate = getNative(Object, 'create');\r\n\r\n/** Used to detect maps, sets, and weakmaps. */\r\nvar dataViewCtorString = toSource(DataView),\r\n    mapCtorString = toSource(Map),\r\n    promiseCtorString = toSource(Promise),\r\n    setCtorString = toSource(Set),\r\n    weakMapCtorString = toSource(WeakMap);\r\n\r\n/** Used to convert symbols to primitives and strings. */\r\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\r\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\r\n\r\n/**\r\n * Creates a hash object.\r\n *\r\n * @private\r\n * @constructor\r\n * @param {Array} [entries] The key-value pairs to cache.\r\n */\r\nfunction Hash(entries) {\r\n  var index = -1,\r\n      length = entries == null ? 0 : entries.length;\r\n\r\n  this.clear();\r\n  while (++index < length) {\r\n    var entry = entries[index];\r\n    this.set(entry[0], entry[1]);\r\n  }\r\n}\r\n\r\n/**\r\n * Removes all key-value entries from the hash.\r\n *\r\n * @private\r\n * @name clear\r\n * @memberOf Hash\r\n */\r\nfunction hashClear() {\r\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\r\n  this.size = 0;\r\n}\r\n\r\n/**\r\n * Removes `key` and its value from the hash.\r\n *\r\n * @private\r\n * @name delete\r\n * @memberOf Hash\r\n * @param {Object} hash The hash to modify.\r\n * @param {string} key The key of the value to remove.\r\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\r\n */\r\nfunction hashDelete(key) {\r\n  var result = this.has(key) && delete this.__data__[key];\r\n  this.size -= result ? 1 : 0;\r\n  return result;\r\n}\r\n\r\n/**\r\n * Gets the hash value for `key`.\r\n *\r\n * @private\r\n * @name get\r\n * @memberOf Hash\r\n * @param {string} key The key of the value to get.\r\n * @returns {*} Returns the entry value.\r\n */\r\nfunction hashGet(key) {\r\n  var data = this.__data__;\r\n  if (nativeCreate) {\r\n    var result = data[key];\r\n    return result === HASH_UNDEFINED ? undefined : result;\r\n  }\r\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\r\n}\r\n\r\n/**\r\n * Checks if a hash value for `key` exists.\r\n *\r\n * @private\r\n * @name has\r\n * @memberOf Hash\r\n * @param {string} key The key of the entry to check.\r\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\r\n */\r\nfunction hashHas(key) {\r\n  var data = this.__data__;\r\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\r\n}\r\n\r\n/**\r\n * Sets the hash `key` to `value`.\r\n *\r\n * @private\r\n * @name set\r\n * @memberOf Hash\r\n * @param {string} key The key of the value to set.\r\n * @param {*} value The value to set.\r\n * @returns {Object} Returns the hash instance.\r\n */\r\nfunction hashSet(key, value) {\r\n  var data = this.__data__;\r\n  this.size += this.has(key) ? 0 : 1;\r\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\r\n  return this;\r\n}\r\n\r\n// Add methods to `Hash`.\r\nHash.prototype.clear = hashClear;\r\nHash.prototype['delete'] = hashDelete;\r\nHash.prototype.get = hashGet;\r\nHash.prototype.has = hashHas;\r\nHash.prototype.set = hashSet;\r\n\r\n/**\r\n * Creates an list cache object.\r\n *\r\n * @private\r\n * @constructor\r\n * @param {Array} [entries] The key-value pairs to cache.\r\n */\r\nfunction ListCache(entries) {\r\n  var index = -1,\r\n      length = entries == null ? 0 : entries.length;\r\n\r\n  this.clear();\r\n  while (++index < length) {\r\n    var entry = entries[index];\r\n    this.set(entry[0], entry[1]);\r\n  }\r\n}\r\n\r\n/**\r\n * Removes all key-value entries from the list cache.\r\n *\r\n * @private\r\n * @name clear\r\n * @memberOf ListCache\r\n */\r\nfunction listCacheClear() {\r\n  this.__data__ = [];\r\n  this.size = 0;\r\n}\r\n\r\n/**\r\n * Removes `key` and its value from the list cache.\r\n *\r\n * @private\r\n * @name delete\r\n * @memberOf ListCache\r\n * @param {string} key The key of the value to remove.\r\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\r\n */\r\nfunction listCacheDelete(key) {\r\n  var data = this.__data__,\r\n      index = assocIndexOf(data, key);\r\n\r\n  if (index < 0) {\r\n    return false;\r\n  }\r\n  var lastIndex = data.length - 1;\r\n  if (index == lastIndex) {\r\n    data.pop();\r\n  } else {\r\n    splice.call(data, index, 1);\r\n  }\r\n  --this.size;\r\n  return true;\r\n}\r\n\r\n/**\r\n * Gets the list cache value for `key`.\r\n *\r\n * @private\r\n * @name get\r\n * @memberOf ListCache\r\n * @param {string} key The key of the value to get.\r\n * @returns {*} Returns the entry value.\r\n */\r\nfunction listCacheGet(key) {\r\n  var data = this.__data__,\r\n      index = assocIndexOf(data, key);\r\n\r\n  return index < 0 ? undefined : data[index][1];\r\n}\r\n\r\n/**\r\n * Checks if a list cache value for `key` exists.\r\n *\r\n * @private\r\n * @name has\r\n * @memberOf ListCache\r\n * @param {string} key The key of the entry to check.\r\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\r\n */\r\nfunction listCacheHas(key) {\r\n  return assocIndexOf(this.__data__, key) > -1;\r\n}\r\n\r\n/**\r\n * Sets the list cache `key` to `value`.\r\n *\r\n * @private\r\n * @name set\r\n * @memberOf ListCache\r\n * @param {string} key The key of the value to set.\r\n * @param {*} value The value to set.\r\n * @returns {Object} Returns the list cache instance.\r\n */\r\nfunction listCacheSet(key, value) {\r\n  var data = this.__data__,\r\n      index = assocIndexOf(data, key);\r\n\r\n  if (index < 0) {\r\n    ++this.size;\r\n    data.push([key, value]);\r\n  } else {\r\n    data[index][1] = value;\r\n  }\r\n  return this;\r\n}\r\n\r\n// Add methods to `ListCache`.\r\nListCache.prototype.clear = listCacheClear;\r\nListCache.prototype['delete'] = listCacheDelete;\r\nListCache.prototype.get = listCacheGet;\r\nListCache.prototype.has = listCacheHas;\r\nListCache.prototype.set = listCacheSet;\r\n\r\n/**\r\n * Creates a map cache object to store key-value pairs.\r\n *\r\n * @private\r\n * @constructor\r\n * @param {Array} [entries] The key-value pairs to cache.\r\n */\r\nfunction MapCache(entries) {\r\n  var index = -1,\r\n      length = entries == null ? 0 : entries.length;\r\n\r\n  this.clear();\r\n  while (++index < length) {\r\n    var entry = entries[index];\r\n    this.set(entry[0], entry[1]);\r\n  }\r\n}\r\n\r\n/**\r\n * Removes all key-value entries from the map.\r\n *\r\n * @private\r\n * @name clear\r\n * @memberOf MapCache\r\n */\r\nfunction mapCacheClear() {\r\n  this.size = 0;\r\n  this.__data__ = {\r\n    'hash': new Hash,\r\n    'map': new (Map || ListCache),\r\n    'string': new Hash\r\n  };\r\n}\r\n\r\n/**\r\n * Removes `key` and its value from the map.\r\n *\r\n * @private\r\n * @name delete\r\n * @memberOf MapCache\r\n * @param {string} key The key of the value to remove.\r\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\r\n */\r\nfunction mapCacheDelete(key) {\r\n  var result = getMapData(this, key)['delete'](key);\r\n  this.size -= result ? 1 : 0;\r\n  return result;\r\n}\r\n\r\n/**\r\n * Gets the map value for `key`.\r\n *\r\n * @private\r\n * @name get\r\n * @memberOf MapCache\r\n * @param {string} key The key of the value to get.\r\n * @returns {*} Returns the entry value.\r\n */\r\nfunction mapCacheGet(key) {\r\n  return getMapData(this, key).get(key);\r\n}\r\n\r\n/**\r\n * Checks if a map value for `key` exists.\r\n *\r\n * @private\r\n * @name has\r\n * @memberOf MapCache\r\n * @param {string} key The key of the entry to check.\r\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\r\n */\r\nfunction mapCacheHas(key) {\r\n  return getMapData(this, key).has(key);\r\n}\r\n\r\n/**\r\n * Sets the map `key` to `value`.\r\n *\r\n * @private\r\n * @name set\r\n * @memberOf MapCache\r\n * @param {string} key The key of the value to set.\r\n * @param {*} value The value to set.\r\n * @returns {Object} Returns the map cache instance.\r\n */\r\nfunction mapCacheSet(key, value) {\r\n  var data = getMapData(this, key),\r\n      size = data.size;\r\n\r\n  data.set(key, value);\r\n  this.size += data.size == size ? 0 : 1;\r\n  return this;\r\n}\r\n\r\n// Add methods to `MapCache`.\r\nMapCache.prototype.clear = mapCacheClear;\r\nMapCache.prototype['delete'] = mapCacheDelete;\r\nMapCache.prototype.get = mapCacheGet;\r\nMapCache.prototype.has = mapCacheHas;\r\nMapCache.prototype.set = mapCacheSet;\r\n\r\n/**\r\n *\r\n * Creates an array cache object to store unique values.\r\n *\r\n * @private\r\n * @constructor\r\n * @param {Array} [values] The values to cache.\r\n */\r\nfunction SetCache(values) {\r\n  var index = -1,\r\n      length = values == null ? 0 : values.length;\r\n\r\n  this.__data__ = new MapCache;\r\n  while (++index < length) {\r\n    this.add(values[index]);\r\n  }\r\n}\r\n\r\n/**\r\n * Adds `value` to the array cache.\r\n *\r\n * @private\r\n * @name add\r\n * @memberOf SetCache\r\n * @alias push\r\n * @param {*} value The value to cache.\r\n * @returns {Object} Returns the cache instance.\r\n */\r\nfunction setCacheAdd(value) {\r\n  this.__data__.set(value, HASH_UNDEFINED);\r\n  return this;\r\n}\r\n\r\n/**\r\n * Checks if `value` is in the array cache.\r\n *\r\n * @private\r\n * @name has\r\n * @memberOf SetCache\r\n * @param {*} value The value to search for.\r\n * @returns {number} Returns `true` if `value` is found, else `false`.\r\n */\r\nfunction setCacheHas(value) {\r\n  return this.__data__.has(value);\r\n}\r\n\r\n// Add methods to `SetCache`.\r\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\r\nSetCache.prototype.has = setCacheHas;\r\n\r\n/**\r\n * Creates a stack cache object to store key-value pairs.\r\n *\r\n * @private\r\n * @constructor\r\n * @param {Array} [entries] The key-value pairs to cache.\r\n */\r\nfunction Stack(entries) {\r\n  var data = this.__data__ = new ListCache(entries);\r\n  this.size = data.size;\r\n}\r\n\r\n/**\r\n * Removes all key-value entries from the stack.\r\n *\r\n * @private\r\n * @name clear\r\n * @memberOf Stack\r\n */\r\nfunction stackClear() {\r\n  this.__data__ = new ListCache;\r\n  this.size = 0;\r\n}\r\n\r\n/**\r\n * Removes `key` and its value from the stack.\r\n *\r\n * @private\r\n * @name delete\r\n * @memberOf Stack\r\n * @param {string} key The key of the value to remove.\r\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\r\n */\r\nfunction stackDelete(key) {\r\n  var data = this.__data__,\r\n      result = data['delete'](key);\r\n\r\n  this.size = data.size;\r\n  return result;\r\n}\r\n\r\n/**\r\n * Gets the stack value for `key`.\r\n *\r\n * @private\r\n * @name get\r\n * @memberOf Stack\r\n * @param {string} key The key of the value to get.\r\n * @returns {*} Returns the entry value.\r\n */\r\nfunction stackGet(key) {\r\n  return this.__data__.get(key);\r\n}\r\n\r\n/**\r\n * Checks if a stack value for `key` exists.\r\n *\r\n * @private\r\n * @name has\r\n * @memberOf Stack\r\n * @param {string} key The key of the entry to check.\r\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\r\n */\r\nfunction stackHas(key) {\r\n  return this.__data__.has(key);\r\n}\r\n\r\n/**\r\n * Sets the stack `key` to `value`.\r\n *\r\n * @private\r\n * @name set\r\n * @memberOf Stack\r\n * @param {string} key The key of the value to set.\r\n * @param {*} value The value to set.\r\n * @returns {Object} Returns the stack cache instance.\r\n */\r\nfunction stackSet(key, value) {\r\n  var data = this.__data__;\r\n  if (data instanceof ListCache) {\r\n    var pairs = data.__data__;\r\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\r\n      pairs.push([key, value]);\r\n      this.size = ++data.size;\r\n      return this;\r\n    }\r\n    data = this.__data__ = new MapCache(pairs);\r\n  }\r\n  data.set(key, value);\r\n  this.size = data.size;\r\n  return this;\r\n}\r\n\r\n// Add methods to `Stack`.\r\nStack.prototype.clear = stackClear;\r\nStack.prototype['delete'] = stackDelete;\r\nStack.prototype.get = stackGet;\r\nStack.prototype.has = stackHas;\r\nStack.prototype.set = stackSet;\r\n\r\n/**\r\n * Creates an array of the enumerable property names of the array-like `value`.\r\n *\r\n * @private\r\n * @param {*} value The value to query.\r\n * @param {boolean} inherited Specify returning inherited property names.\r\n * @returns {Array} Returns the array of property names.\r\n */\r\nfunction arrayLikeKeys(value, inherited) {\r\n  var isArr = isArray(value),\r\n      isArg = !isArr && isArguments(value),\r\n      isBuff = !isArr && !isArg && isBuffer(value),\r\n      isType = !isArr && !isArg && !isBuff && isTypedArray(value),\r\n      skipIndexes = isArr || isArg || isBuff || isType,\r\n      result = skipIndexes ? baseTimes(value.length, String) : [],\r\n      length = result.length;\r\n\r\n  for (var key in value) {\r\n    if ((inherited || hasOwnProperty.call(value, key)) &&\r\n        !(skipIndexes && (\r\n           // Safari 9 has enumerable `arguments.length` in strict mode.\r\n           key == 'length' ||\r\n           // Node.js 0.10 has enumerable non-index properties on buffers.\r\n           (isBuff && (key == 'offset' || key == 'parent')) ||\r\n           // PhantomJS 2 has enumerable non-index properties on typed arrays.\r\n           (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||\r\n           // Skip index properties.\r\n           isIndex(key, length)\r\n        ))) {\r\n      result.push(key);\r\n    }\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * Gets the index at which the `key` is found in `array` of key-value pairs.\r\n *\r\n * @private\r\n * @param {Array} array The array to inspect.\r\n * @param {*} key The key to search for.\r\n * @returns {number} Returns the index of the matched value, else `-1`.\r\n */\r\nfunction assocIndexOf(array, key) {\r\n  var length = array.length;\r\n  while (length--) {\r\n    if (eq(array[length][0], key)) {\r\n      return length;\r\n    }\r\n  }\r\n  return -1;\r\n}\r\n\r\n/**\r\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\r\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\r\n * symbols of `object`.\r\n *\r\n * @private\r\n * @param {Object} object The object to query.\r\n * @param {Function} keysFunc The function to get the keys of `object`.\r\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\r\n * @returns {Array} Returns the array of property names and symbols.\r\n */\r\nfunction baseGetAllKeys(object, keysFunc, symbolsFunc) {\r\n  var result = keysFunc(object);\r\n  return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\r\n}\r\n\r\n/**\r\n * The base implementation of `getTag` without fallbacks for buggy environments.\r\n *\r\n * @private\r\n * @param {*} value The value to query.\r\n * @returns {string} Returns the `toStringTag`.\r\n */\r\nfunction baseGetTag(value) {\r\n  if (value == null) {\r\n    return value === undefined ? undefinedTag : nullTag;\r\n  }\r\n  return (symToStringTag && symToStringTag in Object(value))\r\n    ? getRawTag(value)\r\n    : objectToString(value);\r\n}\r\n\r\n/**\r\n * The base implementation of `_.isArguments`.\r\n *\r\n * @private\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\r\n */\r\nfunction baseIsArguments(value) {\r\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\r\n}\r\n\r\n/**\r\n * The base implementation of `_.isEqual` which supports partial comparisons\r\n * and tracks traversed objects.\r\n *\r\n * @private\r\n * @param {*} value The value to compare.\r\n * @param {*} other The other value to compare.\r\n * @param {boolean} bitmask The bitmask flags.\r\n *  1 - Unordered comparison\r\n *  2 - Partial comparison\r\n * @param {Function} [customizer] The function to customize comparisons.\r\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\r\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\r\n */\r\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\r\n  if (value === other) {\r\n    return true;\r\n  }\r\n  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {\r\n    return value !== value && other !== other;\r\n  }\r\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\r\n}\r\n\r\n/**\r\n * A specialized version of `baseIsEqual` for arrays and objects which performs\r\n * deep comparisons and tracks traversed objects enabling objects with circular\r\n * references to be compared.\r\n *\r\n * @private\r\n * @param {Object} object The object to compare.\r\n * @param {Object} other The other object to compare.\r\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\r\n * @param {Function} customizer The function to customize comparisons.\r\n * @param {Function} equalFunc The function to determine equivalents of values.\r\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\r\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\r\n */\r\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\r\n  var objIsArr = isArray(object),\r\n      othIsArr = isArray(other),\r\n      objTag = objIsArr ? arrayTag : getTag(object),\r\n      othTag = othIsArr ? arrayTag : getTag(other);\r\n\r\n  objTag = objTag == argsTag ? objectTag : objTag;\r\n  othTag = othTag == argsTag ? objectTag : othTag;\r\n\r\n  var objIsObj = objTag == objectTag,\r\n      othIsObj = othTag == objectTag,\r\n      isSameTag = objTag == othTag;\r\n\r\n  if (isSameTag && isBuffer(object)) {\r\n    if (!isBuffer(other)) {\r\n      return false;\r\n    }\r\n    objIsArr = true;\r\n    objIsObj = false;\r\n  }\r\n  if (isSameTag && !objIsObj) {\r\n    stack || (stack = new Stack);\r\n    return (objIsArr || isTypedArray(object))\r\n      ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)\r\n      : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\r\n  }\r\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\r\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\r\n        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\r\n\r\n    if (objIsWrapped || othIsWrapped) {\r\n      var objUnwrapped = objIsWrapped ? object.value() : object,\r\n          othUnwrapped = othIsWrapped ? other.value() : other;\r\n\r\n      stack || (stack = new Stack);\r\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\r\n    }\r\n  }\r\n  if (!isSameTag) {\r\n    return false;\r\n  }\r\n  stack || (stack = new Stack);\r\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\r\n}\r\n\r\n/**\r\n * The base implementation of `_.isNative` without bad shim checks.\r\n *\r\n * @private\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a native function,\r\n *  else `false`.\r\n */\r\nfunction baseIsNative(value) {\r\n  if (!isObject(value) || isMasked(value)) {\r\n    return false;\r\n  }\r\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\r\n  return pattern.test(toSource(value));\r\n}\r\n\r\n/**\r\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\r\n *\r\n * @private\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\r\n */\r\nfunction baseIsTypedArray(value) {\r\n  return isObjectLike(value) &&\r\n    isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\r\n}\r\n\r\n/**\r\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\r\n *\r\n * @private\r\n * @param {Object} object The object to query.\r\n * @returns {Array} Returns the array of property names.\r\n */\r\nfunction baseKeys(object) {\r\n  if (!isPrototype(object)) {\r\n    return nativeKeys(object);\r\n  }\r\n  var result = [];\r\n  for (var key in Object(object)) {\r\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\r\n      result.push(key);\r\n    }\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * A specialized version of `baseIsEqualDeep` for arrays with support for\r\n * partial deep comparisons.\r\n *\r\n * @private\r\n * @param {Array} array The array to compare.\r\n * @param {Array} other The other array to compare.\r\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\r\n * @param {Function} customizer The function to customize comparisons.\r\n * @param {Function} equalFunc The function to determine equivalents of values.\r\n * @param {Object} stack Tracks traversed `array` and `other` objects.\r\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\r\n */\r\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\r\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\r\n      arrLength = array.length,\r\n      othLength = other.length;\r\n\r\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\r\n    return false;\r\n  }\r\n  // Assume cyclic values are equal.\r\n  var stacked = stack.get(array);\r\n  if (stacked && stack.get(other)) {\r\n    return stacked == other;\r\n  }\r\n  var index = -1,\r\n      result = true,\r\n      seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;\r\n\r\n  stack.set(array, other);\r\n  stack.set(other, array);\r\n\r\n  // Ignore non-index properties.\r\n  while (++index < arrLength) {\r\n    var arrValue = array[index],\r\n        othValue = other[index];\r\n\r\n    if (customizer) {\r\n      var compared = isPartial\r\n        ? customizer(othValue, arrValue, index, other, array, stack)\r\n        : customizer(arrValue, othValue, index, array, other, stack);\r\n    }\r\n    if (compared !== undefined) {\r\n      if (compared) {\r\n        continue;\r\n      }\r\n      result = false;\r\n      break;\r\n    }\r\n    // Recursively compare arrays (susceptible to call stack limits).\r\n    if (seen) {\r\n      if (!arraySome(other, function(othValue, othIndex) {\r\n            if (!cacheHas(seen, othIndex) &&\r\n                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\r\n              return seen.push(othIndex);\r\n            }\r\n          })) {\r\n        result = false;\r\n        break;\r\n      }\r\n    } else if (!(\r\n          arrValue === othValue ||\r\n            equalFunc(arrValue, othValue, bitmask, customizer, stack)\r\n        )) {\r\n      result = false;\r\n      break;\r\n    }\r\n  }\r\n  stack['delete'](array);\r\n  stack['delete'](other);\r\n  return result;\r\n}\r\n\r\n/**\r\n * A specialized version of `baseIsEqualDeep` for comparing objects of\r\n * the same `toStringTag`.\r\n *\r\n * **Note:** This function only supports comparing values with tags of\r\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\r\n *\r\n * @private\r\n * @param {Object} object The object to compare.\r\n * @param {Object} other The other object to compare.\r\n * @param {string} tag The `toStringTag` of the objects to compare.\r\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\r\n * @param {Function} customizer The function to customize comparisons.\r\n * @param {Function} equalFunc The function to determine equivalents of values.\r\n * @param {Object} stack Tracks traversed `object` and `other` objects.\r\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\r\n */\r\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\r\n  switch (tag) {\r\n    case dataViewTag:\r\n      if ((object.byteLength != other.byteLength) ||\r\n          (object.byteOffset != other.byteOffset)) {\r\n        return false;\r\n      }\r\n      object = object.buffer;\r\n      other = other.buffer;\r\n\r\n    case arrayBufferTag:\r\n      if ((object.byteLength != other.byteLength) ||\r\n          !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\r\n        return false;\r\n      }\r\n      return true;\r\n\r\n    case boolTag:\r\n    case dateTag:\r\n    case numberTag:\r\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\r\n      // Invalid dates are coerced to `NaN`.\r\n      return eq(+object, +other);\r\n\r\n    case errorTag:\r\n      return object.name == other.name && object.message == other.message;\r\n\r\n    case regexpTag:\r\n    case stringTag:\r\n      // Coerce regexes to strings and treat strings, primitives and objects,\r\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\r\n      // for more details.\r\n      return object == (other + '');\r\n\r\n    case mapTag:\r\n      var convert = mapToArray;\r\n\r\n    case setTag:\r\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\r\n      convert || (convert = setToArray);\r\n\r\n      if (object.size != other.size && !isPartial) {\r\n        return false;\r\n      }\r\n      // Assume cyclic values are equal.\r\n      var stacked = stack.get(object);\r\n      if (stacked) {\r\n        return stacked == other;\r\n      }\r\n      bitmask |= COMPARE_UNORDERED_FLAG;\r\n\r\n      // Recursively compare objects (susceptible to call stack limits).\r\n      stack.set(object, other);\r\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\r\n      stack['delete'](object);\r\n      return result;\r\n\r\n    case symbolTag:\r\n      if (symbolValueOf) {\r\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\r\n      }\r\n  }\r\n  return false;\r\n}\r\n\r\n/**\r\n * A specialized version of `baseIsEqualDeep` for objects with support for\r\n * partial deep comparisons.\r\n *\r\n * @private\r\n * @param {Object} object The object to compare.\r\n * @param {Object} other The other object to compare.\r\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\r\n * @param {Function} customizer The function to customize comparisons.\r\n * @param {Function} equalFunc The function to determine equivalents of values.\r\n * @param {Object} stack Tracks traversed `object` and `other` objects.\r\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\r\n */\r\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\r\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\r\n      objProps = getAllKeys(object),\r\n      objLength = objProps.length,\r\n      othProps = getAllKeys(other),\r\n      othLength = othProps.length;\r\n\r\n  if (objLength != othLength && !isPartial) {\r\n    return false;\r\n  }\r\n  var index = objLength;\r\n  while (index--) {\r\n    var key = objProps[index];\r\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\r\n      return false;\r\n    }\r\n  }\r\n  // Assume cyclic values are equal.\r\n  var stacked = stack.get(object);\r\n  if (stacked && stack.get(other)) {\r\n    return stacked == other;\r\n  }\r\n  var result = true;\r\n  stack.set(object, other);\r\n  stack.set(other, object);\r\n\r\n  var skipCtor = isPartial;\r\n  while (++index < objLength) {\r\n    key = objProps[index];\r\n    var objValue = object[key],\r\n        othValue = other[key];\r\n\r\n    if (customizer) {\r\n      var compared = isPartial\r\n        ? customizer(othValue, objValue, key, other, object, stack)\r\n        : customizer(objValue, othValue, key, object, other, stack);\r\n    }\r\n    // Recursively compare objects (susceptible to call stack limits).\r\n    if (!(compared === undefined\r\n          ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))\r\n          : compared\r\n        )) {\r\n      result = false;\r\n      break;\r\n    }\r\n    skipCtor || (skipCtor = key == 'constructor');\r\n  }\r\n  if (result && !skipCtor) {\r\n    var objCtor = object.constructor,\r\n        othCtor = other.constructor;\r\n\r\n    // Non `Object` object instances with different constructors are not equal.\r\n    if (objCtor != othCtor &&\r\n        ('constructor' in object && 'constructor' in other) &&\r\n        !(typeof objCtor == 'function' && objCtor instanceof objCtor &&\r\n          typeof othCtor == 'function' && othCtor instanceof othCtor)) {\r\n      result = false;\r\n    }\r\n  }\r\n  stack['delete'](object);\r\n  stack['delete'](other);\r\n  return result;\r\n}\r\n\r\n/**\r\n * Creates an array of own enumerable property names and symbols of `object`.\r\n *\r\n * @private\r\n * @param {Object} object The object to query.\r\n * @returns {Array} Returns the array of property names and symbols.\r\n */\r\nfunction getAllKeys(object) {\r\n  return baseGetAllKeys(object, keys, getSymbols);\r\n}\r\n\r\n/**\r\n * Gets the data for `map`.\r\n *\r\n * @private\r\n * @param {Object} map The map to query.\r\n * @param {string} key The reference key.\r\n * @returns {*} Returns the map data.\r\n */\r\nfunction getMapData(map, key) {\r\n  var data = map.__data__;\r\n  return isKeyable(key)\r\n    ? data[typeof key == 'string' ? 'string' : 'hash']\r\n    : data.map;\r\n}\r\n\r\n/**\r\n * Gets the native function at `key` of `object`.\r\n *\r\n * @private\r\n * @param {Object} object The object to query.\r\n * @param {string} key The key of the method to get.\r\n * @returns {*} Returns the function if it's native, else `undefined`.\r\n */\r\nfunction getNative(object, key) {\r\n  var value = getValue(object, key);\r\n  return baseIsNative(value) ? value : undefined;\r\n}\r\n\r\n/**\r\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\r\n *\r\n * @private\r\n * @param {*} value The value to query.\r\n * @returns {string} Returns the raw `toStringTag`.\r\n */\r\nfunction getRawTag(value) {\r\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\r\n      tag = value[symToStringTag];\r\n\r\n  try {\r\n    value[symToStringTag] = undefined;\r\n    var unmasked = true;\r\n  } catch (e) {}\r\n\r\n  var result = nativeObjectToString.call(value);\r\n  if (unmasked) {\r\n    if (isOwn) {\r\n      value[symToStringTag] = tag;\r\n    } else {\r\n      delete value[symToStringTag];\r\n    }\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * Creates an array of the own enumerable symbols of `object`.\r\n *\r\n * @private\r\n * @param {Object} object The object to query.\r\n * @returns {Array} Returns the array of symbols.\r\n */\r\nvar getSymbols = !nativeGetSymbols ? stubArray : function(object) {\r\n  if (object == null) {\r\n    return [];\r\n  }\r\n  object = Object(object);\r\n  return arrayFilter(nativeGetSymbols(object), function(symbol) {\r\n    return propertyIsEnumerable.call(object, symbol);\r\n  });\r\n};\r\n\r\n/**\r\n * Gets the `toStringTag` of `value`.\r\n *\r\n * @private\r\n * @param {*} value The value to query.\r\n * @returns {string} Returns the `toStringTag`.\r\n */\r\nvar getTag = baseGetTag;\r\n\r\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\r\nif ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||\r\n    (Map && getTag(new Map) != mapTag) ||\r\n    (Promise && getTag(Promise.resolve()) != promiseTag) ||\r\n    (Set && getTag(new Set) != setTag) ||\r\n    (WeakMap && getTag(new WeakMap) != weakMapTag)) {\r\n  getTag = function(value) {\r\n    var result = baseGetTag(value),\r\n        Ctor = result == objectTag ? value.constructor : undefined,\r\n        ctorString = Ctor ? toSource(Ctor) : '';\r\n\r\n    if (ctorString) {\r\n      switch (ctorString) {\r\n        case dataViewCtorString: return dataViewTag;\r\n        case mapCtorString: return mapTag;\r\n        case promiseCtorString: return promiseTag;\r\n        case setCtorString: return setTag;\r\n        case weakMapCtorString: return weakMapTag;\r\n      }\r\n    }\r\n    return result;\r\n  };\r\n}\r\n\r\n/**\r\n * Checks if `value` is a valid array-like index.\r\n *\r\n * @private\r\n * @param {*} value The value to check.\r\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\r\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\r\n */\r\nfunction isIndex(value, length) {\r\n  length = length == null ? MAX_SAFE_INTEGER : length;\r\n  return !!length &&\r\n    (typeof value == 'number' || reIsUint.test(value)) &&\r\n    (value > -1 && value % 1 == 0 && value < length);\r\n}\r\n\r\n/**\r\n * Checks if `value` is suitable for use as unique object key.\r\n *\r\n * @private\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\r\n */\r\nfunction isKeyable(value) {\r\n  var type = typeof value;\r\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\r\n    ? (value !== '__proto__')\r\n    : (value === null);\r\n}\r\n\r\n/**\r\n * Checks if `func` has its source masked.\r\n *\r\n * @private\r\n * @param {Function} func The function to check.\r\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\r\n */\r\nfunction isMasked(func) {\r\n  return !!maskSrcKey && (maskSrcKey in func);\r\n}\r\n\r\n/**\r\n * Checks if `value` is likely a prototype object.\r\n *\r\n * @private\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\r\n */\r\nfunction isPrototype(value) {\r\n  var Ctor = value && value.constructor,\r\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\r\n\r\n  return value === proto;\r\n}\r\n\r\n/**\r\n * Converts `value` to a string using `Object.prototype.toString`.\r\n *\r\n * @private\r\n * @param {*} value The value to convert.\r\n * @returns {string} Returns the converted string.\r\n */\r\nfunction objectToString(value) {\r\n  return nativeObjectToString.call(value);\r\n}\r\n\r\n/**\r\n * Converts `func` to its source code.\r\n *\r\n * @private\r\n * @param {Function} func The function to convert.\r\n * @returns {string} Returns the source code.\r\n */\r\nfunction toSource(func) {\r\n  if (func != null) {\r\n    try {\r\n      return funcToString.call(func);\r\n    } catch (e) {}\r\n    try {\r\n      return (func + '');\r\n    } catch (e) {}\r\n  }\r\n  return '';\r\n}\r\n\r\n/**\r\n * Performs a\r\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\r\n * comparison between two values to determine if they are equivalent.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to compare.\r\n * @param {*} other The other value to compare.\r\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\r\n * @example\r\n *\r\n * var object = { 'a': 1 };\r\n * var other = { 'a': 1 };\r\n *\r\n * _.eq(object, object);\r\n * // => true\r\n *\r\n * _.eq(object, other);\r\n * // => false\r\n *\r\n * _.eq('a', 'a');\r\n * // => true\r\n *\r\n * _.eq('a', Object('a'));\r\n * // => false\r\n *\r\n * _.eq(NaN, NaN);\r\n * // => true\r\n */\r\nfunction eq(value, other) {\r\n  return value === other || (value !== value && other !== other);\r\n}\r\n\r\n/**\r\n * Checks if `value` is likely an `arguments` object.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\r\n *  else `false`.\r\n * @example\r\n *\r\n * _.isArguments(function() { return arguments; }());\r\n * // => true\r\n *\r\n * _.isArguments([1, 2, 3]);\r\n * // => false\r\n */\r\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\r\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\r\n    !propertyIsEnumerable.call(value, 'callee');\r\n};\r\n\r\n/**\r\n * Checks if `value` is classified as an `Array` object.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\r\n * @example\r\n *\r\n * _.isArray([1, 2, 3]);\r\n * // => true\r\n *\r\n * _.isArray(document.body.children);\r\n * // => false\r\n *\r\n * _.isArray('abc');\r\n * // => false\r\n *\r\n * _.isArray(_.noop);\r\n * // => false\r\n */\r\nvar isArray = Array.isArray;\r\n\r\n/**\r\n * Checks if `value` is array-like. A value is considered array-like if it's\r\n * not a function and has a `value.length` that's an integer greater than or\r\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\r\n * @example\r\n *\r\n * _.isArrayLike([1, 2, 3]);\r\n * // => true\r\n *\r\n * _.isArrayLike(document.body.children);\r\n * // => true\r\n *\r\n * _.isArrayLike('abc');\r\n * // => true\r\n *\r\n * _.isArrayLike(_.noop);\r\n * // => false\r\n */\r\nfunction isArrayLike(value) {\r\n  return value != null && isLength(value.length) && !isFunction(value);\r\n}\r\n\r\n/**\r\n * Checks if `value` is a buffer.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.3.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\r\n * @example\r\n *\r\n * _.isBuffer(new Buffer(2));\r\n * // => true\r\n *\r\n * _.isBuffer(new Uint8Array(2));\r\n * // => false\r\n */\r\nvar isBuffer = nativeIsBuffer || stubFalse;\r\n\r\n/**\r\n * Performs a deep comparison between two values to determine if they are\r\n * equivalent.\r\n *\r\n * **Note:** This method supports comparing arrays, array buffers, booleans,\r\n * date objects, error objects, maps, numbers, `Object` objects, regexes,\r\n * sets, strings, symbols, and typed arrays. `Object` objects are compared\r\n * by their own, not inherited, enumerable properties. Functions and DOM\r\n * nodes are compared by strict equality, i.e. `===`.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Lang\r\n * @param {*} value The value to compare.\r\n * @param {*} other The other value to compare.\r\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\r\n * @example\r\n *\r\n * var object = { 'a': 1 };\r\n * var other = { 'a': 1 };\r\n *\r\n * _.isEqual(object, other);\r\n * // => true\r\n *\r\n * object === other;\r\n * // => false\r\n */\r\nfunction isEqual(value, other) {\r\n  return baseIsEqual(value, other);\r\n}\r\n\r\n/**\r\n * Checks if `value` is classified as a `Function` object.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\r\n * @example\r\n *\r\n * _.isFunction(_);\r\n * // => true\r\n *\r\n * _.isFunction(/abc/);\r\n * // => false\r\n */\r\nfunction isFunction(value) {\r\n  if (!isObject(value)) {\r\n    return false;\r\n  }\r\n  // The use of `Object#toString` avoids issues with the `typeof` operator\r\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\r\n  var tag = baseGetTag(value);\r\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\r\n}\r\n\r\n/**\r\n * Checks if `value` is a valid array-like length.\r\n *\r\n * **Note:** This method is loosely based on\r\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\r\n * @example\r\n *\r\n * _.isLength(3);\r\n * // => true\r\n *\r\n * _.isLength(Number.MIN_VALUE);\r\n * // => false\r\n *\r\n * _.isLength(Infinity);\r\n * // => false\r\n *\r\n * _.isLength('3');\r\n * // => false\r\n */\r\nfunction isLength(value) {\r\n  return typeof value == 'number' &&\r\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\r\n}\r\n\r\n/**\r\n * Checks if `value` is the\r\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\r\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\r\n * @example\r\n *\r\n * _.isObject({});\r\n * // => true\r\n *\r\n * _.isObject([1, 2, 3]);\r\n * // => true\r\n *\r\n * _.isObject(_.noop);\r\n * // => true\r\n *\r\n * _.isObject(null);\r\n * // => false\r\n */\r\nfunction isObject(value) {\r\n  var type = typeof value;\r\n  return value != null && (type == 'object' || type == 'function');\r\n}\r\n\r\n/**\r\n * Checks if `value` is object-like. A value is object-like if it's not `null`\r\n * and has a `typeof` result of \"object\".\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\r\n * @example\r\n *\r\n * _.isObjectLike({});\r\n * // => true\r\n *\r\n * _.isObjectLike([1, 2, 3]);\r\n * // => true\r\n *\r\n * _.isObjectLike(_.noop);\r\n * // => false\r\n *\r\n * _.isObjectLike(null);\r\n * // => false\r\n */\r\nfunction isObjectLike(value) {\r\n  return value != null && typeof value == 'object';\r\n}\r\n\r\n/**\r\n * Checks if `value` is classified as a typed array.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 3.0.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\r\n * @example\r\n *\r\n * _.isTypedArray(new Uint8Array);\r\n * // => true\r\n *\r\n * _.isTypedArray([]);\r\n * // => false\r\n */\r\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\r\n\r\n/**\r\n * Creates an array of the own enumerable property names of `object`.\r\n *\r\n * **Note:** Non-object values are coerced to objects. See the\r\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\r\n * for more details.\r\n *\r\n * @static\r\n * @since 0.1.0\r\n * @memberOf _\r\n * @category Object\r\n * @param {Object} object The object to query.\r\n * @returns {Array} Returns the array of property names.\r\n * @example\r\n *\r\n * function Foo() {\r\n *   this.a = 1;\r\n *   this.b = 2;\r\n * }\r\n *\r\n * Foo.prototype.c = 3;\r\n *\r\n * _.keys(new Foo);\r\n * // => ['a', 'b'] (iteration order is not guaranteed)\r\n *\r\n * _.keys('hi');\r\n * // => ['0', '1']\r\n */\r\nfunction keys(object) {\r\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\r\n}\r\n\r\n/**\r\n * This method returns a new empty array.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.13.0\r\n * @category Util\r\n * @returns {Array} Returns the new empty array.\r\n * @example\r\n *\r\n * var arrays = _.times(2, _.stubArray);\r\n *\r\n * console.log(arrays);\r\n * // => [[], []]\r\n *\r\n * console.log(arrays[0] === arrays[1]);\r\n * // => false\r\n */\r\nfunction stubArray() {\r\n  return [];\r\n}\r\n\r\n/**\r\n * This method returns `false`.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.13.0\r\n * @category Util\r\n * @returns {boolean} Returns `false`.\r\n * @example\r\n *\r\n * _.times(2, _.stubFalse);\r\n * // => [false, false]\r\n */\r\nfunction stubFalse() {\r\n  return false;\r\n}\r\n\r\nmodule.exports = isEqual;\r\n"]}