import { QuillDeltaToHtmlConverter } from "../miniprogram_npm/quill-delta-to-html/index"

// 获取JSON文本中的html
function getQuestionJsonText(options) {
  return new QuillDeltaToHtmlConverter(options.ops).convert()
}

// 清除最外层p标签
function clearPLabel(html) {
  if (html.slice(0, 3) === "<p>") {
    return html.slice(3, html.length - 4)
  }
  return html
}

// 获取json文本显示内容
const parseQuestionHtml = function (jsonText) {
  let html = ""
  try {
    // 尝试解析JSON文本并标准化换行符
    jsonText = JSON.stringify(JSON.parse(jsonText)).replace(/(\r\n|\r|\n)/g, '\n');

    if (Object.prototype.toString.call(JSON.parse(jsonText)) === "[object Object]") {
      // 获取并处理问题的JSON文本内容
      html = getQuestionJsonText(JSON.parse(jsonText))

      // 清理段落标签（假设此函数移除不需要的<p>标签）
      html = clearPLabel(html)

      // 解析图片HTML（假设此函数处理图片标签）
      html = parseImageHtml(html)

      // 压缩多个连续换行符为一个换行符
      html = html.replace(/(\n)+/g, '\n');

      // 处理换行：替换单个换行符为<br>
      html = html.replace(/\n/g, '<br>');

      // 确保最外层有<p>标签，并且将单个<br>作为段落分隔
      // 在这里为每个<p>标签添加 class='overflow-three'
      html = html.split('<br>')
        .map(paragraph => paragraph.trim())
        .filter(Boolean)
        .map(paragraph => `<p class="overflow-three">${paragraph}</p>`)
        .join('');

    } else {
      // 如果不是对象则直接解析图片HTML，并替换换行符
      html = parseImageHtml(jsonText);

      // 压缩多个连续换行符为一个换行符
      html = html.replace(/(\n)+/g, '\n');

      // 替换换行符为<br>
      html = html.replace(/\n/g, '<br>');

      // 包装非对象情况下的html到<p>标签并添加类名
      html = `<p class="overflow-three">${html.replace(/<\/?p[^>]*>/gi, '')}</p>`;
    }
  } catch (error) {
    // 如果解析失败，尝试直接解析图片HTML，并替换换行符
    html = parseImageHtml(jsonText);

    // 压缩多个连续换行符为一个换行符
    html = html.replace(/(\n)+/g, '\n');

    // 替换换行符为<br>
    html = html.replace(/\n/g, '<br>');

    // 包装错误情况下的html到<p>标签并添加类名
    html = `<p class="overflow-three">${html.replace(/<\/?p[^>]*>/gi, '')}</p>`;
  }

  return html;
}

// 获取图片文本HTML
const parseImageHtml = function (title) {
  title = title.toString().replace("<F", "&lt")
  return title.toString().replace(/\[img=.*?\]/g, function (s) {
    const url = s.toString().replace(/\[img=|\((.*)\)\]$/g, "")
    return '<img src="' + url + '" style="max-width:100%"/>'
  })
}

export { parseQuestionHtml, parseImageHtml }
