.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.weichat-box {
  position: relative;
  background: #fff;
  width: 590rpx;
  padding: 60rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  .user-img {
    width: 120rpx;
    height: 120rpx;
    border: 6rpx solid #fff;
  }
  .title {
    font-size: 32rpx;
    color: #3c3d42;
    font-weight: 600;
    margin-top: 20rpx;
  }
  .tipst {
    font-size: 24rpx;
    color: #919499;
    margin-top: 18rpx;
  }

  .code-img {
    width: 360rpx;
    height: 360rpx;
    margin-top: 40rpx;
  }
  .labels {
    font-size: 24rpx;
    color: #919499;
    margin-top: 24rpx;
  }
  .close-img {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 30rpx;
    top: 30rpx;
  }
}
