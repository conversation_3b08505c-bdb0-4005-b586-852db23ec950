const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
let audioService = null
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
  },
  properties: {
    border: {
      type: Boolean,
      value: true,
    },
    zIndex: {
      type: Number,
      value: 100,
    },
    active: {
      type: String,
      value: "",
    },
    showAudio: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    height: 0,
    show: false,
    menuShow: false,
    progress: 0, // 当前播放音频的进度
    id: "", // 当前播放音频的id
    title: "", // 播放标题
    isPlaying: false, // 是否播放
    musicList: [], // 播放清单
    playMode: "loop-list", // 播放模式
    activeIndex: 0, // 当前选中的分类
    tabList: [], // 播放分类列表
    coverImgUrl: "",
  },
  lifetimes: {
    ready() {
      this.setHeight()
    },
  },
  async attached() {
    console.log(APP.globalData.audioService, "是否初始化")
    audioService = APP.globalData.audioService
    // if (audioService.getState()?.playStateMap[audioService.getState().id]) {
    //   const stateAudio = audioService.getState().playStateMap[
    //     audioService.getState().id
    //   ]
    //   this.setData({
    //     title: stateAudio.title || "",
    //     coverImgUrl: stateAudio.tag_img || null,
    //     id: stateAudio.id || "",
    //     playMode: stateAudio.playMode || "",
    //   })
    // }
    // 注册状态变化监听器
    this.unwatchPlayer = audioService.watchPlayer((state) => {
      // 直接从state中获取当前播放项的状态信息
      const currentItemState = state
      const playStateMap = state?.playStateMap

      // 遍历 playStateMap 并更新 dataList 中对应项的 currentTime 和 progress
      let updatedDataList = this.data.musicList.map((item) => {
        if (playStateMap[item.id]) {
          return {
            ...item,
            currentTime: playStateMap[item.id].currentTime,
            progress: playStateMap[item.id].progress,
          }
        }
        return item
      })
      // 更新UI数据
      this.setData({
        currentTime: currentItemState.currentTime,
        duration: currentItemState.duration,
        progress: currentItemState.progress || 0,
        title: currentItemState.title,
        coverImgUrl: currentItemState.tag_img,
        isPlaying: currentItemState.isPlaying,
        id: currentItemState.id,
        playMode: currentItemState.playMode,
        musicList: updatedDataList,
      })
    })
    audioService.updateAndEmitState(
      audioService.getState().id,
      audioService.getState().isPlaying
    )
  },
  // detached() {
  //   // 当组件销毁时，取消监听播放器状态的变化
  //   if (this.unwatchPlayer) {
  //     this.unwatchPlayer()
  //     console.log("已尝试取消播放器状态监听")
  //   }
  // },
  methods: {
    setHeight: function () {
      var _this = this
      wx.nextTick(function () {
        let query = wx.createSelectorQuery().in(_this)
        query.select(".vans-tabbar").boundingClientRect()
        query.exec(function (res) {
          _this.setData({ height: res[0].height })
        })
      })
    },
    async getMusicList(id) {
      const param = {
        tag_id: id,
        only_video: 1,
        page: 1,
      }
      const res = await UTIL.request(API.getCcumulationList, param, "get")
      console.log(res, "请求")
      let newArr = res.data.list || []
      if (newArr.length > 0) {
        // // 对 newArr 中的每一项进行处理
        newArr = newArr.map((item) => {
          if (item.progress_record && item.progress_record.time !== undefined) {
            item.progress = Math.round(
              (item.progress_record.time / Number(item.video_time)) * 100
            )
            item.currentTime = item.progress_record.time
          }
          return item
        })
      }
      this.setData({
        musicList: newArr,
      })
    },
    openList() {
      console.log("进来没得")

      // 一次性获取状态
      const { list, tabList, id, tagId } = audioService.getState()
      console.log(tabList, "123123123")
      // const activeId = wx.getStorageSync("accumulateActiveId")
      // 准备要更新的数据对象
      const dataToUpdate = {
        show: true,
      }
      // 检查并设置 tabList
      if (tabList?.length) {
        dataToUpdate.tabList = tabList
        if (tagId) {
          const activeIndex = tabList.findIndex((item) => item.id == tagId)
          if (activeIndex >= 0) {
            dataToUpdate.activeIndex = activeIndex
            this.getMusicList(tagId)
          }
        } else {
          this.getMusicList(tabList[0].id)
        }
        // if (activeId) {
        //   const activeIndex = tabList.findIndex((item) => item.id == activeId)
        //   if (activeIndex >= 0) {
        //     dataToUpdate.activeIndex = activeIndex
        //     this.getMusicList(activeId)
        //   }
        // } else {
        //   this.getMusicList(tabList[0].id)
        // }
      }
      // 使用一次 setData 更新所有需要更新的数据
      this.setData(dataToUpdate)
      this.triggerEvent("openHideen")
    },
    openMenu() {
      this.setData({
        menuShow: !this.data.menuShow,
      })
    },
    async changeMenu(e) {
      const index = e.currentTarget.dataset.index
      const dataToUpdate = {
        activeIndex: index,
        menuShow: false,
      }
      console.log("切换之前", this.data.id)
      await this.getMusicList(this.data.tabList[index].id)
      this.setData(dataToUpdate)
    },
    // 点击.play-box以外的区域时隐藏.menu-box
    handlePlayBoxTap(event) {
      if (this.data.menuShow) {
        console.log("开始查询节点 .menu-box")
        // 确保节点已经被渲染出来
        setTimeout(() => {
          const query = wx.createSelectorQuery().in(this)
          query
            .select(".menu-box")
            .boundingClientRect((rect) => {
              console.log("查询结果:", rect)
              if (!rect) {
                console.error("未能找到节点 .menu-box")
                return
              }
              console.log(rect, "数据呢")
              const { left, right, top, bottom } = rect
              const { x, y } = event.detail

              if (!(x >= left && x <= right && y >= top && y <= bottom)) {
                this.setData({ menuShow: false })
              }
            })
            .exec()
        }, 0) // 小延迟以确保节点已被渲染
      }
    },
    onClose() {
      this.setData({
        show: false,
        menuShow: false,
      })
      console.log("触发了没有")
      this.triggerEvent("closeHidden")
    },
    playMusic() {
      console.log("播放呢")
      this.setData({
        isPlaying: true,
      })
      audioService.play()
    },
    pausedMusic() {
      this.setData({
        isPlaying: false,
      })
      audioService.pause()
    },
    prev() {
      audioService.prev()
    },
    next() {
      audioService.next()
    },
    changeMode(e) {
      console.log(e, "数据呢")
      const currentTarget = e.currentTarget.dataset.type
      audioService.setPlayMode(currentTarget)
      console.log(audioService.getState(), "播放器数据")
      this.setData({
        playMode: audioService.getState().playMode,
        menuShow: false,
      })
    },
    changePlay(e) {
      console.log(this.data.tabList[this.data.activeIndex])
      const item = e.currentTarget.dataset.item
      const isPlaying = this.data.isPlaying
      if (this.data.id == item.id && isPlaying) {
        this.setData({
          isPlaying: false,
        })
        audioService.pause()
      } else {
        this.setData({
          isPlaying: true,
          id: item.id,
        })
        audioService.playItemById(
          item.id,
          this.data.tabList[this.data.activeIndex].id
        )
      }
      this.setData({
        menuShow: false,
      })
    },
    goMaterial() {
      ROUTER.navigateTo({
        path: "/pages/accumulate/detail/index",
        query: {
          id: this.data.id,
        },
      })
    },
    closeMusic() {
      audioService.clearMusic()
    },
  },
})
