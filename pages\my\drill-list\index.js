const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeIndex: 1, // 1-每日演练 2-线下专场
    tabList: [],
    isComplete: false,
    isChangeOver: false,
    listHeight: null,
    isNeedFreshData: {}, // 是否需要刷新数据的页码和题目
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    await this.getTabList()
  },
  async getTabList() {
    const res = await UTIL.request(API.getMyDrillTag)
    let arr = res.data.list || []
    if (arr.length) {
      arr.forEach((item) => {
        item.page = 1
        item.limit = 20
        item.dataList = []
        item.isRequest = true
      })
    }
    this.setData({
      tabList: arr,
      activeIndex: arr[0].id,
    })
    this.getList(1)
  },
  async getNewList() {
    let arr = []
    let tabArr = this.data.tabList
    let activeIndex = tabArr.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    const param = {
      page: this.data.isNeedFreshData.page,
      limit: tabArr[activeIndex].limit,
      tag_id: this.data.activeIndex,
    }
    const res = await UTIL.request(API.getMyDrillList, param)
    arr = res.data.list || []
    let data = {}
    if (arr.length) {
      data = arr.find((item) => item.id == this.data.isNeedFreshData.id)
      data.page = this.data.isNeedFreshData.page
    }
    let oldArr = tabArr[activeIndex].dataList
    for (let i = 0; i < oldArr.length; i++) {
      if (oldArr[i].id == this.data.isNeedFreshData.id) {
        oldArr[i] = data
      }
    }
    tabArr[activeIndex].dataList = oldArr
    this.setData({
      tabList: tabArr,
    })
  },
  async getList(type) {
    let tabArr = this.data.tabList
    let activeIndex = tabArr.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    const param = {
      tag_id: this.data.activeIndex,
      page: tabArr[activeIndex].page,
      limit: tabArr[activeIndex].limit,
    }
    const res = await UTIL.request(API.getMyDrillList, param)
    let isRequest = tabArr[activeIndex].isRequest
    let list = res.data.list || []
    if (list.length) {
      list.forEach((item) => {
        item.page = tabArr[activeIndex].page
      })
    }
    let arr = []
    isRequest = list.length < tabArr[activeIndex].limit ? false : true
    arr = type == 1 ? list : tabArr[activeIndex].dataList.concat(list)
    tabArr[activeIndex].dataList = arr
    tabArr[activeIndex].isRequest = isRequest
    this.setData({
      tabList: tabArr,
      isComplete: true,
      isChangeOver: true,
    })
    this.getListHeight()
  },
  getListHeight() {
    if (this.data.listHeight) {
      return
    }
    const query = wx.createSelectorQuery()
    query
      .select(".main-content") // 替换为你的类名
      .boundingClientRect((rect) => {
        if (rect) {
          let heightText = "height: calc(100vh - " + rect.top + "px)"
          this.setData({
            listHeight: heightText,
          })
          console.log("距离顶部高度: ", rect.top) // 输出距离顶部的高度
        }
      })
      .exec()
  },
  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      activeIndex: index,
    })
    let activeIndex = this.data.tabList.findIndex(
      (item) => item.id == this.data.activeIndex
    )
    if (!this.data.tabList[activeIndex].dataList.length) {
      this.getList(1)
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (
      this.data.activeIndex &&
      this.data.isComplete &&
      this.data.isNeedFreshData.page
    ) {
      this.getNewList()
    }
  },

  toSingeQuestion(e) {
    const { data } = e.currentTarget.dataset
    let obj = {
      page: data.page,
      id: data.id,
    }
    this.setData({
      isNeedFreshData: obj,
    })
    ROUTER.navigateTo({
      path: "/pages/speech/report/index",
      query: {
        record_id: data.id,
        type: "drill",
      },
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getList(2)
  },
})
