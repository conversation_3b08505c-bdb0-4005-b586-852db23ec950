<view class="course-list-card" bindtap="goDetail" data-data="{{item}}">
  <image src="{{item.app_img}}" class="course-bg"></image>
  <view class="fonts">
    <view class="title {{item.summary?'text-ellipsis-1':'text-ellipsis-2'}} ">{{item.name}}</view>
    <!-- <rich-text class="title {{item.summary?'text-ellipsis-1':'text-ellipsis-2'}}" nodes="{{item.name}}"></rich-text> -->

    <view class="label-text text-ellipsis-1">{{item.summary}}</view>
    <view class="fonts-bootom">
      <!-- 免费试听 -->
      <view class="try-listen" wx:if="{{item.free_audition ===1}}">
        <image class="image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/course/coures_listen.png"></image>
        免费试听
      </view>
      <!-- 老师列表 -->
      <view class="teacher-list" wx:else>
        <block wx:if="{{item.teachers.length>0}}">
          <image wx:for="{{item.teachers}}" wx:key="index" src="{{item.portrait}}"></image>
        </block>
      </view>
      <view class="right-box">
        <!-- <view class="money">券后<span class="symbol">￥</span><span class="num">168</span></view> -->
        <!-- 免费课程 -->
        <block wx:if="{{item.usability=='free'}}">
          <view class="free-text">免费</view>
        </block>
        <!-- 商品 -->
        <block wx:else>
          <view class="money"><span class="symbol">￥</span><span class="num">{{item.product.price}}</span></view>
          <view class="study-num" wx:if="{{item.right_bottom_txt}}">{{item.right_bottom_txt}}</view>
        </block>
      </view>
    </view>
  </view>
</view>