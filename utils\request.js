const {
  miniProgram: { envVersion },
} = wx.getAccountInfoSync()
const { VERSION } = require("../config/config")
const { API_BASE_URL } = require("@/config/api")

/**
 * 封封微信的的request
 */
function request(url, data = {}, method = "POST", header = {}) {
  return new Promise(function (resolve, reject) {
    const globalData = getApp()?.globalData || {}
    let adsource = globalData.adsource

    const userInfo = globalData.userInfo || wx.getStorageSync("user") || {}
    const { exam, province } = wx.getStorageSync("base") || {}

    let token = userInfo.token
    if (token) {
      header.Authorization = "Bearer " + token
    }
    header.version = VERSION
    province?.key && (header.province = province?.key)
    exam?.key && (header["exam"] = exam?.key)

    if (adsource) {
      header.adsource = adsource
    }

    // 线上打印
    if (envVersion !== "develop") {
      console.log(["请求开始：" + url.replace(API_BASE_URL, ""), data])
    }
    wx.request({
      url,
      data,
      method,
      header,
      success: function (res) {
        // 线上打印
        if (envVersion !== "develop") {
          console.log(["请求结束：" + url.replace(API_BASE_URL, ""), res.data])
        }
        resolve(res.data)
      },
      fail: function (err) {
        reject(err)
      },
    })
  })
}

module.exports = {
  request,
}
