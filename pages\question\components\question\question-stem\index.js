Component({
  options: {
    addGlobalClass: true,
  },
  properties: {
    question: {
      type: Object,
      value: {},
    },
    questionList: {
      type: Array,
      value: [],
    },
    open: {
      type: Boolean,
      value: false, // 默认值
    },

    listenCount: {
      type: Number,
      value: 1, // 听的次数
    },
    playState: {
      type: Object,
      value: {}, // 当前播放状态
    },
    isRecording: {
      type: Boolean,
      value: false, // 是否录制中
    },
    isDark: {
      type: Boolean,
      value: false, // 暗色
    },
    mainState: {
      type: String,
      value: "",
    },
    questionType: {
      type: String,
      value: "singleQuestion", //singleQuestion 单题 manyQuestion 多个题
    },
    manyMode: {
      type: String,
      value: "listen", // listen 听题  look 看题
    },
  },
  data: {
    activeIndex: 0,
  },
  lifetimes: {},
  methods: {
    changePop() {
      this.setData({
        open: !this.data.open,
      })
      // this.triggerEvent("popchange", { open: this.data.open }) // 触发事件通知父组件
    },
    open() {
      this.setData({
        open: true,
      })
    },
    close() {
      this.setData({
        open: false,
      })
    },

    changeTab(e) {
      console.log(e)
      const index = e.currentTarget.dataset.index
      this.setData({
        activeIndex: index,
      })
      // const index = e.cu
    },

    tapPlayQuestion() {
      this.triggerEvent("onPlayQuestion", { question: this.data.question }) // 触发事件通知父组件
    },
    tapPauseQuestion() {
      this.triggerEvent("onPauseQuestion", { question: this.data.question }) // 触发事件通知父组件
    },
    onMaterialOpen(e) {
      this.triggerEvent("onMaterialOpen", {
        material: e.currentTarget.dataset.item,
      })
    },
    previewImage(e) {
      this.triggerEvent("onImagePreview", e.detail)
    },
  },
})
