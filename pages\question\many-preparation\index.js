const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")

const QUESTION_SETUP_CACHE = require("@/utils/cache/questionSetupCache")
let PAGE_OPTIONS = {}
Page({
  data: {
    show_white: false,
    activeIndex: 0,
    questionData: {},
    pageTitle: "",

    isLogin: false, // 用户是否登录
  },
  async onLoad(options) {
    PAGE_OPTIONS = options
    await APP.checkLoadRequest()
    await this.getQuestionDeatail()
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
  },
  onShow() {
    this.setData({
      isLogin: APP.getIsUserLogin(),
    })
  },
  goHistory() {
    ROUTER.navigateTo({
      path: "/pages/question/question-detail-history/index",
      query: PAGE_OPTIONS,
    })
  },
  getQuestionDeatail() {
    return UTIL.request(API.getVoiceQuestionDetail, {
      type: PAGE_OPTIONS.type,
      item_no: PAGE_OPTIONS.item_no,
    }).then((res) => {
      const resData = res.data
      this.setData({
        questionData: resData,
        pageTitle: resData.is_online ? "面试PK赛" : "面试演练",
      })
    })
  },
  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/practice/home/<USER>",
      })
      return false
    }
    wx.navigateBack()
  },
  changeSetting(e) {
    const data = e.currentTarget.dataset.item
    this.setData({
      activeIndex: data,
    })
  },
  onPageScroll(e) {
    if (e.scrollTop > APP.globalData.navHeight) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
  goQuestion() {
    let manyMode =
      this.data.questionData.question_volume_info.question_mod == 1
        ? "listen"
        : "look"
    ROUTER.redirectTo({
      path: "/pages/question/answer-vertical/index",
      query: {
        type: PAGE_OPTIONS.type,
        item_no: PAGE_OPTIONS.item_no,
        manyMode,
      },
    })
  },

  getPageShareParams() {
    let query = PAGE_OPTIONS
    return APP.createShareParams({
      title: this.data.questionData.share_info.title || "",
      imageUrl: this.data.questionData.share_info.image || "",
      path: "/pages/question/many-preparation/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
})
