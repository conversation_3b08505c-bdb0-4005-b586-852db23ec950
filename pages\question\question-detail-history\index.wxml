<wxs module="utils">
  // 格式化时间戳
  function formatTimeStamp(time) {
    // 辅助函数需要定义在外部并在此处引用或直接定义在这里
    var formatSingleNumber = function (number) {
      return number <= 9 ? "0" + number : number;
    };

    time = time >>> 0; // 确保time是整数
    if (time < 0) {
      time = 0;
    }

    var minute = Math.floor((time % 3600) / 60),
      second = Math.floor(time % 60);

    // 返回格式化的分钟和秒
    var ms = formatSingleNumber(minute) + ":" + formatSingleNumber(second);

    return ms;
  }
  module.exports = {
    formatTimeStamp: formatTimeStamp,
  }
</wxs>
<view class="main-content" wx:if="{{isComplete}}">
  <view class="answering-process" wx:if="{{historyList.length>0}}">
    <view class="answering-process-box">
      <view class="process-item" catchtap="goRecordId" data-id="{{item.record_id}}" wx:for="{{historyList}}" wx:key="index">
        <view class="yuan"></view>
        <view class="time">{{item.record_time}}</view>
        <view class="right-box">
          <view class="left-font">
            <view class="titles text-ellipsis-2">第{{item.record_num}}次作答</view>
            <view class="font-bottom">
              <view class="waiting" wx:if="{{item.correct_status ==0}}">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_waiting.png"></image>
                报告生成中…
              </view>
              <view class="time-all" wx:if="{{item.correct_status ==-1}}">
                报告生成失败
              </view>
              <block wx:if="{{item.correct_status ==1}}">
                <view class="time-all">时长：<text>{{utils.formatTimeStamp(item.answer_time)}}</text></view>
                <view class="line"></view>
                <view class="score-all">得分：<text>{{item.score}}</text></view>
              </block>
            </view>
          </view>
          <image class="right-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_right_arrow.png"></image>
        </view>
      </view>
    </view>
  </view>
  <tips-default text="暂无历史作答记录" wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
</view>