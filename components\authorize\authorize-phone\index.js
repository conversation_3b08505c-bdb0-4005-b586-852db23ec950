// pages/login/index.js
const APP = getApp()
Component({
  properties: {
    isBindPhone: Boolean,
    customClass: String,
    key: {
      type: String,
      optionalTypes: [Object],
    },
    item: null,
  },

  methods: {
    // 手机号授权
    getPhoneNumber: async function (e) {
      let { encryptedData, iv } = {
        ...e.detail,
      }
      const result = await APP.userLogin({
        encryptedData,
        iv,
      })
      if (!result) {
        wx.showToast({
          title: "请登录",
          icon: "none",
        })
        return false
      }
      // 上报数据
      try {
        wx.reportEvent("user_register", {
          launch_options: JSON.stringify(APP.globalData.launchOptions),
        })
      } catch (error) {
        wx.reportEvent("user_register", {
          launch_options: APP.globalData.launchOptions,
        })
      }

      this.setData({
        isBindPhone: APP.getIsUserLogin(),
      })

      this.triggerEvent("onAuthorize", {
        userInfo: this.data.key ? this.data.key : result,
        item: this.data.item,
      })
    },
    tapPhoneButton() {
      console.log(this.data.item)
      this.triggerEvent("onAuthorize", {
        userInfo: this.data.key ? this.data.key : APP.globalData.userInfo,
        item: this.data.item,
      })
    },
  },
})
