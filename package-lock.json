{"name": "mp-ai-interview", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@vant/weapp": "^1.11.7", "quill-delta-to-html": "^0.12.1"}}, "node_modules/@vant/weapp": {"version": "1.11.7", "resolved": "https://registry.npmmirror.com/@vant/weapp/-/weapp-1.11.7.tgz", "integrity": "sha512-Rwn9BBnb4kHSV4XmvBicwtd42J+amEUfnFDcXJsGNPNX4a9c/DoT6YLsm4X1wB2+sQbdiQsbFBLAvGRBxCkD8g=="}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==", "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead."}, "node_modules/quill-delta-to-html": {"version": "0.12.1", "resolved": "https://registry.npmmirror.com/quill-delta-to-html/-/quill-delta-to-html-0.12.1.tgz", "integrity": "sha512-QhpeMk9+5ge3HYbL5A0Ewz3pXCsbemqGvIF/kw5D6D4V68AtcUp7yt9xNUkzOk/0IQz43hKy3IkzBzRhLIE+oA==", "dependencies": {"lodash.isequal": "^4.5.0"}}}}