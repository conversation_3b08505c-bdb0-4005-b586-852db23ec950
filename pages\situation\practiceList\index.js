// 文章列表页

const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/routerManager")
const APP = getApp()
let PAGE_OPTIONS = {}
Page({
  data: {
    dataList: [],
    pageTitle: "",
    share_info: {},
    isRequest: false,
    page: 1,
    limit: 20,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    PAGE_OPTIONS = options
    this.getList(1)
  },
  onShow() {},
  onReachBottom() {
    if (!this.data.isRequest) {
      return
    }
    this.setData({
      //改变页码
      page: this.data.page + 1,
    })
    this.getList(2)
  },
  onShareAppMessage() {},
  async getList(type) {
    let url = API.getNewsList
    let param = {
      tag_id: PAGE_OPTIONS.tag_id,
      page: this.data.page,
      limit: this.data.limit,
    }
    const res = await UTIL.request(url, param)
    if (res?.error?.code === 0) {
      let arr = []
      let newArr = res.data.list || []
      let isRequest = newArr < this.data.limit ? false : true
      arr = type == 1 ? newArr : this.data.dataList.concat(newArr)
      this.setData({
        dataList: arr,
        isRequest,
        share_info: res.data.share_info,
        pageTitle: res.data.title || PAGE_OPTIONS.title,
      })
    }
  },
  goDetail(e) {
    const { id } = e.currentTarget.dataset
    ROUTER.navigateTo({
      path: "/pages/situation/practice-webview/index",
      query: {
        id: id,
      },
    })
  },
  getPageShareParams() {
    let query = PAGE_OPTIONS
    return APP.createShareParams({
      title: this.data.share_info.title || "",
      imageUrl: this.data.share_info.image || "",
      path: "/pages/situation/practiceList/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    params.query = ROUTER.convertPathQuery(params.query)
    return params
  },
})
