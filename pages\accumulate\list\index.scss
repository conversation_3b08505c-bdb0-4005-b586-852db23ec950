/* pages/accumulate/list/index.wxss */
page {
  box-sizing: border-box;
  background: #f2f4f7;
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/banner_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  flex-direction: column;
}
.fixed-box {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
}
.no-data-box {
  overflow: hidden;
}
.accumulate-tab {
  padding: 0 32rpx;
  box-sizing: border-box;
  white-space: nowrap;
  position: sticky;
  left: 0;
  &-item {
    padding: 32rpx 0;
    font-size: 32rpx;
    color: #666666;
    display: inline-block;
    margin-right: 80rpx;
    box-sizing: border-box;
    position: relative;
    .icon-box {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      &.pl40 {
        padding-left: 40rpx;
      }
    }
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    &.active {
      color: #e60000;
      font-weight: bold;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
.show-white {
  background: #ffffff;
}

.scroll {
  display: flex;
}

.van-tabs__line {
  display: none !important;
}

.van-tabs__scroll {
  background-color: transparent !important;
}

.accumulate-list {
  padding: 0 32rpx;
  padding-top: 24rpx;
  padding-bottom: 130rpx;
  box-sizing: border-box;
  .accumulate-list-item {
    display: flex;
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    .fonts {
      display: flex;
      align-items: center;
      margin-top: 20rpx;
      .text-content-box {
        flex: 1;
        font-size: 24rpx;
        color: #919499;
      }
    }
    .lines {
      font-size: 24rpx;
      color: #c2c5cc;
      margin: 0 8rpx 0 2rpx;
    }
    .time {
      font-size: 24rpx;
      color: #919499;
    }
    .progress {
      font-size: 24rpx;
      color: #919499;
      .double-width {
        display: inline-block;
        width: 28rpx;
      }
      .single-width {
        display: inline-block;
        width: 15rpx;
      }
    }

    &.active {
      .font {
        .title {
          color: #e60000;
        }
      }
    }
    .font {
      flex: 1;
      min-width: 0;
      .title {
        font-size: 30rpx;
        color: #3c3d42;
        font-weight: bold;
        image {
          width: 24rpx;
          height: 24rpx;
          margin-right: 10rpx;
        }
      }
      .label {
        font-size: 24rpx;
        color: #919499;
        line-height: 36rpx;
        margin-top: 20rpx;
      }
    }
    .right {
      margin-left: 16rpx;
      image {
        width: 48rpx;
        height: 48rpx;
      }
      .play-box {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        image {
          width: 24rpx !important;
          height: 24rpx !important;
        }
      }
    }
  }
}

.sticky-box {
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
}

.oh-hideen {
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  z-index: 0;
}
