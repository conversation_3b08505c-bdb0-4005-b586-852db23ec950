page {
  box-sizing: border-box;
  background: #f2f4f7;
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/banner_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
}

.main-content {
  padding: 0 32rpx;
  padding-top: 32rpx;
  padding-bottom: 130rpx;
}

.face-list-card {
  box-sizing: border-box;
  width: 100%;
  position: relative;
  margin-bottom: 32rpx;

  .bg-img {
    width: 100%;
    display: block;
    border-radius: 16rpx;
    height: 404rpx;
  }
  .bottom-box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0 0 16rpx 16rpx;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0) 100%
    );
    padding: 60rpx 24rpx 24rpx 24rpx;
    display: flex;
    align-items: center;
    .left-box {
      flex: 1;
      min-width: 0;
      padding-right: 30rpx;
      .title {
        font-size: 30rpx;
        color: #fff;
        font-weight: bold;
      }
      .time {
        font-size: 24rpx;
        color: #fff;
        margin-top: 16rpx;
        font-weight: 400;
      }
    }
    .see-btn {
      font-size: 26rpx;
      font-weight: 500;
      color: #e60000;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 64rpx;
      padding: 0rpx 28rpx;
      height: 58rpx;
      line-height: 58rpx;
    }
  }
}

.border_b {
  padding-bottom: 32rpx;
  border-bottom: 0.5px solid rgba(235, 236, 240, 0.6);
  margin-bottom: 32rpx;
  .face-list-card {
    margin-bottom: 0;
  }
}

.top-content {
  padding: 32rpx 32rpx 0rpx 32rpx;
  // border-bottom: 0.5px solid rgba(235, 236, 240, 0.6);
  position: relative;
  z-index: 2;
}

.face-list-more {
  background: #fff;
  border-radius: 16rpx;
  // padding: 40rpx 32rpx 24rpx 32rpx;
  margin-bottom: 32rpx;
  position: relative;
  box-shadow: 0rpx 2rpx 8rpx 2rpx rgba(0, 0, 0, 0.04);
  .card-item {
    margin-bottom: 32rpx;

    // &:last-child {
    //   margin-bottom: 0;
    //   padding-bottom: 0;
    //   border-bottom: 0;
    // }
    .title {
      font-size: 30rpx;
      color: #22242e;
      font-weight: bold;
      position: relative;
      z-index: 2;
      // text-align: center;
    }
    .label-text {
      font-size: 24rpx;
      color: #919499;
      margin-top: 10rpx;
      position: relative;
      z-index: 2;
      // text-align: center;
    }
    padding-bottom: 32rpx;
    border-bottom: 0.5px solid rgba(235, 236, 240, 0.6);
  }
  .card-type-face-big {
    .banner-img {
      width: 100%;
      margin-top: 24rpx;
    }
  }
  .card-type-face-small {
    display: flex;
    // align-items: center;
    .fonts {
      flex: 1;
      min-width: 0;
      padding-right: 40rpx;
    }
    .banner-img {
      width: 170rpx;
      height: 106rpx;
      border-radius: 8rpx;
      object-fit: cover;
    }
    .right-img {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .more-face {
    font-size: 22rpx;
    color: #666666;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    padding: 0 16rpx;
    height: 72rpx;
    margin-top: 64rpx;
    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.face-defaul-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 26rpx 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  box-shadow: 0rpx 2rpx 8rpx 2rpx rgba(0, 0, 0, 0.04);
  // align-items: center;
  .banner-img {
    width: 240rpx;
    height: 148rpx;
    border-radius: 8rpx;
    object-fit: cover;
  }
  .fonts {
    flex: 1;
    min-width: 0;
    margin-left: 24rpx;
    .title {
      font-size: 30rpx;
      color: #22242e;
      font-weight: bold;
    }
    .label-text {
      font-size: 24rpx;
      color: #919499;
      margin-top: 8rpx;
      // height: 34rpx;
    }
    .font-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 22rpx;
      .left-tex {
        font-size: 22rpx;
        color: #c2c5cc;
      }
    }
  }
}

.course-card {
  box-shadow: 0rpx 2rpx 8rpx 2rpx rgba(0, 0, 0, 0.04);
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  .titles {
    font-size: 30rpx;
    color: #22242e;
    line-height: 48rpx;
    font-weight: bold;
    .label-tip {
      display: inline;
      background: rgba(255, 243, 240, 1);
      font-size: 22rpx;
      color: #f03916;
      position: relative;
      padding: 5rpx 18rpx 5rpx 48rpx;
      border-radius: 18rpx;
      margin-right: 10rpx;
      transform: translateY(-5rpx);
      image {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .label-text {
    font-size: 24rpx;
    color: #919499;
    margin-top: 10rpx;
  }
  .fonts-bootom {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .teacher-list {
      display: flex;
      align-items: center;
      .img {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 16rpx;
      }
    }

    .try-listen {
      display: flex;
      align-items: center;
      font-size: 20rpx;
      color: #ff6a4d;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      .img {
        width: 14rpx;
        height: 16rpx;
        display: block;
        margin-right: 8rpx;
      }
      background: rgba(255, 106, 77, 0.05);
    }

    .right-box {
      .money {
        font-size: 22rpx;
        color: #e60000;
        .coupons {
          background: rgba(255, 106, 77, 0.05);
          color: rgba(255, 106, 77, 1);
          border: 0.5px solid rgba(255, 106, 77, 0.3);
          padding: 2rpx 6rpx;
          border-radius: 6rpx;
        }
        .symbol {
          font-size: 24rpx;
          font-weight: bold;
          margin-left: 4rpx;
        }
        .num {
          font-family: "DINBold";
          font-size: 32rpx;
          color: #e60000;
        }
      }
      .free-text {
        font-size: 28rpx;
        color: #e60000;
        font-weight: bold;
        text-align: right;
      }
      .study-num {
        font-size: 16rpx;
        color: #c2c5cc;
        text-align: right;
        margin-top: 4rpx;
      }
    }
  }
  .label-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16rpx;
    .try-listen {
      display: flex;
      align-items: center;
      font-size: 20rpx;
      color: #ff6a4d;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      .img {
        width: 14rpx;
        height: 16rpx;
        display: block;
        margin-right: 8rpx;
      }
      background: rgba(255, 106, 77, 0.05);
    }
  }
}

.left-tex {
  font-size: 22rpx;
  color: #c2c5cc;
}

.see-detail {
  background: #e60000;
  font-size: 26rpx;
  color: #fff;
  padding: 0rpx 28rpx;
  height: 58rpx;
  line-height: 58rpx;
  border-radius: 30rpx;
}

.right-boxs {
  padding-right: 75px;
  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 24rpx;
  }
}

.tab-list {
  background: #fff;
  padding-left: 40rpx;
  padding-bottom: 24rpx;
}

.map-where {
  padding-bottom: 24rpx;
  .left-fonts {
    font-size: 28rpx;
    color: #3c3d42;
    display: flex;
    align-items: center;
    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.label-tip {
  display: inline;
  background: rgba(255, 243, 240, 1);
  font-size: 22rpx;
  color: #f03916;
  position: relative;
  padding: 5rpx 18rpx 5rpx 48rpx;
  border-radius: 18rpx;
  margin-right: 10rpx;
  transform: translateY(-5rpx);
  image {
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.more-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}

.tc {
  text-align: center;
}

.f36 {
  font-size: 36rpx !important;
}

.bor_0 {
  border-bottom: 0 !important;
}

.face-list-c {
  padding: 0 24rpx;
  &:last-child {
    .card-item {
      border-bottom: 0;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
}

.border-b {
  border-bottom: 0;
  position: relative;
  &::after {
    position: absolute;
    display: block;
    content: " ";
    border-bottom: 0.5px solid rgba(235, 236, 240, 0.6);
    width: 100%;
    height: 0.5px;
    bottom: 0;
    z-index: 2;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.title-top {
  font-size: 36rpx;
  position: absolute;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
}

.oh-hideen {
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  z-index: 0;
}
