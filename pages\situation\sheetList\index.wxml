<navigation-bar back="{{true}}" isSticky="{{ true }}">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">{{pageTitle}}</view>
  </view>
</navigation-bar>
<van-tabs wx:if="{{tag_id && isComplete && accumulateTab.length}}" active="{{ active }}" title-active-color="#E60000" custom-class="tab-list-area" sticky bind:change="onChange">
  <van-tab title="{{item.short_title || item.title}}" wx:for="{{accumulateTab}}" name="{{ item.id }}">
  </van-tab>
</van-tabs>
<block wx:if="{{isComplete}}">
  <view wx:for="{{ accumulateTab }}" wx:key="index" class="main-content">
    <scroll-view hidden="{{active != item.id}}" scroll-y style="{{stickyHeight}}" bindscrolltolower="onScrollToLower">
      <view class="sheet-list" wx:if="{{item.dataList.length}}">
        <view class="sheet-list-item" wx:for="{{item.dataList}}" wx:for-item="item1" wx:key="index" bind:tap="goDetail" data-id="{{ item1.id }}">
          <view class="title text-ellipsis-2">{{item1.title}}</view>
          <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/drill/drill_arrow.png"></image>
          <!-- <image class="new" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/common_new.png"></image> -->
        </view>
      </view>
      <tips-default width="280" wx:if="{{!item.dataList.length && !item.isRequest}}" text="内容筹备中..." imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/accumulate_no_data.png"></tips-default>
    </scroll-view>
  </view>
</block>