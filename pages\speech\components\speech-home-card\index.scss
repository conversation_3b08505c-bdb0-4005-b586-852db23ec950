.speech-top-box {
  position: relative;
  width: 100%;
  height: 388rpx;
  margin-bottom: 24rpx;
  &:last-of-type {
    margin-bottom: 0;
  }
  .speech-bg {
    width: 100%;
    height: 100%;
  }
  .font {
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    right: 0;
    padding: 42rpx 40rpx 40rpx 40rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .top-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;
      width: 100%;
      .top-left {
        display: flex;
        align-items: center;
        .drill_fire {
          width: 40rpx;
          height: 40rpx;
          margin-left: 8rpx;
        }
        .title {
          font-size: 44rpx;
          font-weight: bold;
          color: #ffffff;
          line-height: 46rpx;
        }
      }
      .top-right {
        background: linear-gradient(90deg, #f94f4f 0%, #ff886c 100%);
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 22rpx;
        color: #ffffff;
        padding: 8rpx 12rpx;
      }
      .blue-top-right {
        background: linear-gradient(90deg, #0092ff 0%, #e60000 100%);
      }
    }
    .desc-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #c2c5cc;
      width: 440rpx;
      line-height: 36rpx;
      margin-bottom: 48rpx;
    }
    .time-text {
      font-weight: 500;
      font-size: 26rpx;
      color: #ffffff;
    }
    .bottom-area {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex: 1;
      .text {
        color: #c2c5cc;
        font-size: 24rpx;
        font-weight: 400;
      }
    }
  }
  .blank {
    align-items: center;
    justify-content: center;
    &-img {
      width: 96rpx;
      height: 96rpx;
      margin-bottom: 40rpx;
    }
    &-text {
      font-weight: 500;
      font-size: 24rpx;
      color: #eaeaea;
      line-height: 46rpx;
    }
  }
}

.speech-home-card {
  display: flex;
  height: 828rpx;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  overflow: hidden;
  padding: 40rpx;
  flex-direction: column;
  background-size: cover;
  background-repeat: no-repeat;
  &-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .title-image {
      width: 156rpx;
      .img {
        max-width: 100%;
      }
    }
    .history-button {
      display: flex;
      align-items: center;
      font-size: 0;
      .img {
        width: 32rpx;
        display: block;
      }
      .text {
        font-size: 24rpx;
        color: #ffffff;
        opacity: 0.6;
        margin-left: 4rpx;
      }
    }
  }
  &-content {
    flex: 1;
    .content-title {
      font-size: 48rpx;
      color: #ffffff;
      line-height: 68rpx;
      margin-bottom: 40rpx;
      font-weight: bold;
    }
    .content-subtitle {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      line-height: 36rpx;
    }

    .exam-content {
      margin-top: 68rpx;
      &-item {
        display: flex;
        align-items: center;
        .img {
          width: 32rpx;
          margin-right: 8rpx;
        }
        .text1 {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.6);
          line-height: 46rpx;
        }
        .text2 {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.9);
          line-height: 46rpx;
        }
      }
    }
    .rank {
      margin-top: 68rpx;
      &-header {
        margin-bottom: 26rpx;
        .img-icon {
          width: 32rpx;
        }
        .img-title {
          width: 112rpx;
          margin-left: 8rpx;
        }
      }
      &-content {
        .rank-content-item {
          display: flex;
          align-items: center;
          width: 370rpx;
          height: 56rpx;
          background: linear-gradient(
            270deg,
            rgba(0, 0, 0, 0.3) 0%,
            rgba(0, 0, 0, 0) 100%
          );
          border-radius: 28rpx 28rpx 28rpx 28rpx;
          margin-bottom: 10rpx;
          padding-right: 24rpx;
          box-sizing: border-box;
          .number {
            padding-left: 10rpx;
            display: flex;
            align-items: center;
            .img {
              width: 32rpx;
            }
          }
          .user-info {
            display: flex;
            align-items: center;
            flex: 1;
            .user-portrait {
              width: 40rpx;
              height: 40rpx;
              border-radius: 32rpx;
              overflow: hidden;
              margin-right: 8rpx;
              .img {
                object-fit: cover;
              }
            }
            .user-name {
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.9);
              line-height: 46rpx;
              width: 130rpx;
            }
            .user-score {
              margin-left: auto;
              display: flex;
              align-items: center;
              .number {
                font-size: 24rpx;
                color: #ffe49a;
                line-height: 48rpx;
                margin-right: 4rpx;
              }
              .text {
                font-size: 24rpx;
                color: rgba(255, 255, 255, 0.6);
                line-height: 46rpx;
              }
            }
          }
        }
      }
    }
  }
  &-footer {
    .time-button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88rpx;
      backdrop-filter: blur(10px);
      background-color: rgba(#fff, 0.3);
      border-radius: 44rpx 44rpx 44rpx 44rpx;
      .number {
        font-weight: bold;
        font-size: 28rpx;
        color: #ffe49a;
        line-height: 56rpx;
      }
      .text {
        font-size: 28rpx;
        color: #ffffff;
        line-height: 36rpx;
        margin-left: 16rpx;
      }
    }

    .join-button {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 36rpx;
      height: 88rpx;
      background: linear-gradient(318deg, #e60000 0%, #ff7b53 100%);
      border-radius: 50rpx 50rpx 50rpx 50rpx;
      width: 100%;
    }
  }
}

.no-permission {
  height: 88rpx;
  backdrop-filter: blur(10px);
  background-color: rgba(#fff, 0.3);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ffe49a;
  .tip {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
  .right {
    width: 24rpx;
    height: 24rpx;
    margin-left: 8rpx;
  }
}
