Component({
  properties: {
    show: <PERSON><PERSON><PERSON>,
    options: {
      type: Array,
      value: [],
    },
    // 当前选中的值，移除children字段
    selected: {
      type: Array,
      value: [],
    },
  },
  data: {
    originalSelected: [], // 原始选中的值，保留children字段
    currentIndex: 0, // 当前选中的tab索引
    currentOptions: [], // 当前显示的选项
  },
  attached() {
    if (!this.data.selected?.length) {
      this.setData({
        selected: [this.removeChildren(this.data.options[0])],
        originalSelected: [this.data.options[0]],
      })
    }
    this.loadCurrentOptions()
  },
  methods: {
    // 移除children字段的辅助函数
    removeChildren: function (option) {
      return {
        text: option.text,
        value: option.value,
      }
    },

    changeTab: function (e) {
      const newIndex = e.currentTarget.dataset.index
      this.setData({ currentIndex: newIndex })
      this.loadCurrentOptions()
    },

    selectOption: function (e) {
      const option = e.currentTarget.dataset.option
      const { selected, originalSelected, currentIndex } = this.data

      // 使用辅助函数移除children字段
      const newOption = this.removeChildren(option)

      if (option.child && option.child.length > 0) {
        this.setData({
          selected: [
            ...selected.slice(0, currentIndex),
            newOption,
            { text: "请选择", value: "-1" },
          ],
          originalSelected: [
            ...originalSelected.slice(0, currentIndex),
            option,
            { text: "请选择", value: "-1", child: [] },
          ],
          currentIndex: currentIndex + 1,
        })
      } else {
        this.setData({
          selected: [...selected.slice(0, currentIndex), newOption],
          originalSelected: [
            ...originalSelected.slice(0, currentIndex),
            option,
          ],
        })
        this.triggerEvent("finish", {
          value: option,
          selectedOptions: this.data.selected,
        })
      }
      this.triggerEvent("change", {
        value: option,
        selectedOptions: this.data.selected,
      })
      this.loadCurrentOptions()
    },

    loadCurrentOptions: function () {
      const { originalSelected, currentIndex, options } = this.data
      const currentOptions =
        currentIndex === 0
          ? options
          : originalSelected[currentIndex - 1]?.child || []
      this.setData({ currentOptions })
    },
    close() {
      console.log("1")
      this.triggerEvent("close", {})
    },
  },
})
