page {
  box-sizing: border-box;
  background: rgba(242, 244, 247, 1);
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/common/banner_bg.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-bottom: 64rpx;
}
.main-content {
  padding: 5rpx 32rpx;
}

.rule-box {
  box-sizing: border-box;
  margin-top: 15rpx;
  background: rgba(255, 255, 255, 1);
  padding: 60rpx 40rpx 48rpx 40rpx;
  border-radius: 24rpx;
  position: relative;
  .title {
    font-size: 32rpx;
    color: rgba(60, 61, 66, 1);
    line-height: 48rpx;
    padding-left: 32rpx;
  }
  .sub-title{
    font-size: 24rpx;
    color: #919499;
    line-height: 1.5;
    padding-left: 32rpx;
    margin-top: 10rpx;
  }
  .rule-comma {
    width: 72rpx;
    height: 72rpx;
    position: absolute;
    top: 20rpx;
  }
  .mt24 {
    margin-top: 24rpx !important;
  }
  .face-time {
    background: rgba(245, 246, 247, 0.5);
    padding: 32rpx;
    margin-top: 48rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text {
      font-size: 28rpx;
      color: rgba(60, 61, 66, 1);
    }
    .time {
      font-size: 24rpx;
      color: #e60000;
      text {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }

  .question-setting {
    font-size: 24rpx;
    color: rgba(194, 197, 204, 1);
    .question-setting-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .titles {
      margin-top: 32rpx;
      margin-bottom: 24rpx;
    }
    .question-setting-item {
      width: 292rpx;
      height: 158rpx;
      border-radius: 24rpx;
      background: rgba(245, 246, 247, 0.5);
      padding: 24rpx 40rpx;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      .select-bg {
        display: none;
      }
      .icon-select {
        display: none;
      }
      .icon {
        width: 48rpx;
        height: 48rpx;
      }
      text {
        font-size: 28rpx;
        color: rgba(60, 61, 66, 1);
        margin-top: 32rpx;
        display: block;
        position: relative;
        z-index: 2;
      }
      .select {
        display: none;
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        top: 16rpx;
        right: 16rpx;
      }
      &.active {
        background: linear-gradient(180deg, #e60000 0%, #ff7b53 100%);
        .icon-select {
          display: block;
          width: 48rpx;
          height: 48rpx;
        }
        .icon {
          display: none;
        }
        text {
          color: #fff;
        }
        .select {
          display: block;
        }
        .select-bg {
          display: block;
          position: absolute;
          width: 158rpx;
          height: 158rpx;
          top: -10rpx;
          right: -20rpx;
        }
      }
    }
  }
  .answering-process {
    margin-top: 64rpx;
    .answering-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #3c3d42;
    }
    .answering-process-box {
      margin-top: 32rpx;
      .process-item {
        position: relative;
        padding-bottom: 32rpx;
        &:last-child::after {
          display: none;
        }
        .yuan {
          width: 14rpx;
          height: 14rpx;
          border: 4rpx solid #e60000;
          border-radius: 50%;
          position: absolute;
          left: 0;
          top: 32rpx;
          background: #fff;
          z-index: 2;
        }
        padding-left: 38rpx;
        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 1px;
          height: 100%;
          border-left: 1px dashed rgba(#e60000, 0.2);
          left: 10rpx;
          top: 32rpx;
          z-index: 1;
        }
        .right-box {
          flex: 1;
          background: rgba(245, 246, 247, 0.5);
          padding: 24rpx;
          border-radius: 16rpx;
          .titles {
            font-size: 28rpx;
            color: #3c3d42;
          }
          .label {
            font-size: 24rpx;
            color: #919499;
            margin-top: 14rpx;
          }
        }
      }
    }
  }
  .text-box {
    .text-title {
      font-size: 32rpx;
      color: rgba(60, 61, 66, 1);
      font-weight: bold;
      margin-bottom: 20rpx;
      margin-top: 32rpx;
    }
    text {
      display: block;
      font-size: 26rpx;
      color: #3c3d42;
      line-height: 46rpx;
    }
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar .button {
  flex: 1;
  height: 100%;
  background-color: var(--main-color);
  font-size: 30rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-collect {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  &.blue {
    text {
      color: #e60000;
    }
  }
  text {
    font-size: 20rpx;
    color: rgba(145, 148, 153, 1);
  }
  image {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
  }
}
