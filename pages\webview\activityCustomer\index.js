const UTIL = require("@/utils/util")
const API = require("@/config/api")
const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageState: 0, //页面状态 0加载中 1参数异常or活动过期 2正常
    images: [],
    webURL: "",
    share_info: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log("j进啦没饿")
    let event = options.event
    let codeQuery = decodeURIComponent(options.q)

    if (codeQuery !== "undefined") {
      let i = this.getUrlParam(this.parseQueryString(codeQuery), "event")
      i && (event = i)
    }

    // 参数异常
    if (!event) {
      this.setData({
        pageState: 1,
      })
      return false
    }
    // ["http://h5.test.camps.shikaobang.cn/static/imgs/img_71.png"]
    this.requestGetActivityCustomer(event)
  },
  // 获取活动客服界面
  requestGetActivityCustomer(event) {
    console.log("进来了啊实打实懂撒打湿")
    UTIL.request(API.getGuideInfo, {
      event,
    }).then((res) => {
      this.setData({
        pageState: res?.data ? 2 : 1,
        images: [res?.data?.img],
        webURL: res?.data?.url,
        share_info: res.data.share_info,
      })
      // if (this.data.images.length > 0) {
      //   this.previewImage(this.data.images)
      // }
    })
  },
  // 预览图片
  previewImage(images) {
    wx.previewImage({
      current: images[0], // 当前显示图片的http链接
      urls: images,
    })
  },
  clickImage(e) {
    this.previewImage([e.currentTarget.dataset.src])
  },
  parseQueryString(url) {
    try {
      var str = url.split("?")[1],
        items = str.split("&")
      var arr, name, value
    } catch (error) {
      return ""
    }

    for (var i = 0, l = items.length; i < l; i++) {
      arr = items[i].split("=")
      name = arr[0]
      value = arr[1]
      this[name] = value
    }
    return str
  },
  getUrlParam(url, name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)") //构造一个含有目标参数的正则表达式对象
    var r = url.match(reg) //匹配目标参数
    if (r != null) return unescape(r[2])
    return null //返回参数值
  },
  getPageShareParams() {
    let query = {}
    return APP.createShareParams({
      title: this.data.share_info.title || "",
      imageUrl: this.data.share_info.image || "",
      path: "/pages/webview/activityCustomer/index",
      query: query,
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.getPageShareParams()
  },
})
