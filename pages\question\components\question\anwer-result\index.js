Component({
  /**
   * 组件的属性列表
   */
  properties: {
    answerState: { type: Object, value: {} },
    pageState: { type: Object, value: {} },
    mediaInfo: {},
  },

  /**
   * 组件的初始数据
   */
  data: {
    videoPoster: "", // 用于存储视频首帧图片
  },

  lifetimes: {
    attached() {
      wx.createVideoContext("resultVideo", this).play()
      wx.createVideoContext("resultVideo", this).pause()
    },
    detached() {
      this.pause()
    },
  },
  data: { hasPlay: false },

  /**
   * 组件的方法列表
   */
  methods: {
    submit() {
      console.log("ddd")
      this.triggerEvent("onSubmit", {}) // 触发事件通知父组件
    },
    replay() {
      this.triggerEvent("onRetry", {}) // 触发事件通知父组件
    },
    pause() {
      console.log("触发暂停方法")

      try {
        const audioComponent = this.selectComponent(`#audio-answer`)
        audioComponent.pause()
        console.log("触发暂停方法1")
      } catch (error) {
        console.log(error)
      }

      try {
        wx.createVideoContext("resultVideo", this).pause()
        console.log("触发暂停方法2")
      } catch (error) {
        console.log(error)
      }
    },

    onBackgroundVideoTimeupdate(e) {
      if (!this.data.hasPlay) {
        this.setData({ hasPlay: true })
        // wx.createVideoContext("resultVideo", this).pause()
      }
    },
  },
})
