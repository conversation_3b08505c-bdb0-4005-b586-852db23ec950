/* pages/entry/index.wxss */
.main-content {
  .main-title {
    font-size: 48rpx;
    font-weight: bold;
    color: rgba(34, 36, 46, 1);
    padding: 40rpx;
  }
  .exam-direction {
    padding: 0 40rpx;
    margin-top: 45rpx;
    .title {
      font-size: 32rpx;
      color: #22242e;
      font-weight: 500;
      margin-bottom: 40rpx;
    }
  }
}

.mt32 {
  margin-top: 32rpx;
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar-box-ones {
  display: flex;
  z-index: 998;
}

.action-bar .button {
  width: 100%;
  height: 100%;
  background-color: var(--main-color);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &.hui {
    background: rgba(230, 0, 0, 0.5);
  }
}

.action-bar .action-text {
  width: 290rpx;
  font-size: 26rpx;
  color: #6c6c6c;
}

.flex-justify_between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
